<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.fa.marketing.cms.dao.FbQuotationDAO">

    <resultMap type="com.yhd.fa.marketing.cms.pojo.po.FbQuotationPO" id="FbQuotationMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="quotationNumber" column="quotation_number" jdbcType="VARCHAR"/>
        <result property="quotationLevel" column="quotation_level" jdbcType="VARCHAR"/>
        <result property="customerQuotationNumber" column="customer_quotation_number" jdbcType="VARCHAR"/>
        <result property="companyCode" column="company_code" jdbcType="VARCHAR"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="userCode" column="user_code" jdbcType="VARCHAR"/>
        <result property="purchaseUserCode" column="purchase_user_code" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="quotationCompletionDate" column="quotation_completion_date" jdbcType="TIMESTAMP"/>
        <result property="quotationUserCode" column="quotation_user_code" jdbcType="VARCHAR"/>
        <result property="quotationStatus" column="quotation_status" jdbcType="VARCHAR"/>
        <result property="totalPrice" column="total_price" jdbcType="NUMERIC"/>
        <result property="payablePrice" column="payable_price" jdbcType="NUMERIC"/>
        <result property="saleQuotationNumber" column="sale_quotation_number" jdbcType="VARCHAR"/>
        <result property="synchronizationStatus" column="synchronization_status" jdbcType="VARCHAR"/>
        <result property="synchronizationDate" column="synchronization_date" jdbcType="TIMESTAMP"/>
        <result property="synchronizationMessage" column="synchronization_message" jdbcType="VARCHAR"/>
        <result property="examineStatus" column="examine_status" jdbcType="VARCHAR"/>
        <result property="deleteStatus" column="delete_status" jdbcType="VARCHAR"/>
        <result property="deleteDate" column="delete_date" jdbcType="TIMESTAMP"/>
        <result property="transferOrder" column="transfer_order" jdbcType="VARCHAR"/>
        <result property="pushUser" column="push_user" jdbcType="VARCHAR"/>
        <result property="pushSalesman" column="push_salesman" jdbcType="VARCHAR"/>
        <result property="invalidReason" column="invalid_reason" jdbcType="VARCHAR"/>
        <result property="channelType" column="channel_type" jdbcType="VARCHAR"/>
        <result property="isInsideCreated" column="is_inside_created" jdbcType="VARCHAR"/>
        <result property="insideEmployeeCode" column="inside_employee_code" jdbcType="VARCHAR"/>
        <result property="employeeName" column="employee_name" jdbcType="VARCHAR"/>
        <result property="platformCode" column="platform_code" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="base_column">
        `id`
        `quotation_number`
		`quotation_level`
		`customer_quotation_number`
		`company_code`
		`company_name`
		`user_code`
		`purchase_user_code`
		`remark`
		`quotation_completion_date`
		`quotation_user_code`
		`quotation_status`
		`total_price`
		`payable_price`
		`sale_quotation_number`
		`synchronization_status`
		`synchronization_date`
		`synchronization_message`
		`examine_status`
		`delete_status`
		`delete_date`
		`transfer_order`
		`push_user`
		`push_salesman`
		`invalid_reason`
		`channel_type`
		`is_inside_created`
		`inside_employee_code`
		`employee_name`
		`platform_code`
		`created_by`
		`created_date`
		`updated_by`
		`updated_date`
    </sql>

    <sql id="table_name">
        `fb_quotation`
    </sql>

    <select id="selectFbQuotationPage"
            resultType="com.yhd.fa.marketing.cms.pojo.vo.response.FbQuotationListResponseVO"
            parameterType="com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationPageRequestVO">
        select id as id,
        quotation_number as quotationNumber,
        quotation_level as quotationLevel,
        customer_quotation_number as customerQuotationNumber,
        company_code as companyCode,
        company_name as companyName,
        quotation_status as quotationStatus,
        user_code as userCode,
        purchase_user_code as purchaseUserCode,
        remark as remark,
        quotation_completion_date as quotationCompletionDate,
        quotation_user_code as quotationUserCode,
        total_price as totalPrice,
        transfer_order as transferOrder,
        created_date as createdDate,
        unit_name as unitName,
        ownership_company as ownershipCompany,
        operator as operator,
        merchandiser as merchandiser
        from
        <include refid="table_name"/>
        <where>

            <if test="quotationNumber != null and quotationNumber != ''">
                and quotation_number like #{quotationNumber}
            </if>
            <if test="quotationStatus != null and quotationStatus != ''">
                and quotation_status =#{quotationStatus}
            </if>
            <if test="companyName != null and companyName != ''">
                and company_name like #{companyName}
            </if>
            <if test="transferOrder != null and transferOrder != ''">
                and transfer_order =#{transferOrder}
            </if>
            <if test="ownershipCompany != null and ownershipCompany != ''">
                and ownership_company =#{ownershipCompany}
            </if>
            <if test="unitName != null and unitName != ''">
                and unit_name like #{unitName}
            </if>
            <if test="startDate != null and startDate != ''">
                and created_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and created_date &lt;= #{endDate}
            </if>
            <if test="quotationAgeing != null and quotationAgeing != ''">
                and timestampdiff(hour,created_date,now())>#{quotationAgeing}
                and quotation_status = "quotation"
            </if>
            <if test="quotationIdList !=null and quotationIdList.size() !=0">
                and id in
                <foreach collection="quotationIdList" item="item" open="(" separator="," close=")">
                    (
                    #{item}
                    )
                </foreach>
            </if>
            <if test="companyCodeList !=null and companyCodeList.size() !=0">
                and company_code in
                <foreach collection="companyCodeList" item="item" open="(" separator="," close=")">
                    (
                    #{item}
                    )
                </foreach>
            </if>
            and delete_status ='normal'
        </where>
        <if test="orderByField !=null and orderByField!=''">
            order by ${orderByField} ${orderByType}
        </if>
    </select>

    <!--  部分报价单的总金额-->
    <select id="selectTotalPricePart" resultType="com.yhd.fa.marketing.cms.pojo.po.FbQuotationPO"
            parameterType="com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationPageRequestVO">
        select
        sum(total_price) as totalPricePart
        from
        <include refid="table_name"/>
        <where>

            <if test="quotationNumber != null and quotationNumber != ''">
                and quotation_number like #{quotationNumber}
            </if>
            <if test="quotationStatus != null and quotationStatus != ''">
                and quotation_status =#{quotationStatus}
            </if>
            <if test="companyName != null and companyName != ''">
                and company_name like #{companyName}
            </if>
            <if test="transferOrder != null and transferOrder != ''">
                and transfer_order =#{transferOrder}
            </if>
            <if test="ownershipCompany != null and ownershipCompany != ''">
                and ownership_company =#{ownershipCompany}
            </if>
            <if test="unitName != null and unitName != ''">
                and unit_name like #{unitName}
            </if>
            <if test="startDate != null and startDate != ''">
                and created_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and created_date &lt;= #{endDate}
            </if>
            <if test="quotationAgeing != null and quotationAgeing != ''">
                and timestampdiff(hour,created_date,now())>#{quotationAgeing}
                and quotation_status = "quotation"
            </if>
            and delete_status ='normal'
            <if test="companyCodeList !=null and companyCodeList.size() !=0">
                and company_code in
                <foreach collection="companyCodeList" item="item" open="(" separator="," close=")">
                    (
                    #{item}
                    )
                </foreach>
            </if>
        </where>
        <if test="orderByField !=null and orderByField!=''">
            order by ${orderByField} ${orderByType}
        </if>
    </select>

</mapper>

