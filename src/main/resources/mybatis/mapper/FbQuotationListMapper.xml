<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.fa.marketing.cms.dao.FbQuotationListDAO">

    <resultMap type="com.yhd.fa.marketing.cms.pojo.po.FbQuotationListPO" id="FbQuotationListMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="quotationId" column="quotation_id" jdbcType="VARCHAR"/>
        <result property="quotationNumber" column="quotation_number" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="VARCHAR"/>
        <result property="model" column="model" jdbcType="VARCHAR"/>
        <result property="customerModel" column="customer_model" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="quantity" column="quantity" jdbcType="VARCHAR"/>
        <result property="originalPrice" column="original_price" jdbcType="VARCHAR"/>
        <result property="discountPrice" column="discount_price" jdbcType="VARCHAR"/>
        <result property="taxDiscountPrice" column="tax_discount_price" jdbcType="VARCHAR"/>
        <result property="totalPrice" column="total_price" jdbcType="VARCHAR"/>
        <result property="quantityDiscountRate" column="quantity_discount_rate" jdbcType="VARCHAR"/>
        <result property="totalDiscountRate" column="total_discount_rate" jdbcType="VARCHAR"/>
        <result property="delivery" column="delivery" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="customerMaterialCode" column="customer_material_code" jdbcType="VARCHAR"/>
        <result property="customerProductName" column="customer_product_name" jdbcType="VARCHAR"/>
        <result property="modelLong" column="model_long" jdbcType="VARCHAR"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
        <result property="quotationStatus" column="quotation_status" jdbcType="VARCHAR"/>
        <result property="confirmationStatus" column="confirmation_status" jdbcType="VARCHAR"/>
        <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="examineRemark" column="examine_remark" jdbcType="VARCHAR"/>
        <result property="insideFileUrl" column="inside_file_url" jdbcType="VARCHAR"/>
        <result property="quotationType" column="quotation_type" jdbcType="VARCHAR"/>
        <result property="quotationUpdatedBy" column="quotation_updated_by" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="base_column">
        `id`
        `quotation_id`
		`quotation_number`
		`sort`
		`model`
		`customer_model`
		`product_code`
		`product_name`
		`quantity`
		`original_price`
		`discount_price`
		`tax_discount_price`
		`total_price`
		`quantity_discount_rate`
		`total_discount_rate`
		`delivery`
		`remark`
		`customer_material_code`
		`customer_product_name`
		`model_long`
		`unit`
		`material_code`
		`quotation_status`
        `confirmation_status`
		`error_message`
		`file_url`
		`examine_remark`
		`inside_file_url`
		`quotation_type`
		`quotation_updated_by`
		`image_url`
		`created_by`
		`created_date`
		`updated_by`
		`updated_date`
    </sql>

    <sql id="table_name">
        `fb_quotation_list`
    </sql>


</mapper>

