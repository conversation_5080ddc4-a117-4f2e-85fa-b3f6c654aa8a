<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.fa.marketing.cms.dao.OrderDAO">


    <sql id="table_name">
        `fa_order`
    </sql>

    <select id="getLastOrderDate" resultType="com.yhd.fa.marketing.cms.pojo.vo.response.LastOrderDateResponseVO"
            parameterType="list">
        select max(created_date) as lastOrderDate,user_code from
        <include refid="table_name"/>
        where user_code in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (
            #{item}
            )
        </foreach>
        group by user_code

    </select>

</mapper>

