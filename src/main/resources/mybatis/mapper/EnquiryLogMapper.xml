<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.fa.marketing.cms.dao.EnquiryLogDAO">


    <insert id="insertBatch" parameterType="java.util.List">
        insert into

        enquiry_log
        (  `id`,
        `model`,
        `code`,
        `product_name`,
        `quantity`,
        `cost_price`,
        `discount_price`,
        `tax_discount_price`,
        `tax_total_price`,
        `total_discount`,
        `delivery`,
        `user_code`,
        `user_name`,
        `user_phone`,
        `user_email`,
        `user_level`,
        `company_code`,
        `company_name`,
        `merchandiser`,
        `salesman`,
        `customer_grade`,
        `company_level`,
        `enquiry_status`,
        `error_msg`,
        `is_standard`,
        `ip`,
        `register_date`,
        `user_type`,
        `occupation`,
        `ip_region`,
        `created_day`,
        `created_date`,
        `created_by`,
        `updated_date`,
        `updated_by`)values
        <foreach collection="list" item="item"
                 separator="," index="index">
            (
            #{item.id},
            #{item.model},
            #{item.code},
            #{item.productName},
            #{item.quantity},
            #{item.costPrice},
            #{item.discountPrice},
            #{item.taxDiscountPrice},
            #{item.taxTotalPrice},
            #{item.totalDiscount},
            #{item.delivery},
            #{item.userCode},
            #{item.userName},
            #{item.userPhone},
            #{item.userEmail},
            #{item.userLevel},
            #{item.companyCode},
            #{item.companyName},
            #{item.merchandiser},
            #{item.salesman},
            #{item.customerGrade},
            #{item.companyLevel},
            #{item.enquiryStatus},
            #{item.errorMsg},
            #{item.isStandard},
            #{item.ip},
            #{item.registerDate},
            #{item.userType},
            #{item.occupation},
            #{item.ipRegion},
            #{item.createdDay},
            #{item.createdDate},
            #{item.createdBy},
            #{item.updatedDate},
            #{item.updatedBy}
            )
        </foreach>
    </insert>
</mapper>

