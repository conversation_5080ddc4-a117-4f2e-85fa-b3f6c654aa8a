server:
  port: 9478

spring:
  profiles:
    active: @@base.properties.active@@
  application:
    name: @@artifactId@@
  cloud:
    nacos:
      discovery:
        server-addr: @@base.spring.cloud.nacos.serveraddr@@
        namespace: @@base.spring.cloud.nacos.namespace@@
        username: @@base.spring.cloud.nacos.username@@
        password: @@base.spring.cloud.nacos.password@@
        group: @@base.spring.cloud.nacos.group@@
        metadata: { 'version': '${spring.profiles.active}' }
      config:
        namespace: ${spring.cloud.nacos.discovery.namespace}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        group: ${spring.cloud.nacos.discovery.group}
        file-extension: yml
        shared-configs:
          - dataId: application.${spring.cloud.nacos.config.file-extension}
            group: ${spring.cloud.nacos.discovery.group}
          - dataId: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${spring.cloud.nacos.discovery.group}
    sentinel:
      transport:
        dashboard: @@base.spring.cloud.sentinel.transport.dashboard@@
        port: @@base.spring.cloud.sentinel.transport.port@@
  kafka:
    bootstrap-servers: @@base.spring.kafka.bootstrapServers@@
  main:
    allow-circular-references: true
project:
  artifactId: @@project.artifactId@@
  version: @@project.version@@
  description: @@project.description@@
yhd:
  doc:
    aggregation:
      group-name: 'FA商城管理后台营销系统CMS yhd-service-fa-marketing-cms'