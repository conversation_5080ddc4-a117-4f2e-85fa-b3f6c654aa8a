package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.QuotationSourceConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.CheckApproveQuotationParameterModelListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CheckApproveQuotationParameterRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationModelInfoRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;
import com.yhd.fa.marketing.cms.service.sao.ProductBusinessInfoService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.UserUtil;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: CheckCustomerModelLogic.java, v0.1 2022/12/23 10:44 yehuasheng Exp $
 */
@Component
public class CheckCustomerModelLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(CheckCustomerModelLogic.class.getName());

    /**
     * 报价与交期服务
     */
    @Resource
    private ProductBusinessInfoService productBusinessInfoService;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 设置前端排序
     *
     * @param checkApproveQuotationParameterRequestVO 参数
     * @param modelPrice                              型号价格
     */
    private static void setFrontEndSort(CheckApproveQuotationParameterRequestVO checkApproveQuotationParameterRequestVO, BusinessResponse<List<QuotationResponseVO>> modelPrice) {
        // 设置前端排序
        logger.info("set front end sort");
        Map<String, Integer> frontEndSortMap = checkApproveQuotationParameterRequestVO.getList()
                .stream()
                .filter(checkApproveQuotationParameterModelListRequestVO -> null != checkApproveQuotationParameterModelListRequestVO.getFrontEndSort())
                .collect(
                        Collectors.toMap(
                                checkApproveQuotationParameterModelListRequestVO ->
                                        checkApproveQuotationParameterModelListRequestVO.getQuantity() + CommonConstant.DASH + checkApproveQuotationParameterRequestVO.getList().indexOf(checkApproveQuotationParameterModelListRequestVO),
                                CheckApproveQuotationParameterModelListRequestVO::getFrontEndSort));
        modelPrice.getData().forEach(quotationResponseVO -> {
            // 设置前端需要序号
            if (frontEndSortMap.containsKey(quotationResponseVO.getSort())) {
                quotationResponseVO.setFrontEndSort(frontEndSortMap.get(quotationResponseVO.getSort()));
            }
        });
    }

    /**
     * 执行获取价格和交期
     *
     * @param checkApproveQuotationParameterRequestVO 检查的参数
     * @return BusinessResponse<List < QuotationResponseVO>>
     */
    public BusinessResponse<List<QuotationResponseVO>> exec(CheckApproveQuotationParameterRequestVO checkApproveQuotationParameterRequestVO) {
        logger.info("start exec check customer model logic.");

        // 获取用户信息
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(checkApproveQuotationParameterRequestVO.getUserCode(), null);
        if (ObjectUtil.isEmpty(userInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.USER_INFO_IS_EMPTY);
        }

        // 用户获取价格和交期的级别
        int userEnquiryRole = UserUtil.getUserEnquiryRole(userInfo);

        // 设置请求参数
        List<QuotationModelInfoRequestVO> quotationModelInfoRequestVOS = checkApproveQuotationParameterRequestVO.getList()
                .stream()
                .map(checkApproveQuotationParameterModelListRequestVO ->
                        QuotationModelInfoRequestVO
                                .builder()
                                .model(checkApproveQuotationParameterModelListRequestVO.getModel())
                                .quantity(checkApproveQuotationParameterModelListRequestVO.getQuantity())
                                .build())
                .collect(Collectors.toList());
        QuotationRequestVO quotationRequestVO = QuotationRequestVO.builder()
                .companyCode(userInfo.getCompanyInfo().getErpCompanyCode())
                .source(QuotationSourceConstant.ONLINE)
                .userRole(userEnquiryRole)
                .list(quotationModelInfoRequestVOS)
                .build();

        // 获取价格和交期
        BusinessResponse<List<QuotationResponseVO>> modelPrice = productBusinessInfoService.getModelPrice(quotationRequestVO);
        if (!modelPrice.success()) {
            return modelPrice;
        }

        // 设置前端排序
        if (checkApproveQuotationParameterRequestVO.getList()
                .stream()
                .anyMatch(checkApproveQuotationParameterModelListRequestVO ->
                        null != checkApproveQuotationParameterModelListRequestVO.getFrontEndSort())) {
            setFrontEndSort(checkApproveQuotationParameterRequestVO, modelPrice);
        }

        return modelPrice;
    }
}
