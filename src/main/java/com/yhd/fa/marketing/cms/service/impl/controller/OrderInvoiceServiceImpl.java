package com.yhd.fa.marketing.cms.service.impl.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderInvoiceRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderInvoiceService;
import com.yhd.fa.marketing.cms.service.impl.logic.order.GetOrderInvoiceInfoLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.order.GetOrderInvoiceListLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.order.SynchronizationOrderInvoiceLogic;
import com.yhd.fa.marketing.cms.service.impl.verification.order.GetOrderInvoiceListVerification;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceServiceImpl.java, v0.1 2023/2/23 10:07 yehuasheng Exp $
 */
@Service
public class OrderInvoiceServiceImpl implements OrderInvoiceService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderInvoiceServiceImpl.class.getName());

    /**
     * 订单发票列表校验
     */
    @Resource
    private GetOrderInvoiceListVerification getOrderInvoiceListVerification;

    /**
     * 订单发票逻辑
     */
    @Resource
    private GetOrderInvoiceListLogic getOrderInvoiceListLogic;

    /**
     * 订单发票详情逻辑
     */
    @Resource
    private GetOrderInvoiceInfoLogic getOrderInvoiceInfoLogic;

    /**
     * 同步订单发票逻辑
     */
    @Resource
    private SynchronizationOrderInvoiceLogic synchronizationOrderInvoiceLogic;

    /**
     * 获取订单发票列表
     *
     * @param orderInvoiceRequestVO 请求订单发票参数
     * @return BusinessResponse<PageInfo < OrderInvoiceResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<OrderInvoiceResponseVO>> getOrderInvoiceList(OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("start get order invoice list service.");

        // 校验查询参数
        BusinessResponse<PageInfo<OrderInvoiceResponseVO>> checkParameter = getOrderInvoiceListVerification.check(orderInvoiceRequestVO);
        if (!checkParameter.success()) {
            return checkParameter;
        }

        // 执行逻辑
        return getOrderInvoiceListLogic.exec(orderInvoiceRequestVO);
    }

    /**
     * 获取订单发票详情
     *
     * @param orderInvoiceId 订单发票id
     * @return BusinessResponse<OrderInvoiceDetailResponseVO>
     */
    @Override
    public BusinessResponse<OrderInvoiceDetailResponseVO> getOrderInvoiceInfo(String orderInvoiceId) {
        logger.info("start get order invoice info service.");

        // 执行逻辑
        return getOrderInvoiceInfoLogic.exec(orderInvoiceId);
    }

    /**
     * 同步订单发票
     *
     * @param orderInvoiceId 订单发票id
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<Void> synchronizationOrderInvoice(String orderInvoiceId) {
        logger.info("start synchronization order invoice info.");

        // 执行逻辑
        return synchronizationOrderInvoiceLogic.exec(orderInvoiceId);
    }
}
