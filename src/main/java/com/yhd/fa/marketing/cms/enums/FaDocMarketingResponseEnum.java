package com.yhd.fa.marketing.cms.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: FaDocMarketingResponseEnum.java, v0.1 2022/12/2 14:10 yehuasheng Exp $
 */
@Getter
public enum FaDocMarketingResponseEnum {
    SUCCESS(0, "成功", "success"),
    QUOTATION_ID_IS_EMPTY(597351, "请选择报价单", "quotation id is null"),
    QUOTATION_IS_EMPTY(597352, "报价单不存在", "quotation is not exists"),
    QUOTATION_DETAIL_IS_EMPTY(597353, "报价单明细不存在", "quotation detail is not exists"),
    QUOTATION_IS_NOT_NEED_REVIEWED(597354, "报价单已审核或无需审核", "quotation is not need reviewed"),
    QUOTATION_IS_DELETE_NOT_NEED_REVIEWED(597355, "报价单已删除或已加入回收站，无需审核报价单", "quotation is delete not need reviewed"),
    QUOTATION_DETAIL_IS_NULL(597356, "报价单明细为空！", "quotation detail is null"),
    USER_INFO_IS_EMPTY(597357, "用户信息为空或者用户不存在！", "user info is null"),
    REASONS_REFUSAL_IS_EMPTY(597358, "拒绝理由不能为空！", "reasons refusal is null"),
    APPROVE_QUOTATION_DETAIL_IS_EMPTY(597359, "审核报价单明细不能为空！", "approve quotation detail is empty"),
    APPROVE_QUOTATION_SAVE_FAIL(597360, "审核报价单保存失败！", "approve quotation save fail."),
    APPROVE_QUOTATION_GET_PRICE_DELIVERY_FAIL(597361, "报价单获取价格和交期失败！", "approve quotation get price and delivery fail."),
    USER_IS_EMPTY(597362, "用户不存在或已禁用！", "user is empty."),
    QUOTATION_CUSTOMER_QUOTATION_ID_IS_TOO_LONG(597363, "内部采购单号过长", "quotation customer quotation id is too length."),
    QUANTITY_LESS_ONE(597364, "数量不能少于1", "quantity less than one."),
    QUOTATION_MODEL_IS_NULL(597365, "创建报价单失败，型号不能为空", "quotation model is null."),
    QUOTATION_MODEL_IS_ERROR(597366, "型号{}格式错误，请输入正确的型号", "created quotation fail parameter model format is error. customerModel:{}."),
    QUOTATION_MATERIAL_CODE_IS_TOO_LONG(597367, "您输入的物料编码：{} 过长，请输入少于255个字符。", "created quotation fail material code is too long. customerModel:{}."),
    INSERT_QUOTATION_FAIL(597368, "创建报价单失败!", "insert quotation fail."),
    START_DATE_IS_AFTER_END_TIME_ERROR(597369, "结束时间不能少于开始时间!", "start date time is after end date time."),
    ORDER_IS_NOT_EXISTS(597370, "订单不存在!", "order is not exists."),
    ORDER_CANCEL_IS_NOT_EXISTS(597371, "订单取消记录不存在!", "order cancel is not exists."),
    ORDER_INVOICE_IS_NOT_EXISTS(597372, "订单发票记录不存在!", "order invoice is not exists."),
    ORDER_AFTER_SALE_IS_NOT_EXISTS(597373, "订单售后记录不存在!", "order after sale is not exists."),
    ORDER_COMMENTS_RATING_CANNOT_IS_VALID(597374, "订单评论分数输入有误!", "order comment score input error!"),
    ORDER_COMMENTS_IS_NOT_EXISTS(597375, "订单评论记录不存在!", "order comments is not exists"),
    ORDER_DETAIL_IS_NOT_EXISTS(597376, "订单明细不存在!", "order detail is not exists"),
    ORDER_CANCEL_DETAIL_IS_NOT_EXISTS(597377, "订单取消明细不存在!", "order cancel detail is not exists"),
    CANCEL_ORDER_ERROR_ORDER(597378, "订单状态不支持申请取消", "Order status does not support cancellation request"),
    CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_CANCEL(597379, "订单明细序号{}已取消不能申请取消", "confirm order detail sort {} is cancel"),
    CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_FINISH(597380, "订单明细序号{}已完成,不能取消", "cancel order detail sort {} is finish"),
    CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_STOCK_UP(597381, "订单明细序号{}正在备货中,不能申请取消", "cancel order detail sort {} is stockUp"),
    CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_TAKE_DELIVERED(597382, "订单明细序号{}已发货,不能申请取消", "cancel order detail sort {} is takeDelivered"),
    CANCEL_ORDER_ERROR_ORDER_SORT(597383, "含有非法明细选项", "Contains illegal detail options"),
    CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_CANCELLING(597384, "订单明细序号{}正在取消中不能申请取消", "take delivery order detail sort {} is cancelling"),
    ORDER_INVOICE_DETAIL_IS_NOT_EXISTS(597385, "订单发票明细记录不存在!", "order invoice detail is not exists."),
    ORDER_AFTER_SALE_DETAIL_IS_NOT_EXISTS(597386, "订单售后明细记录不存在!", "order after sale detail is not exists."),
    ORDER_COMMENTS_INFO_NOT_EXIST(597387, "订单评价记录不存在!", "The order review record does not exist."),
    ORDER_COMMENTS_UPDATE_FAIL(597388, "更新订单评价失败. 请重试!", "Failed to update order review. Please try again!"),
    ORDER_COMMENTS_REPLIED(597388, "订单评价已回复,请勿重复操作!", "The order evaluation has been replied, please do not repeat the operation!"),
    SEND_DING_DING_MESSAGE_FAIL(597389, "发送钉钉内容失败.", "send ding ding message fail."),
    SEND_EMAIL_MESSAGE_FAIL(597390, "发送电子邮箱内容失败.", "send email message fail."),
    SEND_SMS_MESSAGE_FAIL(597391, "发送短信内容失败.", "send sms message fail."),
    ORDER_COLLECTION_LOG_IS_NOT_EXIST(597392, "订单收款单不存在.", "order collection is not exist."),
    RESET_SYNCHRONIZATION_ORDER_COLLECTION_LOG_FAIL(597392, "重置同步订单收款单失败.", "reset synchronization order collection fail."),
    ORDER_SERIAL_NUMBER_IS_NULL(597393, "订单流水号不能为空.", "order serial number is null."),
    ORDER_PAYMENT_PRICE_IS_NULL(597394, "订单支付金额不能为空.", "order payment price is null."),
    ORDER_IS_CANCEL(597395, "订单已取消无法更换支付方式或者流水号.", "order is cancel."),
    ORDER_SETTLEMENT_TYPE_IS_OFFLINE(597396, "订单结算方式是月结方式无法更换支付方式或者流水号.", "order is settlement type is offline."),
    ORDER_PAYMENT_PRICE_ERROR(597397, "订单应付金额不正确无法更换支付方式或者流水号.", "order payment price error."),
    UPDATE_ORDER_PAYMENT_INFO_FAIL(597398, "更新订单支付信息以及流水号失败.", "update order payment info fail."),
    INSERT_ORDER_COLLECTION_LOG_FAIL(597399, "添加收款单记录失败.", "insert order collection fail."),
    COMPANY_NAME_IS_NULL(597400, "企业名称不能为空！", "company name is null."),
    ENTERPRISE_NAME_CANNOT_BE_LESS_THAN_THREE(597400, "企业名称不能少于3位！", "enterprise names cannot be less than 3."),
    USER_LEVEL_CANNOT_CREATE_QUOTATION(597401, "该用户等级不能创建报价单！", "user level cannot create quotation"),
    USER_NOT_CREATE_ORDER_AUTH_AND_PURCHASE_USER_IS_NULL(597402, "该用户没有采购权限，采购员不能为空！", "user not create order auth and purchase user is null!"),
    COMPANY_CANNOT_ENQUIRY_PRICE_AUTHORITY(597403, "该企业没有询价权限！", "company cannot enquiry price authority!"),
    COMPANY_CANNOT_CREATE_QUOTATION_AUTHORITY(597404, "该企业没有创建报价单权限！", "company cannot create quotation authority!"),
    ORDER_IS_NOT_PAY(597405, "订单未支付,不能同步！", "The order is not paid and cannot be synchronized!"),
    AFF_OLD_QUOTATION_DETAIL_FAIL(597406, "添加旧报价单明细失败！", "add old quotation detail fail!"),
    GET_ORIGINAL_COMPANY_CODE_FAIL(597407, "获取订单原企业ERP编码失败！", "Failed to get the original enterprise ERP code of the order!"),
    PARAM_ERROR(597408, "参数异常！", "param error"),
    ;

    private final int code;
    private final String desc;
    private final String descEn;

    FaDocMarketingResponseEnum(int code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }
}
