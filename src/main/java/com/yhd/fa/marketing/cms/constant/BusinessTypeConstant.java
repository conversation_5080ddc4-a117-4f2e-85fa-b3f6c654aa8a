package com.yhd.fa.marketing.cms.constant;

/**
 * <AUTHOR>
 * @version Id: BusinessTypeConstant.java, v0.1 2023/1/11 10:58 yehuasheng Exp $
 */
public class BusinessTypeConstant {
    /**
     * 同步订单状态
     */
    public static final String ORDER_STATUS = "orderStatus";
    /**
     * 同步订单取消
     */
    public static final String ORDER_CANCEL = "orderCancel";
    /**
     * 同步订单售后
     */
    public static final String ORDER_AFTER_SALES = "afterSales";
    /**
     * 同步订单物流信息
     */
    public static final String ORDER_LOGISTICS = "orderLogistics";
    /**
     * 同步订单退款回传
     */
    public static final String ORDER_REFUND = "orderRefund";
    /**
     * 同步订单发票
     */
    public static final String ORDER_INVOICE = "orderInvoice";
    /**
     * 同步报价单信息
     */
    public static final String QUOTATION = "quotation";
    /**
     * 同步创建订单
     */
    public static final String CREATE_ORDER = "createOrder";
    /**
     * 同步创建报价单
     */
    public static final String CREATE_QUOTATION = "createQuotation";
    /**
     * 同步发票资料
     */
    public static final String ORDER_INVOICE_DATA = "orderInvoiceData";

    private BusinessTypeConstant() {
    }
}
