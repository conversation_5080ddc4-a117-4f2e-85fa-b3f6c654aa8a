package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.BusinessTypeConstant;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.SynchronizeStatusEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCancelListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderCancelMapper;
import com.yhd.fa.marketing.cms.mq.producer.SynchronizationProducer;
import com.yhd.fa.marketing.cms.pojo.dto.FaOrderCancelDetailSynchronizationOfflineDTO;
import com.yhd.fa.marketing.cms.pojo.dto.FaOrderCancelSynchronizationOfflineDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCancelListPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCancelPO;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SynchronizationOrderCancelLogic.java, v0.1 2023/2/27 17:32 yehuasheng Exp $
 */
@Component
public class SynchronizationOrderCancelLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(SynchronizationOrderCancelLogic.class.getName());

    /**
     * 订单取消mapper
     */
    @Resource
    private OrderCancelMapper orderCancelMapper;

    /**
     * 订单明细mapper
     */
    @Resource
    private OrderCancelListMapper orderCancelListMapper;

    /**
     * 同步生产者
     */
    @Resource
    private SynchronizationProducer synchronizationProducer;

    /**
     * 执行订单取消同步
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<Void> exec(String orderCancelId) {
        logger.info("start exec synchronization order cancel logic.");

        // 获取订单取消详情
        OrderCancelPO orderCancelInfo = orderCancelMapper.getOne(new MPJLambdaWrapper<OrderCancelPO>()
                .selectAll(OrderCancelPO.class)
                .eq(OrderCancelPO::getId, orderCancelId)
                .last(FaDocMarketingCmsConstant.LIMIT));
        if (ObjectUtil.isNull(orderCancelInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_CANCEL_IS_NOT_EXISTS);
        }

        // 获取订单明细
        List<OrderCancelListPO> orderCancelList = orderCancelListMapper.list(new MPJLambdaWrapper<OrderCancelListPO>()
                .selectAll(OrderCancelListPO.class)
                .eq(OrderCancelListPO::getCancelId, orderCancelId));
        if (CollUtil.isEmpty(orderCancelList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_CANCEL_DETAIL_IS_NOT_EXISTS);
        }

        // 执行同步
        synchronizationOrderCancel(orderCancelInfo, orderCancelList);

        return BusinessResponseCommon.ok(null);
    }

    /**
     * 执行订单取消同步
     *
     * @param orderCancelInfo 订单取消详情
     * @param orderCancelList 订单取消明细
     */
    @Async("faSynchronizationOrderCancel")
    public void synchronizationOrderCancel(OrderCancelPO orderCancelInfo, List<OrderCancelListPO> orderCancelList) {
        logger.info("start synchronization order cancel to mq.");

        // 设置同步订单取消明细
        List<FaOrderCancelDetailSynchronizationOfflineDTO> faOrderCancelDetailSynchronizationOfflineData = setSynchronizationOrderCancelDetailData(orderCancelList);

        // 设置同步订单详情
        FaOrderCancelSynchronizationOfflineDTO faOrderCancelSynchronizationOfflineData = setSynchronizationOrderCancelData(orderCancelInfo, faOrderCancelDetailSynchronizationOfflineData);

        // 执行同步
        synchronizationProducer.synchronizationData(JSON.toJSONString(faOrderCancelSynchronizationOfflineData), BusinessTypeConstant.ORDER_CANCEL, orderCancelInfo.getCancelNumber());

        // 更新状态
        orderCancelMapper.update(null,
                new LambdaUpdateWrapper<OrderCancelPO>()
                        .set(OrderCancelPO::getSyncStatus, SynchronizeStatusEnum.SYNCHRONIZING.getStatus())
                        .set(OrderCancelPO::getUpdatedDate, LocalDateTime.now())
                        .eq(OrderCancelPO::getId, orderCancelInfo.getId())
        );
    }

    /**
     * 设置订单取消明细同步数据
     *
     * @param orderCancelList 订单取消明细
     * @return List<FaOrderCancelDetailSynchronizationOfflineDTO>
     */
    private List<FaOrderCancelDetailSynchronizationOfflineDTO> setSynchronizationOrderCancelDetailData(List<OrderCancelListPO> orderCancelList) {
        logger.info("set synchronization order cancel detail data.");

        // 设置订单取消明细同步参数
        return orderCancelList.stream().map(orderCancelListPO ->
                FaOrderCancelDetailSynchronizationOfflineDTO
                        .builder()
                        .cancelId(orderCancelListPO.getCancelId())
                        .sortId(orderCancelListPO.getSortId())
                        .quantity(orderCancelListPO.getQuantity())
                        .productCode(orderCancelListPO.getProductCode())
                        .productName(orderCancelListPO.getProductName())
                        .productModel(orderCancelListPO.getProductModel())
                        .cancelDetailStatus(orderCancelListPO.getCancelDetailStatus())
                        .build()
        ).collect(Collectors.toList());
    }

    /**
     * 设置同步订单取消的详情参数
     *
     * @param orderCancelInfo                               订单取消详情
     * @param faOrderCancelDetailSynchronizationOfflineList 同步参数
     * @return FaOrderCancelSynchronizationOfflineDTO
     */
    private FaOrderCancelSynchronizationOfflineDTO setSynchronizationOrderCancelData(OrderCancelPO orderCancelInfo, List<FaOrderCancelDetailSynchronizationOfflineDTO> faOrderCancelDetailSynchronizationOfflineList) {
        logger.info("set synchronization order cancel data.");

        // 设置订单取消详情同步数据
        return FaOrderCancelSynchronizationOfflineDTO.builder()
                .cancelNumber(orderCancelInfo.getCancelNumber())
                .orderNumber(orderCancelInfo.getOrderNumber())
                .companyCode(orderCancelInfo.getCompanyCode())
                .companyName(orderCancelInfo.getCompanyName())
                .erpCode(orderCancelInfo.getErpCode())
                .userCode(orderCancelInfo.getUserCode())
                .totalMoney(orderCancelInfo.getTotalMoney())
                .syncStatus(orderCancelInfo.getSyncStatus())
                .cancelType(orderCancelInfo.getCancelType())
                .cancelStatus(orderCancelInfo.getCancelStatus())
                .reason(orderCancelInfo.getReason())
                .notAgree(orderCancelInfo.getNotAgree())
                .handelDate(orderCancelInfo.getHandelDate())
                .syncDate(orderCancelInfo.getSyncDate())
                .refundDate(orderCancelInfo.getRefundDate())
                .departmentLink(orderCancelInfo.getDepartmentLink())
                .territory(orderCancelInfo.getTerritory())
                .syncErrMsg(orderCancelInfo.getSyncErrMsg())
                .cancelDetailList(faOrderCancelDetailSynchronizationOfflineList)
                .build();
    }
}
