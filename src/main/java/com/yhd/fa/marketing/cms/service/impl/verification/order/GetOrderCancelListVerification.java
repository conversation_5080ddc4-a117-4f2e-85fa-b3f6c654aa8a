package com.yhd.fa.marketing.cms.service.impl.verification.order;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCancelRequestVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: GetOrderCancelListVerification.java, v0.1 2023/2/22 10:02 yehuasheng Exp $
 */
@Component
public class GetOrderCancelListVerification {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(GetOrderCancelListVerification.class.getName());

    /**
     * 检查获取订单取消列表参数
     *
     * @param orderCancelRequestVO 订单取消列表参数
     * @param <T>                  T
     * @return BusinessResponse
     */
    public <T> BusinessResponse<T> check(OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("start check get order cancel list parameter.");

        // 判断时间是否正确
        if (ObjectUtil.isNotNull(orderCancelRequestVO.getEndDateTime())
                && ObjectUtil.isNotNull(orderCancelRequestVO.getStartDateTime())
                && orderCancelRequestVO.getStartDateTime().isAfter(orderCancelRequestVO.getEndDateTime())) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.START_DATE_IS_AFTER_END_TIME_ERROR);
        }

        return BusinessResponse.ok(null);
    }
}
