package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_erp_logistics_list")
public class OrderErpLogisticsListPO extends BaseEntity {
    /**
     * 父级id
     */
    private String parentId;

    /**
     * 明细id
     */
    private Integer orderSortId;

    /**
     * 发货数量
     */
    private long quantity;
}

