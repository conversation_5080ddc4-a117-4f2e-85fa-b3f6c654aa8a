package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: FaOrderInvoiceDetailSynchronizationDTO.java, v0.1 2023/3/2 10:31 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaOrderInvoiceDetailSynchronizationDTO extends BaseDTO {
    /**
     * 发票详情id
     */
    private String id;

    /**
     * 索取发票id
     */
    private String orderInvoiceId;

    /**
     * 实付总金额
     */
    private BigDecimal payablePrice;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreatedDate;

    /**
     * 索取发票的订单号
     */
    private String orderNumber;
}
