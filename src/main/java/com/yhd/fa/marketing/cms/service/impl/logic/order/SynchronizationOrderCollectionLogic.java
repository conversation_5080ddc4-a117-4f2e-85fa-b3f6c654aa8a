package com.yhd.fa.marketing.cms.service.impl.logic.order;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCollectionLogMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderCollectionLogPO;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: SynchronizationOrderCollectionLogic.java, v0.1 2023/3/28 17:23 yehuasheng Exp $
 */
@Component
public class SynchronizationOrderCollectionLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(SynchronizationOrderCollectionLogic.class.getName());

    /**
     * 订单收款单记录mapper
     */
    @Resource
    private OrderCollectionLogMapper orderCollectionLogMapper;

    /**
     * 执行同步订单收款单
     *
     * @param orderCollectionLogId 订单收款单id
     * @return BusinessResponse<Void>
     */
    @Transactional
    public BusinessResponse<Void> exec(String orderCollectionLogId) {
        logger.info("start exec order collection logic.");

        // 更新收款单同步记录
        return orderCollectionLogMapper
                .update(null,
                        new LambdaUpdateWrapper<OrderCollectionLogPO>()
                                .eq(OrderCollectionLogPO::getId, orderCollectionLogId)
                                .set(OrderCollectionLogPO::getUpdatedBy, SecurityUtil.getUserName())
                                .set(OrderCollectionLogPO::getUpdatedDate, LocalDateTime.now())
                                .set(OrderCollectionLogPO::getSynchronizationsNumber, CommonConstant.ZERO)
                                .set(OrderCollectionLogPO::getDownErpStatus, "wait")) ? BusinessResponseCommon.fail(FaDocMarketingResponseEnum.RESET_SYNCHRONIZATION_ORDER_COLLECTION_LOG_FAIL) : BusinessResponseCommon.ok(null);
    }
}
