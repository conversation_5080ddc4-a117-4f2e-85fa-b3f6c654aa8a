package com.yhd.fa.marketing.cms.service.sao;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionCategoryRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionTypeRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationCategoryResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationTypeResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProductInfoResponseVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: ProductCenterCmsService.java, v0.1 2022/12/19 10:54 yehuasheng Exp $
 */
public interface ProductCenterCmsService {
    /**
     * 获取产品分类、代码信息
     *
     * @param approveQuotationSelectionTypeRequestVO 产品分类代码的请求参数
     * @return BusinessResponse<List < ApproveQuotationTypeResponseVO>>
     */
    BusinessResponse<List<ApproveQuotationTypeResponseVO>> getProductTypeCode(ApproveQuotationSelectionTypeRequestVO approveQuotationSelectionTypeRequestVO);

    /**
     * 获取产品的类别
     *
     * @param approveQuotationSelectionCategoryRequestVO 请求获取产品类别的参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    BusinessResponse<List<ApproveQuotationCategoryResponseVO>> getProductCategoryCode(ApproveQuotationSelectionCategoryRequestVO approveQuotationSelectionCategoryRequestVO);

    /**
     * 根据代码获取产品信息
     *
     * @param productCode 产品代码
     * @return Map<String, ProductInfoResponseVO>
     */
    Map<String, ProductInfoResponseVO> getProductInfo(List<String> productCode);
}
