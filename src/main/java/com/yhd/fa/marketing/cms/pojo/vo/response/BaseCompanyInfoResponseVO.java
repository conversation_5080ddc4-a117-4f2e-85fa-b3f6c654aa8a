package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: BaseCompanyInfoResponseVO.java, v0.1 2023/4/3 11:21 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseCompanyInfoResponseVO extends BaseVO {
    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * erp编码
     */
    private String erpCompanyCode;

    /**
     * 是否认证企业
     */
    private String attestationStatus;

    /**
     * 跟单信息返参实体
     */
    private MerchandiserResponseVO merchandiserInfo;

    /**
     * 业务员信息
     */
    private MerchandiserResponseVO salesManInfo;
}
