package com.yhd.fa.marketing.cms.service.impl.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRemarkRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsReplyRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProblemCategoriesListResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderCommentsService;
import com.yhd.fa.marketing.cms.service.impl.logic.order.*;
import com.yhd.fa.marketing.cms.service.impl.verification.order.GetOrderCommentsListVerification;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsServiceImpl.java, v0.1 2023/2/24 18:55 yehuasheng Exp $
 */
@Service
public class OrderCommentsServiceImpl implements OrderCommentsService {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(OrderCommentsServiceImpl.class.getName());

    /**
     * 订单评论列表校验
     */
    @Resource
    private GetOrderCommentsListVerification getOrderCommentsListVerification;

    /**
     * 获取订单评论逻辑
     */
    @Resource
    private GetOrderCommentsListLogic getOrderCommentsListLogic;

    /**
     * 获取订单评论详情页逻辑
     */
    @Resource
    private GetOrderCommentsInfoLogic getOrderCommentsInfoLogic;

    /**
     * 订单评价回复逻辑
     */
    @Resource
    private OrderCommentsReplyLogic orderCommentsReplyLogic;

    /**
     * 订单评价详情备注
     */
    @Resource
    private OrderCommentsRemarkLogic orderCommentsRemarkLogic;

    @Resource
    private OrderCommentsProblemCategoriesLogic orderCommentsProblemCategoriesLogic;

    /**
     * 获取订单评论列表
     *
     * @param orderCommentsRequestVO 订单评论列表请求参数
     * @return BusinessResponse<PageInfo < OrderCommentsResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<OrderCommentsResponseVO>> getOrderCommentsList(OrderCommentsRequestVO orderCommentsRequestVO) {
        logger.info("start get order comments list service.");

        // 校验请求参数
        BusinessResponse<PageInfo<OrderCommentsResponseVO>> checkParameter = getOrderCommentsListVerification.check(orderCommentsRequestVO);
        if (!checkParameter.success()) {
            return checkParameter;
        }

        // 执行逻辑
        return getOrderCommentsListLogic.exec(orderCommentsRequestVO);
    }

    /**
     * 获取订单评论详情页
     *
     * @param orderCommentsId 订单评论id
     * @return BusinessResponse<OrderCommentsDetailResponseVO>
     */
    @Override
    public BusinessResponse<OrderCommentsDetailResponseVO> getOrderCommentsInfo(String orderCommentsId) {
        logger.info("start get order comments detail service.");

        // 执行逻辑
        return getOrderCommentsInfoLogic.exec(orderCommentsId);
    }

    /**
     * 订单评价回复
     *
     * @param requestVO 请求参数
     * @return BusinessResponse<Object>
     */
    @Override
    public BusinessResponse<Object> orderCommentsReply(OrderCommentsReplyRequestVO requestVO) {

        logger.info("start order comments reply service.");

        return orderCommentsReplyLogic.orderCommentsReply(requestVO);
    }

    /**
     * 订单评论详情页修改备注
     * @param requestVO 请求参数
     * @return BusinessResponse<Object>
     */
    @Override
    public BusinessResponse<Object> orderCommentsRemark(OrderCommentsRemarkRequestVO requestVO) {
        logger.info("start order comments detail other edit service.");

        // 执行逻辑
        return orderCommentsRemarkLogic.exec(requestVO);

    }

    /**
     * 获取订单评论问题分类列表
     *
     * @return BusinessResponse
     */
    @Override
    public BusinessResponse<List<ProblemCategoriesListResponseVO>> orderCommentsProblemCategoriesList() {
        logger.info("start get order comments problem categories list service.");

        return orderCommentsProblemCategoriesLogic.orderCommentsProblemCategoriesList();
    }
}
