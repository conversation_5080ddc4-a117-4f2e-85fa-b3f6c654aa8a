package com.yhd.fa.marketing.cms.dao;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationClickHousePO;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @version Id: SaleCompanyRelationClickHouseDAO.java, v 0.1 2025/5/7 15:23 JiangYuHong Exp $
 */
public interface SaleCompanyRelationClickHouseDAO extends MPJBaseMapper<SaleCompanyRelationClickHousePO> {

    /**
     * 清空表操作，谨慎调用
     */
    @DS("order_ch")
    @Update("TRUNCATE TABLE `sale_company_relation`")
    void truncateClickHouseData();
}
