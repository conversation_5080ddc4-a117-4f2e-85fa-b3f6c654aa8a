package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.OrderByFieldConstant;
import com.yhd.fa.marketing.cms.dao.CompanyDAO;
import com.yhd.fa.marketing.cms.enums.ShareSystemdCompanyLevelEnum;
import com.yhd.fa.marketing.cms.mapper.EnquiryLogMapper;
import com.yhd.fa.marketing.cms.pojo.dto.ShareSystemdCompanyLevelDTO;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.CompanyPO;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.po.UcSyncCustomerInfoPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.EnquiryLogListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CustomerCompanyListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryLogListResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetEnquiryLogLogic.java, v0.1 2022/12/8 11:20 yehuasheng Exp $
 */
@Component
public class GetEnquiryLogLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetEnquiryLogLogic.class.getName());

    /**
     * 查价单的mapper
     */
    @Resource
    private EnquiryLogMapper enquiryLogMapper;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 用户的服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private CompanyDAO companyDAO;

    /**
     * 获取查价单集合
     *
     * @param enquiryLogListRequestVO 查价单集合请求参数
     * @return BusinessResponse<PageInfo < EnquiryLogListResponseVO>>
     */
    public BusinessResponse<PageInfo<EnquiryLogListResponseVO>> exec(EnquiryLogListRequestVO enquiryLogListRequestVO) {
        logger.info("start exec get enquiry log.");

        // 设置查询内容
        MPJLambdaWrapper<EnquiryLogPO> queryWrapper = setEnquiryLogQueryWrapper(enquiryLogListRequestVO);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, EnquiryLogPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 设置分页
        if (enquiryLogListRequestVO.getPageNum() != 0) {
            PageMethod.startPage(enquiryLogListRequestVO.getPageNum(), enquiryLogListRequestVO.getPageSize());
        }

        // 查询集合
        List<EnquiryLogListResponseVO> enquiryLogList = enquiryLogMapper.selectJoinList(EnquiryLogListResponseVO.class, queryWrapper);

        // 设置分页
        PageInfo<EnquiryLogListResponseVO> pageInfo = new PageInfo<>(enquiryLogList);

//        // 获取用户、企业名称、跟单、业务员信息
//        setResultData(pageInfo.getList());
//
//        List<String> companyList = enquiryLogList.stream().map(EnquiryLogListResponseVO::getCompanyCode).distinct().collect(Collectors.toList());
//
//        setShareSystemdCompanyLevel(companyList, pageInfo.getList());

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 设置查询条件
     *
     * @param enquiryLogListRequestVO 查价单请求参数
     * @return MPJLambdaWrapper<EnquiryLogPO>
     */
    private MPJLambdaWrapper<EnquiryLogPO> setEnquiryLogQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO) {
        logger.info("set enquiry log query wrapper.");

        // 设置初始查询条件
        MPJLambdaWrapper<EnquiryLogPO> queryWrapper = new MPJLambdaWrapper<EnquiryLogPO>()
                .selectAs(EnquiryLogPO::getId, EnquiryLogListResponseVO::getId)
                .selectAs(EnquiryLogPO::getModel, EnquiryLogListResponseVO::getModel)
                .selectAs(EnquiryLogPO::getQuantity, EnquiryLogListResponseVO::getQuantity)
                .selectAs(EnquiryLogPO::getCostPrice, EnquiryLogListResponseVO::getPrice)
                .selectAs(EnquiryLogPO::getDiscountPrice, EnquiryLogListResponseVO::getDiscountPrice)
                .selectAs(EnquiryLogPO::getTaxDiscountPrice, EnquiryLogListResponseVO::getTaxDiscountPrice)
                .selectAs(EnquiryLogPO::getTaxTotalPrice, EnquiryLogListResponseVO::getTotalPrice)
                .selectAs(EnquiryLogPO::getDelivery, EnquiryLogListResponseVO::getDelivery)
                .selectAs(EnquiryLogPO::getUserCode, EnquiryLogListResponseVO::getUserCode)
                .selectAs(EnquiryLogPO::getCompanyCode, EnquiryLogListResponseVO::getCompanyCode)
                .selectAs(EnquiryLogPO::getProductName, EnquiryLogListResponseVO::getProductName)
                .selectAs(EnquiryLogPO::getCode, EnquiryLogListResponseVO::getCode)
                .selectAs(EnquiryLogPO::getIsStandard, EnquiryLogListResponseVO::getIsStandard)
                .selectAs(EnquiryLogPO::getErrorMsg, EnquiryLogListResponseVO::getErrorMsg)
                .selectAs(EnquiryLogPO::getCreatedDate, EnquiryLogListResponseVO::getCreatedDate)
                .selectAs(EnquiryLogPO::getRegisterDate, EnquiryLogListResponseVO::getRegisterDate)
                .selectAs(EnquiryLogPO::getUserType, EnquiryLogListResponseVO::getUserType)
                .selectAs(EnquiryLogPO::getOccupation, EnquiryLogListResponseVO::getOccupation)
                .selectAs(EnquiryLogPO::getIpRegion, EnquiryLogListResponseVO::getIpRegion)

                .selectAs(EnquiryLogPO::getUserName, EnquiryLogListResponseVO::getUserName)
                .selectAs(EnquiryLogPO::getCompanyName, EnquiryLogListResponseVO::getCompanyName)
                .selectAs(EnquiryLogPO::getUserPhone, EnquiryLogListResponseVO::getUserPhone)
                .selectAs(EnquiryLogPO::getUserEmail, EnquiryLogListResponseVO::getUserEmail)
                .selectAs(EnquiryLogPO::getMerchandiser, EnquiryLogListResponseVO::getMerchandiser)
                .selectAs(EnquiryLogPO::getSalesman, EnquiryLogListResponseVO::getSalesman)
                .selectAs(EnquiryLogPO::getCustomerGrade, EnquiryLogListResponseVO::getCustomerGrade)
                .orderByDesc(EnquiryLogPO::getCreatedDate);


        // 判断是否有日期
        setTimeQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        // 判断是否有型号
        setModelQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        // 判断是否有企业名称
        setCompanyNameQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        // 判断是否有用户手机号码
        setUserMobileQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        // 判断是否有用户邮箱
        setUserEmailQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        // 判断是否有传自定义排序
        setOrderByQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        // 判断是否有产品名称
        setProductNameQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        // 判断是否有含税单价
        setTaxDiscountPriceQueryWrapper(enquiryLogListRequestVO, queryWrapper);

        if (enquiryLogListRequestVO.getStartRegisterDate() != null && enquiryLogListRequestVO.getEndRegisterDate() != null) {
            queryWrapper.ge(EnquiryLogPO::getRegisterDate, enquiryLogListRequestVO.getStartRegisterDate().atStartOfDay().format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
            queryWrapper.le(EnquiryLogPO::getRegisterDate, enquiryLogListRequestVO.getEndRegisterDate().atTime(LocalTime.MAX).format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
        }

        // 是否查询用户类型
        Optional.ofNullable(enquiryLogListRequestVO.getUserType())
                .filter(StringUtils::isNotBlank)
                .ifPresent(userType -> queryWrapper.eq(EnquiryLogPO::getUserType, userType));

        // 是否有查询职位
        Optional.ofNullable(enquiryLogListRequestVO.getOccupation())
                .filter(StringUtils::isNotBlank)
                .ifPresent(occupation -> queryWrapper.eq(EnquiryLogPO::getUserType, occupation));

        // 是否有查询区域
        Optional.ofNullable(enquiryLogListRequestVO.getIpRegion())
                .filter(StringUtils::isNotBlank)
                .ifPresent(region -> queryWrapper.likeRight(EnquiryLogPO::getIpRegion, region));

        return queryWrapper;
    }

    /**
     * 设置查询含税单价
     *
     * @param enquiryLogListRequestVO 请求参数
     * @param queryWrapper            查询where条件
     */
    private void setTaxDiscountPriceQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set tax discount price query wrapper.");

        if (enquiryLogListRequestVO.getTaxDiscountPriceMin() != null) {
            queryWrapper.ge(EnquiryLogPO::getTaxDiscountPrice, enquiryLogListRequestVO.getTaxDiscountPriceMin());

        }
        if (enquiryLogListRequestVO.getTaxDiscountPriceMax() != null) {
            queryWrapper.le(EnquiryLogPO::getTaxDiscountPrice, enquiryLogListRequestVO.getTaxDiscountPriceMax());
        }
    }

    /**
     * 设置查询产品名称
     *
     * @param enquiryLogListRequestVO 请求参数
     * @param queryWrapper            查询where条件
     */
    private void setProductNameQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set product name query wrapper.");
        if (StringUtils.isNotBlank(enquiryLogListRequestVO.getProductName())) {
            queryWrapper.like(EnquiryLogPO::getProductName, enquiryLogListRequestVO.getProductName());
        }
    }

    /**
     * 设置查询传时间
     *
     * @param enquiryLogListRequestVO 查价单列表请求参数
     * @param queryWrapper            查询where条件
     */
    private void setTimeQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set time query wrapper.");
        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();
        // 开始时间
        LocalDateTime startTime = Optional.ofNullable(enquiryLogListRequestVO.getStartTime()).orElse(nowDateTime.minusMonths(CommonConstant.ONE));
        // 结束时间
        LocalDateTime endTime = Optional.ofNullable(enquiryLogListRequestVO.getEndTime()).orElse(nowDateTime);
        // 时间的分区
        LocalDate createdDay = startTime.toLocalDate();

        // 设置查询
        queryWrapper.ge(EnquiryLogPO::getCreatedDay, createdDay);
        queryWrapper.ge(EnquiryLogPO::getCreatedDate, startTime.format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
        queryWrapper.le(EnquiryLogPO::getCreatedDate, endTime.format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
    }

    /**
     * 设置型号查询
     *
     * @param enquiryLogListRequestVO 查价单列表请求参数
     * @param queryWrapper            查询where条件
     */
    private void setModelQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set model query wrapper.");

        if (StringUtils.isNotBlank(enquiryLogListRequestVO.getModel())) {
            queryWrapper.eq(EnquiryLogPO::getModel, enquiryLogListRequestVO.getModel());
        }
    }

    /**
     * 设置企业名称查询
     *
     * @param enquiryLogListRequestVO 查价单列表请求参数
     * @param queryWrapper            查询where条件
     */
    private void setCompanyNameQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set company name query wrapper.");

        if (StringUtils.isNotBlank(enquiryLogListRequestVO.getCompanyName())) {
            queryWrapper.like(EnquiryLogPO::getCompanyName, "%"+enquiryLogListRequestVO.getCompanyName()+"%");
//            // 请求用户中心获取企业编码
//            List<CustomerCompanyListResponseVO> customerCompanyList = userBucCmsService.searchCompanyListByCompanyName(enquiryLogListRequestVO.getCompanyName());
//            if (CollUtil.isNotEmpty(customerCompanyList)) {
//                // 提取企业编码
//                List<String> companyCode = customerCompanyList.stream().map(CustomerCompanyListResponseVO::getCompanyCode).collect(Collectors.toList());
//                queryWrapper.in(EnquiryLogPO::getCompanyCode, companyCode);
//            } else {
//                // 如果查不到企业默认设置值
//                queryWrapper.eq(EnquiryLogPO::getCompanyCode, CommonConstant.FALSE);
//            }
        }
    }

    /**
     * 设置用户手机号码查询条件
     *
     * @param enquiryLogListRequestVO 查价单列表请求参数
     * @param queryWrapper            查询where条件
     */
    private void setUserMobileQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set user mobile query wrapper.");

        if (StringUtils.isNotBlank(enquiryLogListRequestVO.getMobile())) {
            queryWrapper.like(EnquiryLogPO::getUserPhone,"%"+enquiryLogListRequestVO.getMobile()+"%");
//            // 请求用户中心获取用户编码
//            List<String> userCodeByModel = userBucCmsService.getUserCodeByModelOrEmail(enquiryLogListRequestVO.getMobile(), null);
//            queryWrapper.in(EnquiryLogPO::getUserCode, userCodeByModel);
        }
    }

    /**
     * 设置用户邮箱查询条件
     *
     * @param enquiryLogListRequestVO 查价单列表请求参数
     * @param queryWrapper            查询where条件
     */
    private void setUserEmailQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set user email query wrapper.");

        if (StringUtils.isNotBlank(enquiryLogListRequestVO.getEmail())) {
            queryWrapper.like(EnquiryLogPO::getUserEmail,"%"+enquiryLogListRequestVO.getEmail()+"%");
//            // 请求用户中心获取用户编码
//            List<String> userCodeByModel = userBucCmsService.getUserCodeByModelOrEmail(null, enquiryLogListRequestVO.getEmail());
//            queryWrapper.in(EnquiryLogPO::getUserCode, userCodeByModel);
        }
    }

    /**
     * 设置用户信息
     *
     * @param enquiryLogList 询价记录
     */
    private void setResultData(List<EnquiryLogListResponseVO> enquiryLogList) {
        logger.info("set enquiry log result data.");

        if (CollUtil.isEmpty(enquiryLogList)) return;

        // 提取用户code
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = enquiryLogList
                .stream()
                .map(enquiryLogListResponseVO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .userCode(enquiryLogListResponseVO.getUserCode())
                        .companyCode(enquiryLogListResponseVO.getCompanyCode())
                        .build())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(userCodeAndCompanyCodeDTO)) {
            // 获取用户的信息
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userInfoMap = userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);

            enquiryLogList
                    .stream()
                    .filter(enquiryLogListResponseVO
                            -> StringUtils.isNotBlank(enquiryLogListResponseVO.getUserCode())
                            && userInfoMap.containsKey(enquiryLogListResponseVO.getUserCode()))
                    .forEach(enquiryLogListResponseVO -> Optional.ofNullable(userInfoMap.get(enquiryLogListResponseVO.getUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
                        // 设置用户的名字和企业的名称
                        Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getUserInfo()).ifPresent(baseUserInfoResponseVO -> {
                            enquiryLogListResponseVO.setUserName(baseUserInfoResponseVO.getUserName());
                            enquiryLogListResponseVO.setUserPhone(baseUserInfoResponseVO.getMobile());
                            enquiryLogListResponseVO.setUserEmail(baseUserInfoResponseVO.getEmail());
                        });
                        // 设置企业信息
                        Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).ifPresent(merchandiserInfo
                                -> enquiryLogListResponseVO.setMerchandiser(merchandiserInfo.getEmployeeCode() + CommonConstant.SLASH + merchandiserInfo.getEmployeeName()));
                        Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getSalesManInfo()).ifPresent(merchandiserInfo
                                -> enquiryLogListResponseVO.setSalesman(merchandiserInfo.getEmployeeCode() + CommonConstant.SLASH + merchandiserInfo.getEmployeeName()));
                        Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getCompanyInfo()).ifPresent(baseCompanyInfoResponseVO
                                -> enquiryLogListResponseVO.setCompanyName(baseCompanyInfoResponseVO.getCompanyName()));
                    }));
        }
    }

    /**
     * 设置排序
     *
     * @param enquiryLogListRequestVO 查价单列表请求参数
     * @param queryWrapper            查询where条件
     */
    private void setOrderByQueryWrapper(EnquiryLogListRequestVO enquiryLogListRequestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set get enquiry log list order by.");

        // 定义是否升序
        boolean isAsc = StringUtils.equals(enquiryLogListRequestVO.getOrderByType(), OrderByFieldConstant.ASC);

        if (StringUtils.isNotBlank(enquiryLogListRequestVO.getOrderByField())) {

            switch (enquiryLogListRequestVO.getOrderByField()) {
                case OrderByFieldConstant.CREATED_DATE:
                    queryWrapper.orderBy(true, isAsc, EnquiryLogPO::getCreatedDate);
                    break;
                case OrderByFieldConstant.UPDATED_DATE:
                    queryWrapper.orderBy(true, isAsc, EnquiryLogPO::getUpdatedDate);
                    break;
                default:
            }
        }
    }

    private void setShareSystemdCompanyLevel(List<String> companyList, List<EnquiryLogListResponseVO> enquiryLogList) {

        if (CollUtil.isEmpty(companyList)) return;

        companyDAO.selectJoinList(ShareSystemdCompanyLevelDTO.class, new MPJLambdaWrapper<CompanyPO>()
                        .leftJoin(UcSyncCustomerInfoPO.class, UcSyncCustomerInfoPO::getCustomerNo, CompanyPO::getErpCompanyCode)
                        .selectAs(CompanyPO::getCompanyCode, ShareSystemdCompanyLevelDTO::getCompanyCode)
                        .selectAs(UcSyncCustomerInfoPO::getCustomerNo, ShareSystemdCompanyLevelDTO::getCustomerNo)
                        .selectAs(UcSyncCustomerInfoPO::getCustomerGrade, ShareSystemdCompanyLevelDTO::getCustomerGrade)
                        .selectAll(CompanyPO.class)
                        .in(CompanyPO::getCompanyCode, companyList))
                .forEach(ucSyncCustomerInfoPrivatePO -> enquiryLogList.stream()
                        .filter(enquiryLogListResponseVO -> StringUtils.equals(enquiryLogListResponseVO.getCompanyCode(), ucSyncCustomerInfoPrivatePO.getCompanyCode()))
                        .forEach(enquiryLogListResponseVO -> enquiryLogListResponseVO.setCustomerGrade(ShareSystemdCompanyLevelEnum.getName(ucSyncCustomerInfoPrivatePO.getCustomerGrade()))));

    }

    public void enquiryLogAdd(List<EnquiryLogPO> enquiryLogPOList) {

    }
}
