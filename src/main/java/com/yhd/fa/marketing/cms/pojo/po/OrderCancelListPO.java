package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_cancel_list")
public class OrderCancelListPO extends BaseEntity {
    /**
     * 取消id
     */
    private String cancelId;

    /**
     * 订单明细id
     */
    private String orderListId;

    /**
     * 明细id
     */
    private int sortId;

    /**
     * 取消数量
     */
    private long quantity;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 状态 明细取消状态 canceling 取消中, cancel 已取消, turnDown 驳回
     */
    private String cancelDetailStatus;

    /**
     * 原单价
     */
    private BigDecimal price;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 交期
     */
    private Integer delivery;
}

