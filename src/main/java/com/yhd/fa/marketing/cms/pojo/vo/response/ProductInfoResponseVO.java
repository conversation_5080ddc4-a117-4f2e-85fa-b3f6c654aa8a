package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: ProductInfoResponseVO.java, v0.1 2023/2/20 15:52 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductInfoResponseVO extends BaseVO {
    /**
     * 产品id
     */
    private String id;

    /**
     * 一级分类编码
     */
    private String typeCode;

    /**
     * 一级分类名称
     */
    private String typeCodeName;

    /**
     * 二级分类编码
     */
    private String catCode;

    /**
     * 二级分类编码名称
     */
    private String catCodeName;

    /**
     * 系列编码
     */
    private String goodsCode;

    /**
     * 系列编码名称
     */
    private String goodsCodeName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 状态
     */
    private String status;
}
