package com.yhd.fa.marketing.cms.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: CompanyPO.java, v 0.1 2025/5/7 15:05 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("uc_company")
@EqualsAndHashCode(callSuper = true)
public class CompanyPO extends BaseEntity {

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业名字
     */
    private String companyName;

    /**
     * ERP企业编码
     */
    private String erpCompanyCode;

    /**
     * 企业地址
     */
    private String address;


    /**
     * 是否认证 true false
     */
    private String attestationStatus;


    /**
     * 注册申请表
     */
    private String registrationFormUrl;

    /**
     * 营业执照
     */
    private String businessLicenseUrl;

    /**
     * 联系人
     */
    private String linkname;

    /**
     * 联系人电话
     */
    private String linkphone;

    /**
     * 联系人邮箱
     */
    private String linkemail;

    /**
     * 联系人职位
     */
    private String occupation;

    /**
     * 联系人所在部门
     */
    private String department;

    /**
     * 'enable'(启用),'disable'（未启用）
     */
    private String status;

    /**
     * 'false','true'（是否测试企业）
     */
    private String isTest;
}
