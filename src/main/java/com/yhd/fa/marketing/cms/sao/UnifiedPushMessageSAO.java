package com.yhd.fa.marketing.cms.sao;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version Id: UnifiedPushMessageSAO.java, v0.1 2023/3/6 16:24 yehuasheng Exp $
 */
@FeignClient(name = "yhd-service-fa-unified-push-message")
public interface UnifiedPushMessageSAO {
    /**
     * 发送钉钉告警群
     *
     * @param sendMessageToDingDingRobotRequestVO 发送的钉钉告警警告
     * @return BusinessResponse<String>
     */
    @PostMapping(value = "/msg/v1/0/ding/sendRobot", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<String> sendDingDingWarn(@RequestBody SendMessageToDingDingRobotRequestVO sendMessageToDingDingRobotRequestVO);

    /**
     * 发送钉钉多对多
     *
     * @param sendMessageToDingDingRobotRequestVO 发送的钉钉告警警告
     * @return BusinessResponse<String>
     */
    @PostMapping(value = "/msg/v1/0/ding/sendMany", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<String> sendDingDingMany(@RequestBody SendMessageToDingDingManyRequestVO sendMessageToDingDingRobotRequestVO);

    /**
     * 发送钉钉一对多
     *
     * @param sendMessageToDingDingRequestVO 发送的钉钉告警警告
     * @return BusinessResponse<String>
     */
    @PostMapping(value = "/msg/v1/0/ding/send", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<String> sendDingDingOneToMany(@RequestBody SendMessageToDingDingRequestVO sendMessageToDingDingRequestVO);

    /**
     * 发送短信 多对多
     *
     * @param sendMessageToSmsManyRequestVO 发送短信多对多
     * @return BusinessResponse<String>
     */
    @PostMapping(value = "/msg/v1/0/sms/sendMany", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<String> sendSmsToMany(@RequestBody SendMessageToSmsManyRequestVO sendMessageToSmsManyRequestVO);

    /**
     * 发送短信
     *
     * @param sendMessageToSmsRequestVO 发送短信参数
     * @return BusinessResponse<String>
     */
    @PostMapping(value = "/msg/v1/0/sms/send", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<String> sendSms(@RequestBody SendMessageToSmsRequestVO sendMessageToSmsRequestVO);

    /**
     * 发送邮件 多对多
     *
     * @param sendMessageToEmailManyRequestVO 发送邮件参数
     * @return BusinessResponse<String>
     */
    @PostMapping(value = "/msg/v1/0/email/sendMany", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<String> sendEmailToMany(@RequestBody SendMessageToEmailManyRequestVO sendMessageToEmailManyRequestVO);

    /**
     * 发送邮件
     *
     * @param sendMessageToEmailRequestVO 发送邮件参数
     * @return BusinessResponse<String>
     */
    @PostMapping(value = "/msg/v1/0/email/send", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<String> sendEmail(@RequestBody SendMessageToEmailRequestVO sendMessageToEmailRequestVO);
}
