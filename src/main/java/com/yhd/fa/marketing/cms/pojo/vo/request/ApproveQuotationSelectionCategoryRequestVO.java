package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.ApproveQuotationCategoryTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationSelectionCategoryRequestVO.java, v0.1 2022/12/19 10:18 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveQuotationSelectionCategoryRequestVO extends BaseVO {
    /**
     * 类型
     */
    @NotEmpty(message = "请求类型不能为空")
    @Schema(description = "类型（type：一级分类，如A  cat：二级分类，如A01  goods：商品系列，如SAD01-22  product：商品代码，如SAD01)",
            example = "type",
            allowableValues = {ApproveQuotationCategoryTypeConstant.TYPE,
                    ApproveQuotationCategoryTypeConstant.CAT,
                    ApproveQuotationCategoryTypeConstant.GOODS,
                    ApproveQuotationCategoryTypeConstant.PRODUCT})
    @ValueInEnum(matchTarget = {ApproveQuotationCategoryTypeConstant.TYPE,
            ApproveQuotationCategoryTypeConstant.CAT,
            ApproveQuotationCategoryTypeConstant.GOODS,
            ApproveQuotationCategoryTypeConstant.PRODUCT}, message = "类型不正确")
    private String type;

    /**
     * 编码
     */
    @NotEmpty(message = "请求编码不能为空")
    @Schema(description = "一二级分类编码、系列编码、产品代码", example = "A")
    private String code;
}
