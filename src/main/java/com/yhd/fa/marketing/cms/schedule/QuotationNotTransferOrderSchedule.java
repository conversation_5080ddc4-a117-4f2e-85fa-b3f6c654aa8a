package com.yhd.fa.marketing.cms.schedule;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.common.util.UUIDUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotationTimePushLogPushTypeConstant;
import com.yhd.fa.marketing.cms.constant.TimedPushLogPushPlatformConstant;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.mapper.QuotationTimedPushLogMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationTimedPushLogPO;
import com.yhd.fa.marketing.cms.service.sao.UnifiedPushMessageService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: QuotationNotTransferOrderSchedule.java, v0.1 2023/9/28 11:23 yehuasheng Exp $
 */
@Component
@RefreshScope
public class QuotationNotTransferOrderSchedule {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(QuotationNotTransferOrderSchedule.class.getName());

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    /**
     * 报价单订单推送记录mapper
     */
    @Resource
    private QuotationTimedPushLogMapper quotationTimedPushLogMapper;

    /**
     * 模板id
     */
    @Value("${schedule.push.quotation.notTransferOrderTemplateId}")
    private String templateId;

    /**
     * 用户中心信息
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 推送消息服务
     */
    @Resource
    private UnifiedPushMessageService unifiedPushMessageService;

    /**
     * 定时推送超2天报价单未转订单
     */
    @XxlJob("quotationNotTransferOrderTimedPush")
    public void quotationNotTransferOrder() {
        logger.info("start quotation not transfer order timed push schedule.");

        // 查询需要定时推送的48小时之后未转订单的报价单
        LocalDateTime nowDateTime = LocalDateTime.now();

        // 查询48小时之后未转订单的报价单
        // SELECT
        //  id,
        //  quotation_number,
        //  company_code,
        //  company_name,
        //  user_code,
        //  purchase_user_code,
        //  created_date,
        //  quotation_completion_date,
        //  payable_price,
        //  quotation_status
        //FROM
        //  `fa_quotation`
        //WHERE id NOT IN
        //  (SELECT
        //    quotation_id
        //  FROM
        //    `fa_quotation_timed_push_log`
        //  WHERE `push_platform` = 'dingding'
        //    AND push_type = 'notTransferOrder'
        //    AND updated_date >= '当前时间前7天')
        //  AND quotation_completion_date >= '当前时间前7天'
        //  AND quotation_completion_date <= '当前时间前2天'
        //  AND quotation_status = 'finish'
        //  AND payable_price IS NOT NULL
        //  AND transfer_order = 'false'
        List<QuotationPO> quotationList = quotationMapper.list(new LambdaQueryWrapper<QuotationPO>()
                .notInSql(QuotationPO::getId, "SELECT `quotation_id` FROM `fa_quotation_timed_push_log` WHERE `push_platform` = '" + TimedPushLogPushPlatformConstant.DING_DING + "' AND `push_type` = '" + QuotationTimePushLogPushTypeConstant.NOT_TRANSFER_ORDER + "' AND `created_date` >= '" + nowDateTime.minusDays(CommonConstant.SEVEN) + "'")
                .ge(QuotationPO::getQuotationCompletionDate, nowDateTime.minusDays(CommonConstant.SEVEN))
                .le(QuotationPO::getQuotationCompletionDate, nowDateTime.minusDays(CommonConstant.TWO))
                .eq(QuotationPO::getQuotationStatus, QuotationStatusConstant.FINISH)
                .isNotNull(QuotationPO::getPayablePrice)
                .eq(QuotationPO::getTransferOrder, CommonConstant.FALSE)
                .select(QuotationPO::getId, QuotationPO::getQuotationNumber, QuotationPO::getCompanyCode, QuotationPO::getCompanyName, QuotationPO::getUserCode, QuotationPO::getPurchaseUserCode, QuotationPO::getCreatedDate, QuotationPO::getQuotationCompletionDate, QuotationPO::getPayablePrice, QuotationPO::getQuotationStatus)
        );

        // 处理定时推送业务
        if (CollUtil.isNotEmpty(quotationList)) {
            // 获取报价单对应用户的业务员
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userMap = getUserInfo(quotationList);

            // 推送钉钉
            List<String> quotationIdList = pushNotTransferOrderToDingDing(quotationList, userMap);

            // 过滤发送失败的报价单
            quotationList.removeIf(quotationPO -> quotationIdList.contains(quotationPO.getId()));

            // 插入数据库
            insertQuotationTimedPushLogData(quotationList, userMap);
        }
    }

    /**
     * 获取用户信息
     *
     * @param quotationList 报价单列表
     * @return Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO>
     */
    private Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserInfo(List<QuotationPO> quotationList) {
        logger.info("quotation not transfer order timed push get pusher");

        // 报价单对于的用户编码
        Map<String, String> quotationToUserCodeMap = quotationList.stream().collect(Collectors.toMap(QuotationPO::getId, QuotationPO::getUserCode, (a, b) -> a));

        // 提取用户编码
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = quotationList
                .stream()
                .map(quotationPO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .companyCode(quotationPO.getCompanyCode())
                        .userCode(quotationPO.getUserCode()).build())
                .collect(Collectors.toList());

        // 获取业务员信息
        Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userInfoMap = userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);

        return quotationToUserCodeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, quotationToUserCode -> userInfoMap.get(quotationToUserCode.getValue())));
    }

    /**
     * 推送报价单未转订单的钉钉消息
     *
     * @param quotationList 报价单列表
     * @param userMap       用户信息
     * @return List<String>
     */
    private List<String> pushNotTransferOrderToDingDing(List<QuotationPO> quotationList, Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userMap) {
        logger.info("start push not transfer order to dingding api.");

        // 定义输出的报价单
        List<String> quotationIdList = new ArrayList<>();

        quotationList.stream().filter(quotationPO -> userMap.containsKey(quotationPO.getId())
                        && Optional.ofNullable(userMap.get(quotationPO.getId()).getSalesManInfo()).isPresent())
                .forEach(quotationPO -> {
                    // 组装要替换的内容
                    Map<String, Object> replaceContent = new HashMap<>();
                    replaceContent.put("userName", userMap.get(quotationPO.getId()).getUserInfo().getUserName());
                    replaceContent.put("userLink", Optional.ofNullable(userMap.get(quotationPO.getId()).getUserInfo().getMobile()).orElse(userMap.get(quotationPO.getId()).getUserInfo().getEmail()));
                    replaceContent.put("companyName", quotationPO.getCompanyName());
                    replaceContent.put("createdDate", quotationPO.getCreatedDate());
                    replaceContent.put("quotationNumber", quotationPO.getQuotationNumber());
                    replaceContent.put("finishDate", quotationPO.getQuotationCompletionDate());
                    replaceContent.put("price", quotationPO.getPayablePrice());

                    // 推送人
                    List<String> receivers = Collections.singletonList(userMap.get(quotationPO.getId()).getSalesManInfo().getEmployeeCode());

                    // 推送
                    BusinessResponse<String> businessResponse = unifiedPushMessageService.sendDingDingOneToMany(templateId, receivers, replaceContent);
                    if (!businessResponse.success()) {
                        quotationIdList.add(quotationPO.getId());
                    }
                });

        return quotationIdList;
    }

    /**
     * 插入报价单定时任务记录数据
     *
     * @param quotationList 报价单列表
     * @param userMap       用户map
     */
    private void insertQuotationTimedPushLogData(List<QuotationPO> quotationList, Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userMap) {
        logger.info("insert quotation timed push log data.");

        // 判断报价单列表是否为空
        if (CollUtil.isEmpty(quotationList)) {
            return;
        }

        // 插入记录
        List<QuotationTimedPushLogPO> quotationTimedPushLogList = quotationList
                .stream()
                .map(quotationPO
                        -> {
                    QuotationTimedPushLogPO quotationTimedPushLogPO = QuotationTimedPushLogPO
                            .builder()
                            .quotationId(quotationPO.getId())
                            .pushPlatform(TimedPushLogPushPlatformConstant.DING_DING)
                            .pushType(QuotationTimePushLogPushTypeConstant.NOT_TRANSFER_ORDER)
                            .pusher(JSON.toJSONString(userMap.get(quotationPO.getId())))
                            .pushTemplateId(templateId)
                            .build();
                    quotationTimedPushLogPO.setCreatedDate(LocalDateTime.now());
                    quotationTimedPushLogPO.setCreatedBy(FaDocMarketingCmsConstant.ADMIN_STRING);
                    quotationTimedPushLogPO.setId(UUIDUtils.getStringUUID());

                    return quotationTimedPushLogPO;
                })
                .collect(Collectors.toList());

        quotationTimedPushLogMapper.saveBatch(quotationTimedPushLogList);
    }
}
