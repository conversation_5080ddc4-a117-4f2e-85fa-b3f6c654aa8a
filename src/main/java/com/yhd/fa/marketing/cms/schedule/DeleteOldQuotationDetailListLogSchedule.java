package com.yhd.fa.marketing.cms.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.mapper.ExamineQuotationDetailListMapper;
import com.yhd.fa.marketing.cms.pojo.po.ExamineQuotationDetailListPO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: DeleteOldQuotationDetailListLogSchedule.java, v0.1 2023/6/26 14:28 yehuasheng Exp $
 */
@Component
class DeleteOldQuotationDetailListLogSchedule {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(DeleteOldQuotationDetailListLogSchedule.class.getName());

    /**
     * 审核后的旧报价单明细数据mapper
     */
    @Resource
    private ExamineQuotationDetailListMapper examineQuotationDetailListMapper;

    /**
     * 删除审核后的旧报价单明细数据
     */
    @XxlJob("deleteOldQuotationDetailList")
    public void deleteOldQuotationDetailList() {
        logger.info("schedule delete quotation detail list for db fa_examine_quotation_detail_list.");

        // 设置当前时间
        LocalDateTime nowTime = LocalDateTime.now();

        // 设置查询语句
        LambdaQueryWrapper<ExamineQuotationDetailListPO> lambdaQueryWrapper = new LambdaQueryWrapper<ExamineQuotationDetailListPO>()
                .ge(ExamineQuotationDetailListPO::getCreatedDate, nowTime.minusMonths(CommonConstant.SIX))
                .le(ExamineQuotationDetailListPO::getCreatedDate, nowTime.minusMonths(CommonConstant.THREE));
        examineQuotationDetailListMapper.remove(lambdaQueryWrapper);

        logger.info("delete old quotation detail list data success.");
    }
}
