package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.common.util.CommonConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/17 9:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单分页列表数据获取")
public class FbQuotationPageRequestVO extends PageRequestVO {

    @Schema(description = "员工工号")
    private String empNo;

    @Schema(description = "企业编码集合")
    private List<String> companyCodeList;

    @Schema(description = "报价单id集合")
    private List<String> quotationIdList;

    @Schema(description = "报价单号")
    private String quotationNumber;

    @Schema(description = "报价状态")
    private String quotationStatus;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "制单开始时间",example = "2024-06-18")
    private String startDate;

    @Schema(description = "制单结束时间",example = "2024-06-18")
    private String endDate;

    @Schema(description = "是否转单")
    private String transferOrder;

    @Schema(description = "公司归属 DGYHD/SZYHD")
    private String ownershipCompany;

    @Schema(description = "部门名称")
    private String unitName;

    @Schema(description = "报价时效 4/8/24")
    private String quotationAgeing;

    /**
     * 日期后面加上23:59:59.*********
     * 例:2022-12-19  变成 2022-12-19 23:59:59.*********
     */
    public void parseDate() {
        if ( StringUtils.isNotBlank(endDate)) {
            this.endDate = String.join(CommonConstant.SINGLE_SPACE,endDate, LocalTime.MAX.toString());
        }
    }
}
