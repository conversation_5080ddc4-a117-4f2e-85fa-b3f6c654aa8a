package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@TableName("fa_order_after_sale")
@EqualsAndHashCode(callSuper = true)
public class OrderAfterSalePO extends BaseEntity {

    /**
     * 售后单号
     */
    private String afterSaleNumber;

    /**
     * 售后单类型 线上：shop；线下：offline
     */
    private String afterSaleOrderType;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单支付方式
     * unknown 暂时未知的支付方式
     * aliPay 支付宝、
     * weChatPay 微信支付、
     * bankTransfer 银行转账、
     * unionPay银联、
     * monthlyKnot 月结、
     * monthlyKnot30 月结30天、
     * monthlyKnot60 月结60天、
     * monthlyKnot90 月结90天
     */
    private String orderPaymentType;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * erp编码
     */
    private String erpCompanyCode;

    /**
     * 是否收到货
     */
    private String shipmentReceiptStatus;

    /**
     * 售后类型：退货退款 refund；换货 exchange；维修 repair
     */
    private String afterSaleType;

    /**
     * 图片路径
     */
    private String imagePath;

    /**
     * 状态：待审核 pendingAudit；
     * 待寄回商品 pendingReturn；
     * 寄回商品待检测 pendingCheck；
     * 待退款 pendingRefund；
     * 待发货 pendingShip；
     * 待收货 pendingReceive；
     * 待上门维修 pendingRepair；
     * 售后完成 completed；
     * 售后关闭 closed；
     * 用户取消 cancel
     * 退款中 refunding
     *
     */
    private String afterSaleStatus;

    /**
     * 申请理由
     */
    private String suggestReason;

    /**
     * 问题描述
     */
    private String problemDescription;

    /**
     * 售后价格
     */
    private BigDecimal afterSalePrice;

    /**
     * 扣除积分
     */
    private Integer deductionPoints;

    /**
     * ERP同步状态 wait等待同步 success成功 fail失败
     */
    private String synchronizationStatus;

    /**
     * 推送给erp的报错信息
     */
    private String synchronizationErrorMsg;

    /**
     * 处理负责人
     */
    private String handlerPersonName;

    /**
     * 处理负责人联系方式
     */
    private String handlerPersonPhone;

    /**
     * 处理说明
     */
    private String handlerDescription;

    /**
     * 处理方案
     */
    private String treatmentPlan;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请人电话
     */
    private String applicantPhone;

    /**
     * 申请人邮箱
     */
    private String applicantEmail;

    /**
     * 维修方式: 上门维修 doorToDoorRepair; 返厂维修 factoryRepair
     */
    private String repairMethod;

    /**
     * 维修工程师名称
     */
    private String maintenanceEngineer;

    /**
     * 维修工程师联系方式
     */
    private String maintenanceEngineerContact;

    /**
     * 所属地区 DGYHD东莞 SZYHD苏州
     */
    private String territory;

    /**
     * 用户寄回商品快递公司名称
     */
    private String userReturnCourier;

    /**
     * 用户寄回商品快递单号
     */
    private String userReturnTrackingNumber;

    /**
     * 寄回公司名称(怡合达收货地址/联系方式)
     */
    private String returnCompanyName;

    /**
     * 寄回收货人(怡合达收货地址/联系方式)
     */
    private String returnRecipient;

    /**
     * 寄回地址(怡合达收货地址/联系方式)
     */
    private String returnAddress;

    /**
     * 寄回联系电话(怡合达收货地址/联系方式)
     */
    private String returnContactPhone;

    /**
     * 拒绝理由
     */
    private String rejectReason;

    /**
     * 是否已评论 true 已评论 false 未评论
     */
    private String replyStatus;

    /**
     * 售后明细
     */
    @TableField(exist = false)
    @EntityMapping(thisField = "id", joinField = "afterSaleId")
    private List<OrderAfterSaleListPO> orderAfterSaleList;
}

