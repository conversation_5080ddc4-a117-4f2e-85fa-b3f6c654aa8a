package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: SynchronizationCreateOrderDataDTO.java, v0.1 2023/1/27 14:42 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SynchronizationCreateOrderDataDTO extends BaseDTO {
    /**
     * 订单id
     */
    private String id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单等级
     */
    private String orderLevel;

    /**
     * 客户单号
     */
    private String customerNumber;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * erp客户编码
     */
    private String erpCustomerCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 结算方式 online线上 offline线下
     */
    private String settlementType;

    /**
     * 支付状态 paid已支付 unpaid未支付
     */
    private String paymentStatus;

    /**
     * 支付类型 unknown 暂时未知的支付方式 aliPay 支付宝、weChatPay 微信支付、bankTransfer 银行转账、unionPay银联
     */
    private String paymentType;

    /**
     * 支付流水号
     */
    private String tradeNo;

    /**
     * 支付回调商户单号
     */
    private String mchOrderNo;

    /**
     * 支付日期
     */
    private LocalDateTime payDate;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 应付金额
     */
    private BigDecimal payablePrice;

    /**
     * 同步状态 wait等待同步 success成功同步 fail同步失败
     */
    private String synchronizeStatus;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 收寄件人
     */
    private String consignee;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String town;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 市id
     */
    private Long cityId;

    /**
     * 区id
     */
    private Long townId;

    /**
     * 地址id
     */
    private String addressId;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 删除状态 recycleBin回收站 false正常 true已删除
     */
    private String deleteStatus;

    /**
     * 取消状态 false没有取消 true已取消
     */
    private String cancelStatus;

    /**
     * 订单状态 unpaid 待支付、untreated 待确认、pendingDelivery 待发货、 cancelling 取消中、takeDelivered 待收货、finish 已完成、cancel 已取消、closed已关闭
     */
    private String orderStatus;

    /**
     * 配送方式 inBatches分批发货、merge合并一起
     */
    private String shipping;

    /**
     * 首批发货商品的发货日
     */
    private Integer firstShippingDay;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下单来源 cart购物车、buy一键购买、quotation报价单
     */
    private String orderSource;

    /**
     * 下单渠道 pc端 wap手机端 weChat微信 app端 applet小程序
     */
    private String orderChannel;

    /**
     * 下单ip
     */
    private String ipLocation;

    /**
     * 订单所属地址 DGYHD东莞 SZYHD苏州
     */
    private String territory;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 建单人
     */
    private String userName;

    /**
     * 订单明细
     */
    private List<SynchronizationCreateOrderDetailDataDTO> orderDetails;
}
