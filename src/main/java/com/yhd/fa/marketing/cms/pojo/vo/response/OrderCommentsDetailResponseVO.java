package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.fa.marketing.cms.pojo.dto.OrderCommentsProblemCategoriesDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsDetailResponseVO.java, v0.1 2023/2/24 19:43 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderCommentsDetailResponseVO extends OrderCommentsResponseVO {

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "F123456")
    private String companyCode;

    /**
     * 公司名称
     */
    @Schema(description = "公司名称", example = "神舟企业")
    private String companyName;

    /**
     * 客户文字评论内容
     */
    @Schema(description = "客户文字评论内容", example = "你猜我评论了啥")
    private String comment;

    /**
     * 客户上传的图片， 多个图片以,号隔开
     */
    @Schema(description = "客户上传的图片， 多个图片以,号隔开", example = "https://xxxx.jpg, https://zzzz.jps")
    private String imageUrl;

    /**
     * 客户评论获取的赠送积分
     */
    @Schema(description = "客户评论获取的赠送积分", example = "10")
    private Integer bonusPoints;

    /**
     * 管理员回复的内容
     */
    @Schema(description = "管理员回复的内容", example = "是不是真的那样评论的")
    private String reply;

    /**
     * 回复的时间
     */
    @Schema(description = "回复的时间", example = "2022-01-01 10:10:10")
    private LocalDateTime repliedDate;

    /**
     * 回复人
     */
    @Schema(description = "回复人", example = "你好回复人")
    private String replyPeople;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "已生成工单")
    private String remark;

    /**
     * 问题分类列表
     */
    List<OrderCommentsProblemCategoriesDTO> categories;
}
