package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: SendMessageStationMailRequestVO.java, v0.1 2023/3/6 17:00 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendMessageToStationMailRequestVO extends BaseVO {
    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 发送人用户ID
     */
    private String senderId;

    /**
     * 接收人用户ID
     */
    private String receiverId;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 模板参数替换
     */
    private transient Map<String, Object> map = new HashMap<>();
}
