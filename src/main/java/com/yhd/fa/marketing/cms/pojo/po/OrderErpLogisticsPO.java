package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_erp_logistics")
public class OrderErpLogisticsPO extends BaseEntity {

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 出库单号
     */
    private String deliveryNumber;

    /**
     * 类型
     */
    private String targetType;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 出库时间
     */
    private LocalDateTime deliveryTime;

    /**
     * received 已签收 notSigned 未签收
     */
    private String signingStatus;

    /**
     * 发货地 DG 东莞 SZ 苏州 SD 山东 HB 湖北
     */
    private String origin;
}

