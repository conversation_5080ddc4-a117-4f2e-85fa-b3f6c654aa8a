package com.yhd.fa.marketing.cms.service.impl.verification.quotation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yhd.buc.cms.api.sdk.enums.FaAuthEnum;
import com.yhd.buc.cms.api.sdk.enums.FaRoleEnum;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.ResourcesInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CreateQuotationDetailRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CreateQuotationRequestVO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: CreateQuotationVerification.java, v0.1 2023/1/4 10:14 yehuasheng Exp $
 */
@Component
@RefreshScope
public class CreateQuotationVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(CreateQuotationVerification.class.getName());

    /**
     * 客户报价单的长度
     */
    @Value("${quotation.customer-quotation-code-length:60}")
    private int customerQuotationCodeLength;

    /**
     * 物料编码长度
     */
    @Value("${quotation.material-code-length:255}")
    private int materialCodeLength;

    /**
     * 用户的服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 检查创建报价单的参数
     *
     * @param createQuotationRequestVO 创建报价单的参数
     * @return BusinessResponse<AllUserInfoResponseVO>
     */
    public BusinessResponse<CompanyAndUserAndMerchandiserAndResourcesResponseVO> check(CreateQuotationRequestVO createQuotationRequestVO) {
        logger.info("start faDoc marketing cms check create quotation check parameter createQuotationRequestVO:{}", createQuotationRequestVO);

        // 获取用户信息
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(createQuotationRequestVO.getUserCode(), null);
        if (ObjectUtil.isEmpty(userInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.USER_IS_EMPTY);
        }

        // 个人用户不能创建报价单 认证企业中没有拉取普通企业的用户
        if (StringUtils.equals(userInfo.getUserRole().getRoleCode(), FaRoleEnum.YHD_FA_USER_GE_REN_YONG_HU.getCode())
                || StringUtils.equals(userInfo.getUserRole().getRoleCode(), FaRoleEnum.YHD_FA_USER_DAI_REN_ZHENG_YONG_HU.getCode())) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.USER_LEVEL_CANNOT_CREATE_QUOTATION);
        }

        // 判断是否认证企业用户
        if (StringUtils.equals(userInfo.getUserRole().getRoleCode(), FaRoleEnum.YHD_FA_USER_REN_ZHENG_QI_YE_YONG_HU.getCode())
                // 并且不是管理员
                && StringUtils.equals(userInfo.getUserInfo().getAdmin(), CommonConstant.FALSE)
                // 并且没有采购权限
                && !UserUtil.haveCreateOrderAuthority(userInfo)
                // 并且没有传采购人编码 或者采购人没有采购权限
                && (StringUtils.isBlank(createQuotationRequestVO.getPurchaseUserCode())
                || !checkPurchasingUserCodeProcurementAuthority(createQuotationRequestVO.getPurchaseUserCode()))) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.USER_NOT_CREATE_ORDER_AUTH_AND_PURCHASE_USER_IS_NULL);
        }

        // 判断企业是否有询价权限
        if (UserUtil.haveNotEnquiryPriceAuthority(userInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.COMPANY_CANNOT_ENQUIRY_PRICE_AUTHORITY);
        }

        // 判断企业是否有快速报价权限
        if (UserUtil.haveNotCreateQuotationAuthority(userInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.COMPANY_CANNOT_CREATE_QUOTATION_AUTHORITY);
        }

        // 校验客户采购单号长度是否大于60
        if (StringUtils.isNotBlank(createQuotationRequestVO.getCustomerQuotationCode()) &&
                createQuotationRequestVO.getCustomerQuotationCode().length() > customerQuotationCodeLength) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_CUSTOMER_QUOTATION_ID_IS_TOO_LONG);
        }

        // 校验型号参数
        return checkProductsParameter(userInfo, createQuotationRequestVO.getProducts());
    }

    /**
     * 检查型号参数
     *
     * @param userInfo 用户信息
     * @param products 产品信息
     * @return BusinessResponse<AllUserInfoResponseVO>
     */
    private BusinessResponse<CompanyAndUserAndMerchandiserAndResourcesResponseVO> checkProductsParameter(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo, List<CreateQuotationDetailRequestVO> products) {
        logger.info("check create quotation product.");

        for (CreateQuotationDetailRequestVO createQuotationDetailRequestVO : products) {
            // 检查数量是否少于1
            if (createQuotationDetailRequestVO.getQuantity() < CommonConstant.ONE) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUANTITY_LESS_ONE);
            }

            // 判断型号是否为空
            if (StringUtils.isBlank(createQuotationDetailRequestVO.getCustomerModel())) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_MODEL_IS_NULL);
            }

            // 判断物料编码长度是否大于255
            if (StringUtils.isNotBlank(createQuotationDetailRequestVO.getMaterialCode()) && createQuotationDetailRequestVO.getMaterialCode().length() > materialCodeLength) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_MATERIAL_CODE_IS_TOO_LONG.getCode(),
                        MessageFormatter.format(FaDocMarketingResponseEnum.QUOTATION_MATERIAL_CODE_IS_TOO_LONG.getDesc(),
                                createQuotationDetailRequestVO.getMaterialCode()).getMessage());
            }
        }

        return BusinessResponseCommon.ok(userInfo);
    }

    /**
     * 检查采购人是否有采购权限
     *
     * @param userCode 用户编码
     * @return boolean
     */
    private boolean checkPurchasingUserCodeProcurementAuthority(String userCode) {
        logger.info("check purchasing user code procurement authority.");

        // 获取用户信息
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = Arrays.stream(userCode.split(CommonConstant.SYMBOL_COMMA)).map(code -> UserCodeAndCompanyCodeDTO.builder().userCode(code).build()).collect(Collectors.toList());
        Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userAndCompanyBaseInfo = userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);
        if (userAndCompanyBaseInfo.containsKey(userCode)) {
            CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userAndCompanyBaseInfo.get(userCode);

            if (CollUtil.isEmpty(userInfo.getResourcesCodeClosedList())) {
                return false;
            }

            // 提取禁用的权限
            return !userInfo.getResourcesCodeClosedList().stream().map(ResourcesInfo::getResourcesCode).collect(Collectors.toList()).contains(FaAuthEnum.YHD_FA_CHUANG_JIAN_DING_DAN.getCode());
        }

        return false;
    }
}
