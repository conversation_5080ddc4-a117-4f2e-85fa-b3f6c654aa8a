package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: CustomerCompanyListResponseVO.java, v0.1 2023/4/12 12:03 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerCompanyListResponseVO extends BaseVO {
    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "测试企业")
    private String companyName;

    /**
     * 企业编码
     */
    @Schema(description = "电商的企业编码", example = "BC123456")
    private String companyCode;

    /**
     * erp企业编码
     */
    @Schema(description = "erp企业编码", example = "A001")
    private String erpCompanyCode;
}
