package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderListClickHousePO.java, v 0.1 2025/3/20 09:54 JiangYuHong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_list")
public class OrderListClickHousePO extends BaseEntity {
    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单明细id
     */
    private int sortId;

    /**
     * 报价单号
     */
    private String quotationNumber;

    /**
     * 报价单明细id
     */
    private Integer quotationSortId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 客户型号
     */
    private String customerModel;

    /**
     * 客户物料号
     */
    private String customerMaterialCode;

    /**
     * 客户产品名称
     */
    private String customerProductName;

    /**
     * 长度
     */
    private BigDecimal modelLong;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 是否标准 true 标准 false 非标准
     */
    private String isStandard;

    /**
     * 原单价
     */
    private BigDecimal price;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣后含税单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总金额
     */
    private BigDecimal totalPrice;

    /**
     * 实付总金额
     */
    private BigDecimal payablePrice;

    /**
     * 数量折扣
     */
    private BigDecimal quantityDiscount;

    /**
     * 总折扣
     */
    private BigDecimal totalDiscount;

    /**
     * 交期
     */
    private Integer delivery;

    /**
     * 发货日期
     */
    private LocalDateTime shipDate;

    /**
     * 产品单位
     */
    private String unit;

    /**
     * 预计发货日期
     */
    private LocalDateTime estimatedShippingDate;

    /**
     * 回复交期（订单延期时会有）
     */
    private LocalDateTime replyToDelivery;

    /**
     * 最后发货日期
     */
    private LocalDateTime deliveryDate;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 计算要求
     */
    private String technicalSpecifications;

    /**
     * 明细状态  unpaid 待支付、untreated 待确认、confirm 已确认、stockUp 备货中、takeDelivered 待收货、finish 已完成、cancelling 取消中、cancel 已取消、closed 已关闭
     */
    private String orderDetailStatus;

    /**
     * 发货通知 true 已通知 false 未通知
     */
    private String deliveredNotice;

    /**
     * 怡合达备注
     */
    private String examineRemark;

    /**
     * 备注
     */
    private String remark;
}
