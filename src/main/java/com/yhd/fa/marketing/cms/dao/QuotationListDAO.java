package com.yhd.fa.marketing.cms.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version Id: QuotationListDAO.java, v0.1 2022/12/6 10:18 yehuasheng Exp $
 */
@Repository("faQuotationList")
@DS("quotation")
public interface QuotationListDAO extends MPJBaseMapper<QuotationListPO> {
}
