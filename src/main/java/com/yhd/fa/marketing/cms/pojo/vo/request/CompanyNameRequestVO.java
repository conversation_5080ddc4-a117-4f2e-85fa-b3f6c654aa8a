package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version Id: CompanyNameRequestVO.java, v0.1 2023/4/12 11:38 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CompanyNameRequestVO extends BaseVO {
    /**
     * 企业名称
     */
    @Schema(description = "企业名称，至少3个字", example = "怡合达", required = true)
    @Length(min = 3, message = "企业名称至少3个字")
    private String companyName;
}
