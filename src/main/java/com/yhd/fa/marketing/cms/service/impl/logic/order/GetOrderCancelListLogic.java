package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.enums.OrderCancelStatusEnum;
import com.yhd.fa.marketing.cms.enums.OrderCancelTypeEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCancelMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCancelPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCancelRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.SqlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderCancelListLogic.java, v0.1 2023/2/22 10:08 yehuasheng Exp $
 */
@Component
public class GetOrderCancelListLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderCancelListLogic.class.getName());

    /**
     * 订单取消mapper
     */
    @Resource
    private OrderCancelMapper orderCancelMapper;

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    public BusinessResponse<PageInfo<OrderCancelResponseVO>> exec(OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("start exec get order cancel list logic.");

        // 设置分页
        PageMethod.startPage(orderCancelRequestVO.getPageNum(), orderCancelRequestVO.getPageSize());

        // 设置查询的条件
        MPJLambdaWrapper<OrderCancelPO> queryWrapper = setQueryWrapper(orderCancelRequestVO);

        // 查询订单取消集合
        List<OrderCancelResponseVO> orderCancelList = orderCancelMapper.selectJoinList(OrderCancelResponseVO.class, queryWrapper);

        // 设置分页
        PageInfo<OrderCancelResponseVO> pageInfo = new PageInfo<>(orderCancelList);

        // 设置其他参数值
        setOrderCancelOtherValue(pageInfo.getList());

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 设置查询条件以及查询字段
     *
     * @param orderCancelRequestVO 请求参数
     * @return MPJLambdaWrapper<OrderCancelPO>
     */
    private MPJLambdaWrapper<OrderCancelPO> setQueryWrapper(OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("set order cancel query wrapper.");

        MPJLambdaWrapper<OrderCancelPO> queryWrapper = OrderUtil.setOrderCancelQueryWrapperSelectAs()
                .selectAs(OrderCancelPO::getIsInsideCreated, OrderCancelResponseVO::getIsInsideCreated)
                .selectAs(OrderCancelPO::getInsideEmployeeCode, OrderCancelResponseVO::getInsideEmployeeCode)
                .selectAs(OrderCancelPO::getCreatedBy, OrderCancelResponseVO::getInsideEmployeeName)
                .selectAs(OrderCancelPO::getSyncStatus, OrderCancelResponseVO::getSyncStatus)
                .selectAs(OrderCancelPO::getSyncErrMsg, OrderCancelResponseVO::getSyncErrMsg)
                .selectAs(OrderCancelPO::getOrderId, OrderCancelResponseVO::getOrderId)
                .orderByDesc(OrderCancelPO::getCreatedDate);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderCancelPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 判断是否有设置时间
        setTimeQueryWrapper(queryWrapper, orderCancelRequestVO);

        // 查询订单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderCancelRequestVO.getOrderNumber(), queryWrapper, OrderCancelPO::getOrderNumber);

        // 查询订单取消单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderCancelRequestVO.getOrderCancelNumber(), queryWrapper, OrderCancelPO::getCancelNumber);

        // 查询订单取消状态
        setOrderCancelStatusQueryWrapper(queryWrapper, orderCancelRequestVO);

        // 查询企业名称
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderCancelRequestVO.getCompanyName(), queryWrapper, OrderCancelPO::getCompanyName);

        // 内部人员工号
        setInsideEmployeeCodeQueryWrapper(queryWrapper, orderCancelRequestVO);

        // 订单取消类型
        setOrderCancelTypeQueryWrapper(queryWrapper, orderCancelRequestVO);

        return queryWrapper;
    }

    /**
     * 设置时间
     *
     * @param queryWrapper         查询条件
     * @param orderCancelRequestVO 请求参数
     */
    private void setTimeQueryWrapper(MPJLambdaWrapper<OrderCancelPO> queryWrapper, OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("get order cancel list set query wrapper time.");

        // 开始时间
        if (ObjectUtil.isNotNull(orderCancelRequestVO.getStartDateTime())) {
            queryWrapper.ge(OrderCancelPO::getCreatedDate, orderCancelRequestVO.getStartDateTime());
        }

        // 结束时间
        if (ObjectUtil.isNotNull(orderCancelRequestVO.getEndDateTime())) {
            queryWrapper.le(OrderCancelPO::getCreatedDate, orderCancelRequestVO.getEndDateTime());
        }
    }

    /**
     * 查询取消订单状态
     *
     * @param queryWrapper         查询条件
     * @param orderCancelRequestVO 请求参数
     */
    private void setOrderCancelStatusQueryWrapper(MPJLambdaWrapper<OrderCancelPO> queryWrapper, OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("get order cancel list set query wrapper order cancel status.");

        if (StringUtils.isNotBlank(orderCancelRequestVO.getOrderCancelStatus())) {
            queryWrapper.eq(OrderCancelPO::getCancelStatus, orderCancelRequestVO.getOrderCancelStatus());
        }
    }

    /**
     * 查询内部员工号状态
     *
     * @param queryWrapper         查询条件
     * @param orderCancelRequestVO 请求参数
     */
    private void setInsideEmployeeCodeQueryWrapper(MPJLambdaWrapper<OrderCancelPO> queryWrapper, OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("get order cancel list set query wrapper inside employee code.");

        if (StringUtils.isNotBlank(orderCancelRequestVO.getInsideEmployeeCode())) {
            queryWrapper.eq(OrderCancelPO::getInsideEmployeeCode, orderCancelRequestVO.getInsideEmployeeCode().trim());
        }
    }

    /**
     * 查询订单取消类型
     *
     * @param queryWrapper         查询条件
     * @param orderCancelRequestVO 请求参数
     */
    private void setOrderCancelTypeQueryWrapper(MPJLambdaWrapper<OrderCancelPO> queryWrapper, OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("get order cancel list set query wrapper order cancel type.");

        if (StringUtils.isNotBlank(orderCancelRequestVO.getOrderCancelType())) {
            queryWrapper.eq(OrderCancelPO::getCancelType, orderCancelRequestVO.getOrderCancelType());
        }
    }

    /**
     * 设置订单取消的其他值
     *
     * @param orderCancelList 订单取消列表
     */
    private void setOrderCancelOtherValue(List<OrderCancelResponseVO> orderCancelList) {
        logger.info("set order cancel other value.");

        if (CollUtil.isNotEmpty(orderCancelList)) {
            // 获取用户信息
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userBaseMap = getUserBaseInfo(orderCancelList);

            // 设置取消订单状态名称
            Map<String, String> orderCancelStatusNameMap = Arrays.stream(OrderCancelStatusEnum.values()).collect(Collectors.toMap(OrderCancelStatusEnum::getOrderCancelStatus, OrderCancelStatusEnum::getOrderCancelStatusName));

            // 设置取消订单类型名称
            Map<String, String> orderCancelTypeNameMap = Arrays.stream(OrderCancelTypeEnum.values()).collect(Collectors.toMap(OrderCancelTypeEnum::getOrderCancelType, OrderCancelTypeEnum::getOrderCancelTypeName));

            orderCancelList.forEach(orderCancelResponseVO -> {
                // 设置用户信息
                Optional.ofNullable(userBaseMap.get(orderCancelResponseVO.getUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
                    // 设置用户名称
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getUserInfo()).ifPresent(baseUserInfoResponseVO -> orderCancelResponseVO.setUserName(baseUserInfoResponseVO.getUserName()));
                    // 设置跟单以及联系方式
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserResponseVO -> {
                        orderCancelResponseVO.setMerchandiserContact(merchandiserResponseVO.getMobile());
                        orderCancelResponseVO.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
                    });
                    // 设置业务员以及联系方式
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> {
                        orderCancelResponseVO.setSalesmanName(salesMan.getMobile());
                        orderCancelResponseVO.setSalesmanContact(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
                    });
                });

                // 设置取消订单状态名称
                orderCancelResponseVO.setOrderCancelStatusName(orderCancelStatusNameMap.get(orderCancelResponseVO.getOrderCancelStatus()));

                // 设置取消订单类型名称
                orderCancelResponseVO.setOrderCancelTypeName(orderCancelTypeNameMap.get(orderCancelResponseVO.getOrderCancelType()));
            });
        }
    }

    /**
     * 获取用户信息
     *
     * @param orderCancelList 订单取消列表
     * @return Map<String, BaseUserInfoResponseVO>
     */
    private Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserBaseInfo(List<OrderCancelResponseVO> orderCancelList) {
        logger.info("get order list user info.");

        // 提取订单列表中的用户编码
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = orderCancelList
                .stream()
                .map(orderCancelResponseVO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .companyCode(orderCancelResponseVO.getCompanyCode())
                        .userCode(orderCancelResponseVO.getUserCode())
                        .build())
                .collect(Collectors.toList());

        // 获取用户信息map
        return userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);
    }
}
