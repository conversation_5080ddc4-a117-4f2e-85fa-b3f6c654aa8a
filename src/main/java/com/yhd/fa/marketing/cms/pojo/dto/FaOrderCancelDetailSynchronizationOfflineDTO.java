package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: FaOrderCancelDetailSynchronizationOfflineDTO.java, v 0.1 2023/2/13 18:36 Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaOrderCancelDetailSynchronizationOfflineDTO extends BaseDTO {
    /**
     * 取消id
     */
    private String cancelId;

    /**
     * 明细id
     */
    private Integer sortId;

    /**
     * 取消数量
     */
    private Long quantity;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 状态 明细取消状态 canceling 取消中, cancel 已取消, turnDown 驳回
     */
    private String cancelDetailStatus;
}
