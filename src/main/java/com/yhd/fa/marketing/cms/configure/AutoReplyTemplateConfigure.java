package com.yhd.fa.marketing.cms.configure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: AutoReplyTemplateConfigure.java, v 0.1 2023/8/16 11:04 JiangYuHong Exp $
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "auto-reply-template")
public class AutoReplyTemplateConfigure {

    /**
     * 回复内容map
     */
    private LinkedHashMap<String, List<String>> map = new LinkedHashMap<>();

}
