package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.response.CustomerCompanyListResponseVO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: GetCompanyListByCompanyNameLogic.java, v0.1 2023/4/12 11:54 yehuasheng Exp $
 */
@Component
public class GetCompanyListByCompanyNameLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetCompanyListByCompanyNameLogic.class.getName());

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 查询企业信息
     *
     * @param companyName 企业名称
     * @return BusinessResponse<List < CustomerCompanyListResponseVO>>
     */
    public BusinessResponse<List<CustomerCompanyListResponseVO>> exec(String companyName) {
        logger.info("start exec get company list by company name logic.");

        // 执行查询
        return BusinessResponse.ok(userBucCmsService.searchCompanyListByCompanyName(companyName));
    }
}
