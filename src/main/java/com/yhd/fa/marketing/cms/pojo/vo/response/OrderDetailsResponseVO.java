package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderDetailsResponseVO.java, v0.1 2023/2/20 9:54 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderDetailsResponseVO extends BaseVO {
    /**
     * 订单明细id
     */
    @Schema(description = "订单明细id", example = "14f7a6600b3a43f5a5b7dc9752303f63")
    private String orderDetailId;

    /**
     * 订单序号
     */
    @Schema(description = "订单序号", example = "1")
    private int orderSortId;

    /**
     * 报价单号
     */
    @Schema(description = "报价单号", example = "YI202212011750039779B1WU")
    private String quotationNumber;

    /**
     * 报价单序号
     */
    @Schema(description = "报价单序号", example = "1")
    private Integer quotationSortId;

    /**
     * 怡合达型号
     */
    @Schema(description = "怡合达型号", example = "SAD01-D3-L100")
    private String productModel;

    /**
     * 代码
     */
    @Schema(description = "产品代码", example = "SAD01")
    private String productCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "导向轴")
    private String productName;

    /**
     * 一级分类编码
     */
    @Schema(description = "一级分类编码", example = "A")
    private String typeCode;

    /**
     * 二级分类编码
     */
    @Schema(description = "二级分类编码", example = "A01")
    private String catCode;

    /**
     * 系列编码
     */
    @Schema(description = "系列编码", example = "SAD01-22")
    private String goodsCode;

    /**
     * 客户型号
     */
    @Schema(description = "客户型号", example = "SDW0244")
    private String customerModel;

    /**
     * 客户物料编码
     */
    @Schema(description = "客户物料编码", example = "123456789")
    private String customerMaterialCode;

    /**
     * 客户产品名称
     */
    @Schema(description = "客户产品名称", example = "导向轴")
    private String customerProductName;

    /**
     * 数量
     */
    @Schema(description = "数量", example = "2")
    private long quantity;

    /**
     * 发货数量
     */
    @Schema(description = "发货数量", example = "1")
    private long deliveryQuantity;

    /**
     * 是否标准型号 true是 false不是
     */
    @Schema(description = "是否标准型号 true是 false不是", example = "true")
    private String isStandard;

    /**
     * 原单价
     */
    @Schema(description = "原单价", example = "10.0")
    private BigDecimal price;

    /**
     * 折扣单价
     */
    @Schema(description = "折扣单价", example = "8.00")
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价", example = "9.04")
    private BigDecimal taxDiscountPrice;

    /**
     * 合计总价
     */
    @Schema(description = "合计总价", example = "18.08")
    private BigDecimal totalPrice;

    /**
     * 应付合计总价
     */
    @Schema(description = "应付合计总价", example = "18.08")
    private BigDecimal payablePrice;

    /**
     * 数量折扣
     */
    @Schema(description = "数量折扣", example = "1.0")
    private BigDecimal quantityDiscount;

    /**
     * 总折扣
     */
    @Schema(description = "总折扣", example = "0.8")
    private BigDecimal totalDiscount;

    /**
     * 交期
     */
    @Schema(description = "交期", example = "1")
    private Integer delivery;

    /**
     * 发货日期
     */
    @Schema(description = "发货日期", example = "2022-01-01 10:10:10")
    private LocalDateTime shipDate;

    /**
     * 单位
     */
    @Schema(description = "单位", example = "PCS")
    private String unit;

    /**
     * 预计发货日期
     */
    @Schema(description = "预计发货日期", example = "2022-01-01 10:10:10")
    private LocalDateTime estimatedShippingDate;

    /**
     * 回复交期（订单延期时会有）
     */
    @Schema(description = "回复交期（订单延期时会有）", example = "2022-01-01 10:10:10")
    private LocalDateTime replyToDelivery;

    /**
     * 附件
     */
    @Schema(description = "附件", example = "http://xxxxx.jpg")
    private String fileUrl;

    /**
     * 明细状态 unpaid 待支付、untreated 待确认、confirm 已确认、stockUp 备货中、takeDelivered 待收货、finish 已完成、cancelling 取消中、cancel 已取消、closed 已关闭
     */
    @Schema(description = "明细状态 unpaid 待支付、untreated 待确认、confirm 已确认、stockUp 备货中、takeDelivered 待收货、finish 已完成、cancelling 取消中、cancel 已取消、closed 已关闭", example = "finish")
    private String orderDetailStatus;

    /**
     * 明细状态名称
     */
    @Schema(description = "明细状态名称", example = "已完成")
    private String orderDetailStatusName;

    /**
     * 怡合达备注
     */
    @Schema(description = "怡合达备注", example = "呵呵 你猜")
    private String examineRemark;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "哈哈 我不猜")
    private String remark;

    /**
     * 优惠类型  none 没有使用 promotion 促销 coupon 优惠券 both 都使用
     */
    @Schema(description = "优惠类型  none 没有使用 promotion 促销 coupon 优惠券 both 都使用", example = "promotion")
    private String discountType;

    /**
     * 折扣单价（原）
     */
    @Schema(description = "折扣单价（原）", example = "8.00")
    private BigDecimal originalDiscountPrice;

    /**
     * 折扣后含税单价（原）
     */
    @Schema(description = "折扣后含税单价（原）", example = "9.04")
    private BigDecimal originalTaxDiscountPrice;

    /**
     * 含税总金额（原）
     */
    @Schema(description = "含税总金额（原）", example = "18.08")
    private BigDecimal originalTotalPrice;

    /**
     * 明细项优惠金额
     */
    @Schema(description = "明细项优惠金额", example = "1.0")
    private BigDecimal detailDiscountPrice;
}
