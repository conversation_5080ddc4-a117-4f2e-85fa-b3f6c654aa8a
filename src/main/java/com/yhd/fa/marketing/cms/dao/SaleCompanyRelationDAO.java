package com.yhd.fa.marketing.cms.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogDAO.java, v0.1 2022/12/6 10:26 yehuasheng Exp $
 */
@Repository("saleCompanyRelation")
public interface SaleCompanyRelationDAO extends MPJBaseMapper<SaleCompanyRelationPO> {

    /**
     * 清空表操作，谨慎调用
     */
    @Update("TRUNCATE TABLE `sale_company_relation`")
    void truncateMysqlData();

    /**
     * 清空 quotation 数据库中的表
     */
    @DS("quotation")
    @Update("TRUNCATE TABLE `sale_company_relation`")
    void truncateQuotationData();
}
