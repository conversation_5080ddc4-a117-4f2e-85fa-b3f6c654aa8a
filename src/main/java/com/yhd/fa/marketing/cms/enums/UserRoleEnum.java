package com.yhd.fa.marketing.cms.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: UserRoleEnum.java, v0.1 2023/1/5 8:58 yehuasheng Exp $
 */
@Getter
public enum UserRoleEnum {
    PER_U("perU", "个人用户"),
    ORD_U("ordCoU", "普通企业用户"),
    CERT_CO_U("certCoU", "认证企业用户"),
    ;

    private final String role;
    private final String desc;

    UserRoleEnum(String role, String desc) {
        this.role = role;
        this.desc = desc;
    }
}
