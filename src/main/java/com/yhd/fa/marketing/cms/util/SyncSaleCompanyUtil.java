package com.yhd.fa.marketing.cms.util;


import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.dao.CompanyDAO;
import com.yhd.fa.marketing.cms.pojo.dto.SaleCompanyRelationDTO;
import com.yhd.fa.marketing.cms.pojo.po.CompanyPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationClickHousePO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.po.UcSyncCustomerInfoPrivatePO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version Id: SyncSaleCompanyUtil.java, v 0.1 2025/5/13 15:26 JiangYuHong Exp $
 */
@Component
public class SyncSaleCompanyUtil {

    public static final int BATCH_SIZE = 1000;

    /**
     * 组装ClickHouse数据
     *
     * @param e SaleCompanyRelationDTO
     * @return SaleCompanyRelationClickHousePO
     */
    public static SaleCompanyRelationClickHousePO apply(SaleCompanyRelationDTO e) {
        SaleCompanyRelationClickHousePO saleCompanyRelationClickHousePO = new SaleCompanyRelationClickHousePO();
        saleCompanyRelationClickHousePO.setId(e.getId());
        saleCompanyRelationClickHousePO.setCompanyCode(Optional.ofNullable(e.getCompanyCode()).orElse(CommonConstant.EMPTY));
        saleCompanyRelationClickHousePO.setSalesNo(Optional.ofNullable(e.getSalesNo()).orElse(CommonConstant.EMPTY));
        saleCompanyRelationClickHousePO.setOwnershipCompany(Optional.ofNullable(e.getOwnershipCompany()).orElse(CommonConstant.EMPTY));
        return saleCompanyRelationClickHousePO;
    }

    /**
     * 组装Mysql数据
     *
     * @param e SaleCompanyRelationDTO
     * @return SaleCompanyRelationPO
     */
    public static SaleCompanyRelationPO applyMysql(SaleCompanyRelationDTO e) {
        SaleCompanyRelationPO saleCompanyRelationPO = new SaleCompanyRelationPO();
        saleCompanyRelationPO.setId(e.getId());
        saleCompanyRelationPO.setCompanyCode(Optional.ofNullable(e.getCompanyCode()).orElse(CommonConstant.EMPTY));
        saleCompanyRelationPO.setSalesNo(Optional.ofNullable(e.getSalesNo()).orElse(CommonConstant.EMPTY));
        saleCompanyRelationPO.setOwnershipCompany(Optional.ofNullable(e.getOwnershipCompany()).orElse(CommonConstant.EMPTY));
        return saleCompanyRelationPO;
    }

    /**
     * 查询员工对应企业信息
     *
     * @param pageNum 页码
     * @return List<SaleCompanyRelationDTO>
     */
    public static List<SaleCompanyRelationDTO> selectSaleCompanyInfoList(int pageNum, CompanyDAO companyDAO) {
        MPJLambdaWrapper<CompanyPO> wrapper = new MPJLambdaWrapper<CompanyPO>()
                .distinct()
                .leftJoin(UcSyncCustomerInfoPrivatePO.class, UcSyncCustomerInfoPrivatePO::getCustomerNo, CompanyPO::getErpCompanyCode)
                .selectAs(CompanyPO::getId, SaleCompanyRelationDTO::getId)
                .selectAs(CompanyPO::getCompanyCode, SaleCompanyRelationDTO::getCompanyCode)
                .selectAs(UcSyncCustomerInfoPrivatePO::getSalesNo, SaleCompanyRelationDTO::getSalesNo)
                .selectAs(UcSyncCustomerInfoPrivatePO::getOwnershipCompany, SaleCompanyRelationDTO::getOwnershipCompany)
                .select(CompanyPO::getCreatedDate)
                .eq(UcSyncCustomerInfoPrivatePO::getCompanyCode, "010000")
                .orderByAsc(CompanyPO::getCreatedDate);

        // 禁用逻辑删除过滤
        wrapper.disableSubLogicDel();

        wrapper.last("limit " + calculatePagination(pageNum, BATCH_SIZE));

        return companyDAO.selectJoinList(SaleCompanyRelationDTO.class, wrapper);
    }

    /**
     * 计算分页参数
     *
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return String  格式为 "offset,limit" 例如 "0,10"
     */
    public static String calculatePagination(int pageNum, int pageSize) {
        int offset = pageNum <= 1 ? 0 : (pageNum - 1) * pageSize;
        return offset + CommonConstant.SYMBOL_COMMA + pageSize;
    }
}
