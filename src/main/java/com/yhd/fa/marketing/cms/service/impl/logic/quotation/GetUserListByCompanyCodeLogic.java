package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyUserCustomResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.ResourcesInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.response.CustomerUserResponseVO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetUserListByCompanyCodeLogic.java, v0.1 2023/4/12 14:24 yehuasheng Exp $
 */
@Component
public class GetUserListByCompanyCodeLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(GetUserListByCompanyCodeLogic.class.getName());
    /**
     * 创建订单权限
     */
    public static final String CREATE_ORDER_AUTHORITY = "YHD-fa-chuang-jian-ding-dan";
    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 执行获取用户列表逻辑
     *
     * @param companyCode 企业编码
     * @return BusinessResponse<List < CustomerUserResponseVO>>
     */
    public BusinessResponse<List<CustomerUserResponseVO>> exec(String companyCode) {
        logger.info("start exec get user list by company code logic.");

        // 定义返回参数
        List<CustomerUserResponseVO> customerUserResponseVOS = new ArrayList<>();

        // 获取用户列表
        List<CompanyUserCustomResponseVO> userListByCompanyCodeList = userBucCmsService.getUserListByCompanyCode(companyCode);
        if (CollUtil.isNotEmpty(userListByCompanyCodeList)) {
            customerUserResponseVOS.addAll(
                    userListByCompanyCodeList
                            .stream()
                            .map(userListByCompanyCode
                                    -> CustomerUserResponseVO
                                    .builder()
                                    .userCode(userListByCompanyCode.getUserCode())
                                    .userName(userListByCompanyCode.getUserName())
                                    .createOrderPermission(haveCreateOrderAuthority(Optional
                                            .ofNullable(userListByCompanyCode.getResourcesCodeClosedList())
                                            .map(resourcesInfos
                                                    -> resourcesInfos
                                                    .stream()
                                                    .map(ResourcesInfo::getResourcesCode)
                                                    .collect(Collectors.toList()))
                                            .orElse(new ArrayList<>())))
                                    .build()
                            ).collect(Collectors.toList())
            );
        }

        return BusinessResponse.ok(customerUserResponseVOS);
    }

    /**
     * 判断是否有创建订单权限
     *
     * @param resourcesCodeClosedList 关闭的资源
     * @return boolean
     */
    private boolean haveCreateOrderAuthority(List<String> resourcesCodeClosedList) {
        if (CollUtil.isEmpty(resourcesCodeClosedList)) {
            return true;
        }

        return !resourcesCodeClosedList.contains(CREATE_ORDER_AUTHORITY);
    }
}
