package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version Id: UserListByCompanyCodeRequestVO.java, v0.1 2023/4/12 14:13 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserListByCompanyCodeRequestVO extends BaseVO {
    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "ABC123456", required = true)
    @NotBlank(message = "企业编码不能为空")
    private String companyCode;
}
