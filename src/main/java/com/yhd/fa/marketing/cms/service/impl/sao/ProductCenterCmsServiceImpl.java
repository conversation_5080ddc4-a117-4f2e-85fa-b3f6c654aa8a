package com.yhd.fa.marketing.cms.service.impl.sao;

import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionCategoryRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionTypeRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ProductInfoRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationCategoryResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationTypeResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProductInfoResponseVO;
import com.yhd.fa.marketing.cms.sao.ProductCenterCmsSAO;
import com.yhd.fa.marketing.cms.service.sao.ProductCenterCmsService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: ProductCenterCmsServiceImpl.java, v0.1 2022/12/19 10:56 yehuasheng Exp $
 */
@Service
public class ProductCenterCmsServiceImpl implements ProductCenterCmsService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(ProductCenterCmsServiceImpl.class.getName());

    /**
     * 产品中心cms的sao
     */
    @Resource
    private ProductCenterCmsSAO productCenterCmsSAO;

    /**
     * 获取产品分类、代码信息
     *
     * @param approveQuotationSelectionTypeRequestVO 产品分类代码的请求参数
     * @return BusinessResponse<List < ApproveQuotationTypeResponseVO>>
     */
    @Override
    public BusinessResponse<List<ApproveQuotationTypeResponseVO>> getProductTypeCode(ApproveQuotationSelectionTypeRequestVO approveQuotationSelectionTypeRequestVO) {
        logger.info("request product center cms service get product type code.");

        // 设置请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            logger.info("get product type code parameter approveQuotationSelectionTypeRequestVO:{}", approveQuotationSelectionTypeRequestVO);
            BusinessResponse<List<ApproveQuotationTypeResponseVO>> result = productCenterCmsSAO.getProductTypeCode(approveQuotationSelectionTypeRequestVO);
            logger.info("get product type code parameter for product center service success . result:{}", result);
            logger.info("get product type code parameter for product center service cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return result;
        } catch (Exception e) {
            logger.error("get product type code parameter for product center service fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(e.getMessage());
        }
    }

    /**
     * 获取产品类别
     *
     * @param approveQuotationSelectionCategoryRequestVO 请求获取产品类别的参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    @Override
    public BusinessResponse<List<ApproveQuotationCategoryResponseVO>> getProductCategoryCode(ApproveQuotationSelectionCategoryRequestVO approveQuotationSelectionCategoryRequestVO) {
        logger.info("request product center cms service get product category code.");

        // 设置请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            logger.info("get product category code parameter approveQuotationSelectionCategoryRequestVO:{}", approveQuotationSelectionCategoryRequestVO);
            BusinessResponse<List<ApproveQuotationCategoryResponseVO>> result = productCenterCmsSAO.getProductCategoryCode(approveQuotationSelectionCategoryRequestVO);
            // 如果成功去重
            if (result.success()) {
                List<String> category = new CopyOnWriteArrayList<>();
                result.getData().removeIf(approveQuotationCategoryResponseVO -> {
                    if (category.contains(approveQuotationCategoryResponseVO.getCategoryCode())) {
                        return true;
                    } else {
                        category.add(approveQuotationCategoryResponseVO.getCategoryCode());
                        return false;
                    }
                });
            }
            logger.info("get product category code parameter for product center service success . result:{}", result);
            logger.info("get product category code parameter for product center service cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return result;
        } catch (Exception e) {
            logger.error("get product category code parameter for product center service fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(e.getMessage());
        }
    }

    /**
     * 根据代码获取产品信息
     *
     * @param productCode 产品代码
     * @return Map<String, ProductInfoResponseVO>
     */
    @Override
    public Map<String, ProductInfoResponseVO> getProductInfo(List<String> productCode) {
        logger.info("get product info for product code.");

        Map<String, ProductInfoResponseVO> productInfoMap = new HashMap<>();

        try {
            // 设置请求时间
            Stopwatch stopwatch = Stopwatch.createStarted();

            // 设置请求参数
            ProductInfoRequestVO productInfoRequestVO = ProductInfoRequestVO.builder().productCodeList(productCode).build();
            BusinessResponse<List<ProductInfoResponseVO>> businessResponse = productCenterCmsSAO.getProductInfo(productInfoRequestVO);
            logger.info("get product info for product code success. businessResponse:{}", businessResponse);
            logger.info("get product info for product code cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            // 如果是成功的则设置返回参数
            if (businessResponse.success()) {
                productInfoMap.putAll(businessResponse.getData().stream().collect(Collectors.toMap(ProductInfoResponseVO::getProductCode, Function.identity(), (a, b) -> a)));
            }
        } catch (Exception e) {
            logger.error("get product info for product code fail. errorMsg:{} productCode:{}", e.getMessage(), productCode);
        }

        return productInfoMap;
    }
}
