package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceDTO.java, v0.1 2023/3/2 10:29 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaOrderInvoiceSynchronizationDTO extends BaseDTO {
    /**
     * 发票id
     */
    private String id;

    /**
     * 发票单号
     */
    private String invoiceNumber;

    /**
     * 发票编码，从erp获取的
     */
    private String invoiceCode;

    /**
     * 发票类型  general 普通 dedicated 专用
     */
    private String invoiceType;

    /**
     * 发票性质 paper 纸质  electron 电子
     */
    private String invoiceNature;

    /**
     * 收货地址id
     */
    private String userAddressId;

    /**
     * 收货地址
     */
    private String userAddress;

    /**
     * 收货人
     */
    private String consignee;

    /**
     * 收货人联系方式
     */
    private String linkPhone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 发票抬头
     */
    private String invCustomerName;

    /**
     * 发票税务登记号
     */
    private String invTaxRegNumber;

    /**
     * 发票公司地址
     */
    private String invAddress;

    /**
     * 发票公司电话
     */
    private String invLinkPhone;

    /**
     * 发票开户银行
     */
    private String invManuBank;

    /**
     * 发票银行账户
     */
    private String invAccount;

    /**
     * 发票状态 invoicing 开票中 invoiced 已开票 shipped 已发货 cancel 已取消
     */
    private String invoiceStatus;

    /**
     * 物流编码
     */
    private String logisticsCode;

    /**
     * 物流公司
     */
    private String logisticsName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 发票额度
     */
    private BigDecimal price;

    /**
     * 发货状态  shipped 已发货 unshipped 未发货
     */
    private String deliveryStatus;

    /**
     * 所属地区 DG东莞 SZ苏州
     */
    private String territory;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 省份
     */
    private String province;
    /**
     * 市
     */
    private String city;

    /**
     * 订单发票详情
     */
    private List<FaOrderInvoiceDetailSynchronizationDTO> orderInvoiceDetails;
}
