package com.yhd.fa.marketing.cms.util;

import cn.hutool.core.util.RandomUtil;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.GoodsStatusEnum;
import com.yhd.fa.marketing.cms.enums.QuotationStatusEnum;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CreateQuotationDetailRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: QuotationUtil.java, v0.1 2023/1/5 15:58 yehuasheng Exp $
 */
public class QuotationUtil {
    /**
     * 100
     */
    public static final int HUNDRED = 100;
    /**
     * 100BigDecimal
     */
    public static final BigDecimal HUNDRED_BIGDECIMAL = BigDecimal.valueOf(HUNDRED);
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(QuotationUtil.class.getName());
    /**
     * 认证企业用户报价单前缀
     */
    private static final String AUTHORIZED_COMPANY_PREFIX = "YB";
    /**
     * 普通企业用户前缀
     */
    private static final String UNAUTHORIZED_COMPANY_PREFIX = "MYB";

    private QuotationUtil() {
    }

    /**
     * 生成报价单号
     *
     * @return String
     */
    public static String generateQuotationNumber(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("generate quotation number.");

        // 前缀
        String prefix = UserUtil.getUserEnquiryRole(userInfo) == CommonConstant.TWO ? AUTHORIZED_COMPANY_PREFIX : UNAUTHORIZED_COMPANY_PREFIX;

        // 时间
        String nowTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_FORMATTER));

        // 用户编码后四位
        String userCode = "000" + userInfo.getUserCode();
        userCode = userCode.substring(userCode.length() - 4);

        // 获取4位随机数
        String randomNumber = RandomUtil.randomStringUpper(CommonConstant.FOUR);

        // 将所有的字符串拼接
        String quotationNumber = prefix +
                nowTime +
                userCode +
                randomNumber;

        logger.info("generate quotation number:{}", quotationNumber);

        return quotationNumber;
    }

    /**
     * 商品状态
     *
     * @return Map<String, String>
     */
    public static Map<String, String> setGoodsStatusMap() {
        logger.info("set goods status map.");

        Map<String, String> goodsStatusMap = new HashMap<>();
        goodsStatusMap.put("outnumbering", GoodsStatusEnum.QUANTITY_EXCESS.getStatus());
        goodsStatusMap.put("modelError", GoodsStatusEnum.WRONG_MODEL.getStatus());
        goodsStatusMap.put("modelCodeError", GoodsStatusEnum.WRONG_MODEL.getStatus());
        goodsStatusMap.put("noUnitPrice", GoodsStatusEnum.NO_PRICE.getStatus());
        goodsStatusMap.put("noPrice", GoodsStatusEnum.NO_PRICE.getStatus());
        goodsStatusMap.put("noDelivery", GoodsStatusEnum.NO_PRICE.getStatus());
        goodsStatusMap.put("formulaError", GoodsStatusEnum.WRONG_MODEL.getStatus());
        goodsStatusMap.put("goodsOffShelf", GoodsStatusEnum.OFF_SHELF.getStatus());
        goodsStatusMap.put("goodsHalfSales", GoodsStatusEnum.DISCONTINUED.getStatus());

        return goodsStatusMap;
    }

    /**
     * 设置报价单明细的状态
     *
     * @param quotationResponseVO            报价单报价单的结果
     * @param createQuotationDetailRequestVO 请求生产报价单的明细
     * @return String
     */
    public static String setQuotationDetailStatus(QuotationResponseVO quotationResponseVO, CreateQuotationDetailRequestVO createQuotationDetailRequestVO) {
        logger.info("set quotation detail status.");

        // 判断是否有备注或者有图片，有就人工报价
        if (StringUtils.isNotBlank(createQuotationDetailRequestVO.getRemark()) || StringUtils.isNotBlank(createQuotationDetailRequestVO.getFileUrl())) {
            return QuotationStatusEnum.QUOTATION.getStatus();
        }

        // 判断是否有价格和交期有就正常没有就人工报价单
        return quotationResponseVO.isStandard() && quotationResponseVO.isHaveDelivery() && null != quotationResponseVO.getPrice() ? QuotationStatusEnum.FINISH.getStatus() : QuotationStatusEnum.QUOTATION.getStatus();
    }

    /**
     * 设置价格和交期
     *
     * @param quotationListPO     报价单的明细po
     * @param quotationResponseVO 查询价格和交期
     */
    public static void setQuotationPOPriceAndDelivery(QuotationListPO quotationListPO, QuotationResponseVO quotationResponseVO) {
        quotationListPO.setOriginalPrice(quotationResponseVO.getPrice());
        quotationListPO.setDiscountPrice(quotationResponseVO.getPriceWithoutTax());
        quotationListPO.setTaxDiscountPrice(quotationResponseVO.getPriceWithTax());
        quotationListPO.setTotalPrice(quotationResponseVO.getTotal());
        quotationListPO.setQuantityDiscountRate(quotationResponseVO.getAmountDiscountRate());
        quotationListPO.setTotalDiscountRate(quotationResponseVO.getTotalDiscount());
        quotationListPO.setDelivery(quotationResponseVO.getDelivery());
        quotationListPO.setPlotId(quotationResponseVO.getModelValueId());
        quotationListPO.setPriceId(quotationResponseVO.getSourcePriceAutoId());
        quotationListPO.setAdditionalPriceId(quotationResponseVO.getSourcePriceAutoId());
        quotationListPO.setSupplierPriceOne(quotationResponseVO.getG1Price());
        quotationListPO.setSupplierPriceTwo(quotationResponseVO.getG2Price());
        quotationListPO.setSupplierPriceThree(quotationResponseVO.getG3Price());
    }
}
