package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: SendMessageToSmsManyRequestVO.java, v0.1 2023/3/20 15:55 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendMessageToSmsManyRequestVO extends BaseVO {
    /**
     * 模板id
     */
    private String templateId;

    /**
     * 短信多对多消息推送传入
     */
    private List<String> map;

    /**
     * 手机号码
     */
    private List<String> phoneNumber;

    /**
     * 短信签名，当短信为多对多发送时，有多少个手机号，传多少个签名；
     */
    private List<String> sign;
}
