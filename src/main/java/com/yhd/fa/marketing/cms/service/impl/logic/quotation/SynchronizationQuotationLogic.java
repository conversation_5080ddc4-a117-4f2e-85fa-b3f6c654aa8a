package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.BusinessTypeConstant;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.QuotationListMapper;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.mq.producer.SynchronizationProducer;
import com.yhd.fa.marketing.cms.pojo.dto.FaQuotationDetailSynchronizationOfflineDTO;
import com.yhd.fa.marketing.cms.pojo.dto.FaQuotationSynchronizationOfflineDTO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SynchronizationQuotationLogic.java, v0.1 2023/2/27 15:33 yehuasheng Exp $
 */
@Component
public class SynchronizationQuotationLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SynchronizationQuotationLogic.class.getName());

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    /**
     * 报价单明细mapper
     */
    @Resource
    private QuotationListMapper quotationListMapper;

    /**
     * 同步生产者
     */
    @Resource
    private SynchronizationProducer synchronizationProducer;

    /**
     * 用户中心
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 执行同步
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<Void> exec(String quotationId) {
        logger.info("start exec synchronization quotation logic.");

        // 获取报价单信息
        QuotationPO quotationInfo = quotationMapper.getOne(new MPJLambdaWrapper<QuotationPO>()
                .selectAll(QuotationPO.class)
                .eq(QuotationPO::getId, quotationId)
                .last(FaDocMarketingCmsConstant.LIMIT));
        if (ObjectUtil.isNull(quotationInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_IS_EMPTY);
        }

        // 获取用户的信息
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(quotationInfo.getUserCode(), quotationInfo.getCompanyCode());

        // 设置erp企业编码用于同步给erp
        quotationInfo.setErpCompanyCode(userInfo.getCompanyInfo().getErpCompanyCode());

        // 查下报价单明细信息
        List<QuotationListPO> quotationList = quotationListMapper.list(new MPJLambdaWrapper<QuotationListPO>()
                .selectAll(QuotationListPO.class)
                .eq(QuotationListPO::getQuotationId, quotationId));
        if (CollUtil.isEmpty(quotationList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_DETAIL_IS_EMPTY);
        }

        // 执行同步
        synchronization(quotationInfo, quotationList);

        return BusinessResponseCommon.ok(null);
    }

    /**
     * 同步报价单
     *
     * @param quotationInfo 报价单详情
     * @param quotationList 报价单明细
     */
    @Async
    public void synchronization(QuotationPO quotationInfo, List<QuotationListPO> quotationList) {
        logger.info("start synchronization quotation to mq.");

        // 组装同步报价单明细
        List<FaQuotationDetailSynchronizationOfflineDTO> faQuotationDetailSynchronizationOffline = setQuotationDetailSynchronizationData(quotationList);

        // 组装同步报价单详情
        FaQuotationSynchronizationOfflineDTO faQuotationSynchronizationOffline = setSynchronizationQuotationInfo(quotationInfo, faQuotationDetailSynchronizationOffline);

        // 执行同步到mq
        synchronizationProducer.synchronizationData(JSON.toJSONString(faQuotationSynchronizationOffline), BusinessTypeConstant.CREATE_QUOTATION, quotationInfo.getQuotationNumber());
    }

    /**
     * 组装报价单明细列表
     *
     * @param quotationList 报价单明细
     * @return List<FaQuotationDetailSynchronizationOfflineDTO>
     */
    private List<FaQuotationDetailSynchronizationOfflineDTO> setQuotationDetailSynchronizationData(List<QuotationListPO> quotationList) {
        logger.info("set quotation detail synchronization data.");

        // 组装
        return quotationList.stream().map(quotationListPO ->
                        FaQuotationDetailSynchronizationOfflineDTO
                                .builder()
                                .quotationId(quotationListPO.getQuotationId())
                                .quotationNumber(quotationListPO.getQuotationNumber())
                                .sort(quotationListPO.getSort())
                                .model(quotationListPO.getModel())
                                .customerModel(quotationListPO.getCustomerModel())
                                .productCode(quotationListPO.getProductCode())
                                .productName(quotationListPO.getProductName())
                                .quantity(quotationListPO.getQuantity())
                                .oldQuantity(quotationListPO.getOldQuantity())
                                .originalPrice(quotationListPO.getOriginalPrice())
                                .discountPrice(quotationListPO.getDiscountPrice())
                                .taxDiscountPrice(quotationListPO.getTaxDiscountPrice())
                                .totalPrice(quotationListPO.getTotalPrice())
                                .quantityDiscountRate(quotationListPO.getQuantityDiscountRate())
                                .totalDiscountRate(quotationListPO.getTotalDiscountRate())
                                .delivery(quotationListPO.getDelivery())
                                .remark(quotationListPO.getRemark())
                                .customerMaterialCode(quotationListPO.getCustomerMaterialCode())
                                .customerProductName(quotationListPO.getCustomerProductName())
                                .modelLong(quotationListPO.getModelLong())
                                .plotId(quotationListPO.getPlotId())
                                .priceId(quotationListPO.getPriceId())
                                .additionalPriceId(quotationListPO.getAdditionalPriceId())
                                .materialQualityId(quotationListPO.getMaterialQualityId())
                                .supplierPriceOne(quotationListPO.getSupplierPriceOne())
                                .supplierPriceTwo(quotationListPO.getSupplierPriceTwo())
                                .supplierPriceThree(quotationListPO.getSupplierPriceThree())
                                .supplierModelOne(quotationListPO.getSupplierModelOne())
                                .supplierModelTwo(quotationListPO.getSupplierModelTwo())
                                .supplierModelThree(quotationListPO.getSupplierModelThree())
                                .technicalSpecifications(quotationListPO.getTechnicalSpecifications())
                                .unit(quotationListPO.getUnit())
                                .calculationStatus(quotationListPO.getCalculationStatus())
                                .confirmationStatus(quotationListPO.getConfirmationStatus())
                                .goodsStatus(quotationListPO.getGoodsStatus())
                                .materialCode(quotationListPO.getMaterialCode())
                                .quotationStatus(quotationListPO.getQuotationStatus())
                                .errorMessage(quotationListPO.getErrorMessage())
                                .fileUrl(quotationListPO.getFileUrl())
                                .standardStatus(quotationListPO.getStandardStatus())
                                .categoryCode(quotationListPO.getCategoryCode())
                                .build()
                )
                .collect(Collectors.toList());
    }

    /**
     * 设置同步报价单详情信息
     *
     * @param quotationInfo                           报价单详情
     * @param faQuotationDetailSynchronizationOffline 同步的数据
     * @return FaQuotationSynchronizationOfflineDTO
     */
    private FaQuotationSynchronizationOfflineDTO setSynchronizationQuotationInfo(QuotationPO quotationInfo, List<FaQuotationDetailSynchronizationOfflineDTO> faQuotationDetailSynchronizationOffline) {
        logger.info("set synchronization quotation info data.");

        // 组装
        return FaQuotationSynchronizationOfflineDTO
                .builder()
                .id(quotationInfo.getId())
                .quotationNumber(quotationInfo.getQuotationNumber())
                .quotationLevel(quotationInfo.getQuotationLevel())
                .customerQuotationNumber(quotationInfo.getCustomerQuotationNumber())
                .companyCode(quotationInfo.getCompanyCode())
                .erpCompanyCode(quotationInfo.getErpCompanyCode())
                .companyName(quotationInfo.getCompanyName())
                .userCode(quotationInfo.getUserCode())
                .purchaseUserCode(quotationInfo.getPurchaseUserCode())
                .quotationStatus(quotationInfo.getQuotationStatus())
                .totalPrice(quotationInfo.getTotalPrice())
                .payablePrice(quotationInfo.getPayablePrice())
                .channelType(quotationInfo.getChannelType())
                .createdDate(quotationInfo.getCreatedDate())
                .quotationDetails(faQuotationDetailSynchronizationOffline)
                .build();
    }
}
