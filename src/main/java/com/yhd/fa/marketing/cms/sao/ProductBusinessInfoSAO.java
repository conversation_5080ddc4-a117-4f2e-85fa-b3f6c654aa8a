package com.yhd.fa.marketing.cms.sao;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ProductBusinessInfoSAO.java, v0.1 2022/12/23 8:54 yehuasheng Exp $
 */
@FeignClient(name = "yhd-service-product-business-info")
public interface ProductBusinessInfoSAO {
    /**
     * 查询价格
     *
     * @param quotationRequestVO 查询请求参数
     * @return BusinessResponse<List < QuotationResponseVO>>
     */
    @PostMapping(value = "/product/v2/0/price", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    BusinessResponse<List<QuotationResponseVO>> quotationPrice(@RequestBody QuotationRequestVO quotationRequestVO);
}
