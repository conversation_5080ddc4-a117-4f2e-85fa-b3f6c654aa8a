package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/6/18 16:30
 */
@Data
public class FbQuotationPageDTO extends BaseDTO {
    @Schema(description = "报价单号")
    private String quotationNumber;

    @Schema(description = "报价状态")
    private String quotationStatus;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "制单开始时间",example = "2024-06-18")
    private String startDate;

    @Schema(description = "制单结束时间",example = "2024-06-18")
    private String endDate;

    @Schema(description = "是否转单")
    private String transferOrder;

    @Schema(description = "公司归属 DGYHD/SZYHD")
    private String ownershipCompany;

    @Schema(description = "部门名称")
    private String unitName;

}
