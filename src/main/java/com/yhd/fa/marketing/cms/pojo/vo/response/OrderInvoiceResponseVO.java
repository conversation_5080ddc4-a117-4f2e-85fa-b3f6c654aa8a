package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceResponseVO.java, v0.1 2023/2/23 8:37 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderInvoiceResponseVO extends BaseVO {
    /**
     * 发票id
     */
    @Schema(description = "发票id", example = "44b07af2969d49d4bc8c02baf77f1d39")
    private String orderInvoiceId;

    /**
     * 发票单号
     */
    @Schema(description = "发票单号", example = "I2018072509075558215")
    private String orderInvoiceNumber;

    /**
     * 发票编号
     */
    @Schema(description = "发票编号", example = "cccccccc")
    private String orderInvoiceCode;

    /**
     * 申请人编号
     */
    @Schema(description = "申请人编号", example = "1399")
    private String userCode;

    /**
     * 申请人名称
     */
    @Schema(description = "申请人名称", example = "你猜")
    private String userName;

    /**
     * 企业编号
     */
    @Schema(description = "企业编号", example = "I55186072428")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称/发票抬头", example = "中科军联（张家港）新能源科技有限公司")
    private String companyName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-01-01 10:10:10")
    private LocalDateTime createdDate;

    /**
     * 发票类型 general 普通 dedicated 专用
     */
    @Schema(description = "发票类型 general普通 dedicated专用", example = "general")
    private String orderInvoiceType;

    /**
     * 发票类型名称
     */
    @Schema(description = "发票类型名称", example = "普通")
    private String orderInvoiceTypeName;

    /**
     * 发票状态
     */
    @Schema(description = "发票状态 invoicing开票中 invoiced已开票 shipped已发货 cancel已取消", example = "invoiced")
    private String orderInvoiceStatus;

    /**
     * 发票状态名称
     */
    @Schema(description = "发票状态名称", example = "已开票")
    private String orderInvoiceStatusName;

    /**
     * 发票性质 paper 纸质  electron 电子
     */
    @Schema(description = "发票性质 paper纸质  electron电子", example = "paper")
    private String orderInvoiceNature;

    /**
     * 发票性质名称
     */
    @Schema(description = "发票性质名称", example = "纸质")
    private String orderInvoiceNatureName;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态 success已同步 fail同步失败 wait等待同步 synchronizing同步中", example = "success")
    private String synchronizationStatus;

    /**
     * 同步时间
     */
    @Schema(description = "同步时间", example = "2022-01-01 10:10:10")
    private LocalDateTime synchronizationDate;

    /**
     * 同步回传信息
     */
    @Schema(description = "同步回传信息", example = "success")
    private String synchronizationMessage;

    /**
     * 申请发票总金额
     */
    @Schema(description = "申请发票总金额", example = "10.00")
    private BigDecimal orderInvoiceTotalPrice;

    /**
     * 跟单员
     */
    @Schema(description = "跟单员", example = "你猜")
    private String merchandiser;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "1311111111")
    private String merchandiserContact;

    /**
     * 业务员名称
     */
    @Schema(description = "业务员名称", example = "01111/林杏金")
    private String salesmanName;

    /**
     * 业务员联系方式
     */
    @Schema(description = "业务员联系方式", example = "13111111111")
    private String salesmanContact;
}
