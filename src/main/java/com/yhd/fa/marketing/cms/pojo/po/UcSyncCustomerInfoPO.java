package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version Id: UcSyncCustomerInfoPO.java, v 0.1 2025/5/15 09:19 JiangYuHong Exp $
 * 客户资料主表
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "uc_sync_customer_info")
@EqualsAndHashCode(callSuper = true)
public class UcSyncCustomerInfoPO extends BaseEntity {

    /**
     * 逻辑删除 0正常，1删除
     */
    private Integer isDelete;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户简称
     */
    private String shortname;

    /**
     * 客户来源：展会收集、朋友介绍、客户转介绍、个人观察、网上搜索、电商平台、甲方指定
     */
    private String customerSource;

    /**
     * 所属行业
     */
    private String industry;

    /**
     * 公司网址
     */
    private String companyWeb;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 行业全称
     */
    private String industryName;

    /**
     * 一级行业
     */
    private String primaryIndustry;

    /**
     * 二级行业
     */
    private String secondaryIndustry;

    /**
     * 客户类别
     */
    private String customerClass;

    /**
     * 客户等级 资料客户1, 潜在客户2,意向客户3,成交客户4,长期客户5,FANS客户6,沉睡客户7,沉睡可转移8
     */
    private Integer customerGrade;

    /**
     * 销售类型  国内销售0 国外销售1
     */
    private Integer salesType;

    /**
     * 预估采购潜力（万元）
     */
    private BigDecimal estimatedProcurement;

    /**
     * 采购潜力
     */
    private String purchasingPotential;

    /**
     * 设备产品
     */
    private String equipmentProducts;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 长途区号
     */
    private String areaCode;

    /**
     * 所属城市
     */
    private String city;

    /**
     * 接受多地发货 是 1、否 0
     */
    private Integer manyPlacesDelivery;

    /**
     * 电商注册 否0 仅询价1 网上订购2
     */
    private Integer ecommerceRegistry;

    /**
     * 人员数量
     */
    private Integer personnelQuantity;

    /**
     * 客户层级 系统自动根据新人员数量如下规则带出新客户层级：新人员数量*1.5 小于50 为6级，小于200为5级，小于400为4级，小于800为3级，小于1500为2级，大于1500为1 级，此规则要配置在数据字典
     */
    private Integer customerLevel;

    /**
     * 含税价格属性 含税两位1 含税四位2
     */
    private Integer taxPriceAttribute;

    /**
     * 含税价格审核时间
     */
    private LocalDateTime taxPriceAuditDate;

    /**
     * 地区名称
     */
    private String regionName;

    /**
     * 地区
     */
    private String region;

    /**
     * 建档日期
     */
    private LocalDateTime regdate;

    /**
     * 建档人
     */
    private String registrant;

    /**
     * 计数
     */
    private Integer counter;

    /**
     * 报价比例
     */
    private BigDecimal quotationProportion;

    /**
     * 备注
     */
    private String remark;

    /**
     * 注册地址
     */
    private String registryAddress;

    /**
     * 法人代表
     */
    private String legalPerson;

    /**
     * 企业性质(公司类型)
     */
    private String entNature;

    /**
     * 工商注册号
     */
    private String registryNo;

    /**
     * 税务登记号
     */
    private String taxId;

    /**
     * 股票代码
     */
    private String stockCode;

    /**
     * 币别 法郎-CHF
     * 、欧元-EUR、港币-HKD、日元-JPY、人民币-RMB、美元-USD
     */
    private String currency;

    /**
     * 注册币别 法郎-CHF
     * 、欧元-EUR、港币-HKD、日元-JPY、人民币-RMB、美元-USD
     */
    private String registryCurrency;

    /**
     * 注册资金-万
     */
    private BigDecimal registryCapital;

    /**
     * 成立日期
     */
    private LocalDateTime registryTime;

    /**
     * 统一信用代码
     */
    private String creditCode;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 授权编号 [010000,050000,040000,070000]
     */
    private String authorizedNo;

    /**
     * 是否禁止整单分批 是 1、否 0
     */
    private Integer isForbidAllBatches;

    /**
     * 是否禁止整行分批 是 1、否 0
     */
    private Integer isForbidRowBatches;

    /**
     * 客户类型 客户1 供应商2 客户供应商3
     */
    private Integer customerType;

    /**
     * 是否客户 是 1、否 0
     */
    private Integer isCustomer;

    /**
     * 机械工程师总人数
     */
    private Integer engineersQuantity;

    /**
     * 预估今年营业额（万元）
     */
    private BigDecimal estimatedTurnover;

    /**
     * 归属公司 东莞-DGYHD 苏州-SZYHD
     */
    private String ownershipCompany;

    /**
     * 附件
     */
    private String appendix;

}