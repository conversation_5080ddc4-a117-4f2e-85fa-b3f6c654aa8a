package com.yhd.fa.marketing.cms.service.impl.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.HelpUserCancelOrderApplyRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCancelRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.HelpUserCancelOrderBasicsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderCancelService;
import com.yhd.fa.marketing.cms.service.impl.logic.order.GetOrderCancelInfoLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.order.GetOrderCancelListLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.order.HelpUserCancelOrderLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.order.SynchronizationOrderCancelLogic;
import com.yhd.fa.marketing.cms.service.impl.verification.order.GetOrderCancelListVerification;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCancelServiceImpl.java, v0.1 2023/2/22 10:00 yehuasheng Exp $
 */
@Service
public class OrderCancelServiceImpl implements OrderCancelService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderCancelServiceImpl.class.getName());

    /**
     * 检查订单取消列表
     */
    @Resource
    private GetOrderCancelListVerification getOrderCancelListVerification;

    /**
     * 订单取消逻辑
     */
    @Resource
    private GetOrderCancelListLogic getOrderCancelListLogic;

    /**
     * 订单取消详情逻辑
     */
    @Resource
    private GetOrderCancelInfoLogic getOrderCancelInfoLogic;

    /**
     * 订单取消同步逻辑
     */
    @Resource
    private SynchronizationOrderCancelLogic synchronizationOrderCancelLogic;

    /**
     * 帮用户取消订单逻辑
     */
    @Resource
    private HelpUserCancelOrderLogic helpUserCancelOrderLogic;

    /**
     * 获取订单取消列表
     *
     * @param orderCancelRequestVO 订单取消列表参数
     * @return BusinessResponse<PageInfo < OrderCancelResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<OrderCancelResponseVO>> getOrderCancelList(OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("start get order cancel list service.");

        // 校验获取订单取消参数
        BusinessResponse<PageInfo<OrderCancelResponseVO>> checkParameter = getOrderCancelListVerification.check(orderCancelRequestVO);
        if (!checkParameter.success()) {
            return checkParameter;
        }

        // 执行逻辑
        return getOrderCancelListLogic.exec(orderCancelRequestVO);
    }

    /**
     * 获取订单取消详情
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<OrderCancelDetailResponseVO>
     */
    @Override
    public BusinessResponse<OrderCancelDetailResponseVO> getOrderCancelInfo(String orderCancelId) {
        logger.info("start get order detail service.");

        // 获取逻辑
        return getOrderCancelInfoLogic.exec(orderCancelId);
    }

    /**
     * 同步订单取消
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<Void> synchronizationOrderCancel(String orderCancelId) {
        logger.info("start synchronization order cancel service.");

        // 执行同步
        return synchronizationOrderCancelLogic.exec(orderCancelId);
    }

    /**
     * 取消订单初始化接口
     *
     * @param orderId 订单id
     * @return BusinessResponse
     */
    @Override
    public BusinessResponse<List<HelpUserCancelOrderBasicsResponseVO>> helpUserCancelOrderBasics(String orderId) {
        logger.info("start get help user cancel order basics service.");

        return helpUserCancelOrderLogic.helpUserCancelOrderBasics(orderId);
    }

    /**
     * 申请取消订单
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @Override
    public BusinessResponse<Object> helpUserCancelOrderApply(HelpUserCancelOrderApplyRequestVO requestVO) {

        logger.info("start help user cancel order service.");

        return helpUserCancelOrderLogic.helpUserCancelOrderApply(requestVO);
    }
}
