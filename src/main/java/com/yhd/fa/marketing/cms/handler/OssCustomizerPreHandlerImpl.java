package com.yhd.fa.marketing.cms.handler;

import com.yhd.united.file.handler.OssCustomizerPreHandler;
import com.yhd.united.file.pojo.vo.request.UploadBodyRequestVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: OssCustomizerPreHandlerImpl.java, v0.1 2023/3/23 8:19 yehuasheng Exp $
 */
@Component
public class OssCustomizerPreHandlerImpl implements OssCustomizerPreHandler {
    /**
     * 文件的大小
     */
    public static final long FILE_SIZE = 50L;

    /**
     * @param uploadBodyRequestVO 上传的图片参数
     * @return String
     */
    @Override
    public String customizerDir(UploadBodyRequestVO uploadBodyRequestVO) {
        return "Uploads/Baojlist";
    }

    /**
     * 限制普通文件上传大小
     *
     * @return long
     */
    @Override
    public long limitFileSize() {
        return FILE_SIZE;
    }
}
