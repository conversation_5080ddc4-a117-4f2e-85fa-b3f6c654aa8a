package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: CustomerListResponseVO.java, v0.1 2023/1/3 16:39 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerListResponseVO extends BaseVO {
    /**
     * 客户编码
     */
    @Schema(description = "客户编码", example = "A001")
    private String customerCode;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称", example = "东莞怡合达自动化股份优先公司")
    private String customerName;

    /**
     * 电商的企业编码
     */
    @Schema(description = "电商的企业编码", example = "H50160796679")
    private String companyCode;
}
