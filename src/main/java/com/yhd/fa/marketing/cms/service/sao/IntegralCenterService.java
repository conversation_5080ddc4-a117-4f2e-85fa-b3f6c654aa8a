package com.yhd.fa.marketing.cms.service.sao;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.AddUserIntegralRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.BatchAddUserIntegralRequestVO;

/**
 * <AUTHOR>
 * @version Id: IntegralCenterService.java, v 0.1 2023/3/14 15:11 JiangYuHong Exp $
 */
public interface IntegralCenterService {


    /**
     * 添加积分
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    BusinessResponse<Object> addUserIntegral(AddUserIntegralRequestVO requestVO);

    /**
     * 批量添加积分（慎用，应与积分中心沟通批量添加的时间）
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    BusinessResponse<Object> batchAddUserIntegral(BatchAddUserIntegralRequestVO requestVO);

}
