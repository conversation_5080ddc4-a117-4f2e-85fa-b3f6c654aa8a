package com.yhd.fa.marketing.cms.web.quotation;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationAndOrderDeleteRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationPageRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbQuotationDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbQuotationListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbQuotationTotalPriceCountResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbQuotationaExportResponseVO;
import com.yhd.fa.marketing.cms.service.controller.FbQuotationApiService;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 8:38
 */
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
@RestController
@Tag(name = "非标报价单cmsApi对接", description = "非标报价单cmsApi对接")
public class FbQuotationApiController {

    @Resource
    private FbQuotationApiService fbQuotationApiService;

    @Operation(summary = "Fb报价单分页列表数据获取")
    @PostMapping(value = "/fb/quotation-page", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb报价单列表数据获取")
    public BusinessResponse<PageInfo<FbQuotationListResponseVO>> getFbQuotationPage(@RequestBody @Validated FbQuotationPageRequestVO fbQuotationPageRequestVO) {
        return fbQuotationApiService.getFbQuotationPage(fbQuotationPageRequestVO);
    }


    @Operation(summary = "Fb报价单详情数据获取")
    @GetMapping(value = "/fb/quotation-detail")
    @SysLog(value = "Fb报价单详情数据获取")
    public BusinessResponse<FbQuotationDetailResponseVO> getFbQuotationDetail(@RequestParam(value = "id", required = false) String id,
                                                                              @RequestParam(value = "quotationNumber", required = false) String quotationNumber) {
        return fbQuotationApiService.getFbQuotationDetail(id, quotationNumber);
    }

    @Operation(summary = "数据清洗<业务员、跟单员、部门、公司归属>")
    @GetMapping(value = "/fb/quotation-init")
    @SysLog(value = "数据清洗<业务员、跟单员、部门、公司归属>")
    public BusinessResponse<FbQuotationDetailResponseVO> fbQuotationInit() {
        return fbQuotationApiService.fbQuotationInit();
    }

    @Operation(summary = "Fb报价单单据统计金额")
    @PostMapping(value = "/fb/quotation-count", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb报价单列表数据获取")
    public BusinessResponse<FbQuotationTotalPriceCountResponseVO> getFbQuotation(@RequestBody @Validated FbQuotationPageRequestVO fbQuotationPageRequestVO) {
        return fbQuotationApiService.getFbQuotationCount(fbQuotationPageRequestVO);
    }

    @Operation(summary = "Fb报价单删除")
    @PostMapping(value = "/fb/quotation-delete", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb报价单删除")
    public BusinessResponse<Object> deleteFbQuotation(@RequestBody @Validated FbQuotationAndOrderDeleteRequestVO fbQuotationAndOrderDeleteRequestVO) {
        return fbQuotationApiService.deleteFbQuotation(fbQuotationAndOrderDeleteRequestVO);
    }

    @Operation(summary = "Fb报价单分页列表导出数据")
    @PostMapping(value = "/fb/quotation-export", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb报价单分页列表导出数据")
    public BusinessResponse<List<FbQuotationaExportResponseVO>> getFbQuotationData(@RequestBody @Validated FbQuotationPageRequestVO fbQuotationPageRequestVO) {
        return fbQuotationApiService.getFbQuotationData(fbQuotationPageRequestVO);
    }
}
