package com.yhd.fa.marketing.cms.service.impl.verification.quotation;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: GetCompanyListByCompanyNameVerification.java, v0.1 2023/4/12 11:47 yehuasheng Exp $
 */
@Component
public class GetCompanyListByCompanyNameVerification {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(GetCompanyListByCompanyNameVerification.class.getName());

    /**
     * 检查企业名称参数
     *
     * @param companyName 企业名称
     * @param <T>         T
     * @return BusinessResponse
     */
    public <T> BusinessResponse<T> check(String companyName) {
        logger.info("check get company list by company name parameter.");

        // 判断是否为空
        if (StringUtils.isBlank(companyName)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.COMPANY_NAME_IS_NULL);
        }

        // 判断企业名字是否少于3个
        if (companyName.trim().length() < CommonConstant.THREE) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ENTERPRISE_NAME_CANNOT_BE_LESS_THAN_THREE);
        }

        return BusinessResponseCommon.ok(null);
    }
}
