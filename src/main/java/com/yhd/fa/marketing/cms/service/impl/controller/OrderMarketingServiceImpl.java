package com.yhd.fa.marketing.cms.service.impl.controller;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.CouponOrderCountRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderCountResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderFinishCountResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderMarketingService;
import com.yhd.fa.marketing.cms.service.impl.logic.order.OrderMarketingLogic;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderMarketingServiceImpl.java, v 0.1 2024/8/8 9:39 JiangYuHong Exp $
 */
@Service
public class OrderMarketingServiceImpl implements OrderMarketingService {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderMarketingServiceImpl.class.getName());

    @Resource
    private OrderMarketingLogic orderMarketingLogic;


    /**
     * 优惠券订单完成
     *
     * @param couponId 优惠券id
     * @return businessResponse
     */
    @Override
    public BusinessResponse<CouponOrderFinishCountResponseVO> couponOrderFinish(List<String> couponId) {

        logger.info("start order marketing coupon order finish impl.");

        return orderMarketingLogic.couponOrderFinish(couponId);
    }

    /**
     * 优惠券订单统计
     *
     * @param requestVO 请求参数
     * @return businessResponse
     */
    @Override
    public BusinessResponse<List<CouponOrderCountResponseVO>> couponOrderCount(CouponOrderCountRequestVO requestVO) {

        logger.info("start order marketing coupon order count impl.");

        return orderMarketingLogic.couponOrderCount(requestVO);
    }
}
