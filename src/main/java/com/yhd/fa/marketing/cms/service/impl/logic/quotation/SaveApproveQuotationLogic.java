package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndMerchandiserAndSalesmanInfoResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.CompanyInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.common.util.UUIDUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.*;
import com.yhd.fa.marketing.cms.enums.*;
import com.yhd.fa.marketing.cms.mapper.ExamineQuotationDetailListMapper;
import com.yhd.fa.marketing.cms.mapper.QuotationListMapper;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.pojo.po.ExamineQuotationDetailListPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CreateQuotationDetailRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationModelInfoRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.SaveApproveQuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;
import com.yhd.fa.marketing.cms.service.sao.ProductBusinessInfoService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.QuotationUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SaveApproveQuotationLogic.java, v0.1 2022/12/29 16:56 yehuasheng Exp $
 */
@Component
public class SaveApproveQuotationLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SaveApproveQuotationLogic.class.getName());

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    /**
     * 报价单明细mapper
     */
    @Resource
    private QuotationListMapper quotationListMapper;

    /**
     * 审核报价单旧的明细
     */
    @Resource
    private ExamineQuotationDetailListMapper examineQuotationDetailListMapper;

    /**
     * 报价与交期服务
     */
    @Resource
    private ProductBusinessInfoService productBusinessInfoService;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 执行操作
     *
     * @param saveApproveQuotationRequestVO 保存的参数
     * @param quotationInfo                 报价单详情
     * @return BusinessResponse<Object>
     */
    public BusinessResponse<Object> exec(SaveApproveQuotationRequestVO saveApproveQuotationRequestVO, QuotationPO quotationInfo) {
        logger.info("start exec save approve quotation logic.");

        if (StringUtils.equals(saveApproveQuotationRequestVO.getResultType(), ApproveQuotationResultTypeConstant.REFUSE)) {
            // 拒绝的
            return refuse(saveApproveQuotationRequestVO);
        } else {
            // 审核通过的

            // 获取用户的信息
            Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyInfoMap = userBucCmsService.getCompanyAndMerchandiserAndSalesmanInfo(Collections.singletonList(quotationInfo.getCompanyCode()));
            CompanyAndMerchandiserAndSalesmanInfoResponseVO companyInfo = companyInfoMap.getOrDefault(quotationInfo.getCompanyCode(), new CompanyAndMerchandiserAndSalesmanInfoResponseVO());

            // 设置erp企业编码用于同步给erp
            quotationInfo.setErpCompanyCode(Optional.ofNullable(companyInfo.getCompanyInfo()).map(CompanyInfo::getErpCompanyCode).orElse(CommonConstant.EMPTY));

            // 查询报价单明细
            List<QuotationListPO> quotationDetailList = quotationListMapper
                    .list(new LambdaQueryWrapper<QuotationListPO>()
                            .eq(QuotationListPO::getQuotationId, quotationInfo.getId()));
            if (CollUtil.isEmpty(quotationDetailList)) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_DETAIL_IS_EMPTY);
            }
            // 判断是否有改动报价单
            if (saveApproveQuotationRequestVO.isChangeToQuotation()) {
                BusinessResponse<List<QuotationListPO>> quotationDetaillistBusinessResponse = changeToQuotation(saveApproveQuotationRequestVO, quotationInfo, quotationDetailList, companyInfo);
                if (!quotationDetaillistBusinessResponse.success()) {
                    return BusinessResponseCommon.fail(quotationDetaillistBusinessResponse.getRt_code(), quotationDetaillistBusinessResponse.getRt_msg());
                }

                // 替换要更新的报价单明细
                quotationDetailList.clear();
                quotationDetailList.addAll(quotationDetaillistBusinessResponse.getData());
            }

            // 更新主表状态以及价格
            LambdaUpdateWrapper<QuotationPO> lambdaUpdateWrapper = getQuotationPOLambdaUpdateWrapper(quotationInfo, quotationDetailList);
            if (!quotationMapper.update(null, lambdaUpdateWrapper)) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.APPROVE_QUOTATION_SAVE_FAIL);
            }

            // 要把数量折扣/总折扣 *100
            quotationDetailList.forEach(quotationListPO -> {
                Optional.ofNullable(quotationListPO.getQuantityDiscountRate())
                        .ifPresent(quantityDiscountRate
                                -> quotationListPO.setQuantityDiscountRate(quantityDiscountRate.multiply(QuotationUtil.HUNDRED_BIGDECIMAL).setScale(CommonConstant.TWO, RoundingMode.HALF_UP)));

                Optional.ofNullable(quotationListPO.getTotalDiscountRate())
                        .ifPresent(totalDiscountRate
                                -> quotationListPO.setTotalDiscountRate(totalDiscountRate.multiply(QuotationUtil.HUNDRED_BIGDECIMAL).setScale(CommonConstant.TWO, RoundingMode.HALF_UP)));
            });

            // 同步给erp
            SpringUtil.getBean(SynchronizationQuotationLogic.class).synchronization(quotationInfo, quotationDetailList);

            return BusinessResponse.ok(null);
        }
    }

    /**
     * 有改动报价单的逻辑
     *
     * @param saveApproveQuotationRequestVO 保存的参数
     * @param quotationInfo                 报价单详情
     * @param quotationDetailList           报价单明细列表
     * @param companyInfo                   企业信息
     * @return BusinessResponse<List < QuotationListPO>>
     */
    private BusinessResponse<List<QuotationListPO>> changeToQuotation(SaveApproveQuotationRequestVO saveApproveQuotationRequestVO, QuotationPO quotationInfo, List<QuotationListPO> quotationDetailList, CompanyAndMerchandiserAndSalesmanInfoResponseVO companyInfo) {
        logger.info("save approve quotation change to quotation logic.");

        // 记录要删除的明细
        List<ExamineQuotationDetailListPO> examineQuotationDetailList = BeanUtil.copyToList(quotationDetailList, ExamineQuotationDetailListPO.class);
        if (!examineQuotationDetailListMapper.saveBatch(examineQuotationDetailList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.AFF_OLD_QUOTATION_DETAIL_FAIL);
        }

        // 删除报价单的明细
        if (!quotationListMapper.remove(new LambdaQueryWrapper<QuotationListPO>().eq(QuotationListPO::getQuotationId, saveApproveQuotationRequestVO.getQuotationId()))) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.APPROVE_QUOTATION_SAVE_FAIL);
        }

        // 重新获取所有价格
        List<QuotationResponseVO> quotationResponseVOList = getPrice(saveApproveQuotationRequestVO.getQuotationDetailList(), companyInfo, quotationInfo);
        if (CollUtil.isEmpty(quotationResponseVOList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.APPROVE_QUOTATION_GET_PRICE_DELIVERY_FAIL);
        }

        // 保存报价单明细
        List<QuotationListPO> newQuotationDetailList = generateQuotationDetail(saveApproveQuotationRequestVO.getQuotationDetailList(), quotationResponseVOList, quotationInfo);
        if (!quotationListMapper.saveBatch(newQuotationDetailList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.APPROVE_QUOTATION_SAVE_FAIL);
        }

        return BusinessResponse.ok(newQuotationDetailList);
    }

    /**
     * 获取更新报价单主表的条件
     *
     * @param quotationInfo       报价单详情
     * @param quotationDetailList 报价单明细
     * @return LambdaUpdateWrapper<QuotationPO>
     */
    private LambdaUpdateWrapper<QuotationPO> getQuotationPOLambdaUpdateWrapper(QuotationPO quotationInfo, List<QuotationListPO> quotationDetailList) {
        LambdaUpdateWrapper<QuotationPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<QuotationPO>()
                .set(QuotationPO::getUpdatedBy, SecurityUtil.getAccountNoAndUserName())
                .set(QuotationPO::getUpdatedDate, LocalDateTime.now())
                .set(QuotationPO::getIsExamine, CommonConstant.FALSE)
                .set(QuotationPO::getExamineDate, LocalDateTime.now())
                .set(QuotationPO::getExamineId, SecurityUtil.getAccountNo())
                .set(QuotationPO::getExamineStatus, ExamineStatusConstant.AGREE)
                .eq(QuotationPO::getId, quotationInfo.getId());
        if (quotationDetailList.stream().allMatch(quotationListPO -> StringUtils.equals(quotationListPO.getQuotationStatus(), QuotationStatusEnum.FINISH.getStatus()))) {
            // 如果全部都是标准的

            // 价格相加
            BigDecimal totalPrice = quotationDetailList.stream().map(QuotationListPO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            lambdaUpdateWrapper.set(QuotationPO::getTotalPrice, totalPrice);
            // 设置报价状态
            lambdaUpdateWrapper.set(QuotationPO::getQuotationStatus, QuotationStatusConstant.FINISH);
        }
        return lambdaUpdateWrapper;
    }

    /**
     * 提交拒绝
     *
     * @param saveApproveQuotationRequestVO 待审核报价单的请求参数
     * @return BusinessResponse<Object>
     */
    private BusinessResponse<Object> refuse(SaveApproveQuotationRequestVO saveApproveQuotationRequestVO) {
        logger.info("exec save approve quotation refuse.");

        LocalDateTime nowTime = LocalDateTime.now();
        String updateBy = SecurityUtil.getAccountNoAndUserName();

        // 更新内容
        return quotationMapper.update(null, new LambdaUpdateWrapper<QuotationPO>()
                .set(QuotationPO::getQuotationStatus, QuotationStatusConstant.CLOSE)
                .set(QuotationPO::getExamineDate, nowTime)
                .set(QuotationPO::getExamineStatus, ExamineStatusConstant.REFUSE)
                .set(QuotationPO::getExamineId, updateBy)
                .set(QuotationPO::getIsExamine, CommonConstant.FALSE)
                .set(QuotationPO::getUpdatedDate, nowTime)
                .set(QuotationPO::getUpdatedBy, updateBy)
                .set(QuotationPO::getInvalidReason, saveApproveQuotationRequestVO.getReasonsRefusal())
                .eq(QuotationPO::getId, saveApproveQuotationRequestVO.getQuotationId())
        ) && quotationListMapper.update(null, new LambdaUpdateWrapper<QuotationListPO>()
                .set(QuotationListPO::getQuotationStatus, QuotationStatusConstant.CLOSE)
                .set(QuotationListPO::getUpdatedDate, nowTime)
                .set(QuotationListPO::getUpdatedBy, updateBy)
                .eq(QuotationListPO::getQuotationId, saveApproveQuotationRequestVO.getQuotationId())
        ) ? BusinessResponse.ok(null) : BusinessResponseCommon.fail(FaDocMarketingResponseEnum.APPROVE_QUOTATION_SAVE_FAIL);
    }

    /**
     * 获取价格
     *
     * @param quotationDetailList 报价单明细列表
     * @param companyInfo         企业信息信息
     * @param quotationInfo       报价单信息
     * @return List<QuotationResponseVO>
     */
    private List<QuotationResponseVO> getPrice(List<CreateQuotationDetailRequestVO> quotationDetailList, CompanyAndMerchandiserAndSalesmanInfoResponseVO companyInfo, QuotationPO quotationInfo) {
        logger.info("get quotation price and delivery.");

        // 设置查询的型号集合
        List<QuotationModelInfoRequestVO> quotationModelInfoRequestVOS = quotationDetailList.stream()
                .map(createQuotationDetailRequestVO ->
                        QuotationModelInfoRequestVO.builder()
                                .model(createQuotationDetailRequestVO.getCustomerModel())
                                .quantity(createQuotationDetailRequestVO.getQuantity())
                                .build())
                .collect(Collectors.toList());

        // 设置请求参数
        QuotationRequestVO quotationRequestVO = QuotationRequestVO.builder()
                .companyCode(Optional.ofNullable(companyInfo.getCompanyInfo()).map(CompanyInfo::getErpCompanyCode).orElse(FaDocMarketingCmsConstant.DEFAULT_CUSTOMER_CODE))
                .userRole(StringUtils.equals(quotationInfo.getQuotationLevel(), CompanyRoleEnum.ORD_CO.getRole()) ? CommonConstant.ONE : CommonConstant.TWO)
                .source(QuotationSourceConstant.ONLINE)
                .list(quotationModelInfoRequestVOS)
                .build();

        // 请求接口
        BusinessResponse<List<QuotationResponseVO>> modelPriceBusinessResponse = productBusinessInfoService.getModelPrice(quotationRequestVO);
        return modelPriceBusinessResponse.success() ? modelPriceBusinessResponse.getData() : new ArrayList<>();
    }

    /**
     * 生成报价单明细数据
     *
     * @param quotationDetailList   报价单详情列表
     * @param quotationResponseList 报价与交期的参数
     * @param quotationInfo         报价单详情
     * @return List<QuotationListPO>
     */
    private List<QuotationListPO> generateQuotationDetail(List<CreateQuotationDetailRequestVO> quotationDetailList, List<QuotationResponseVO> quotationResponseList, QuotationPO quotationInfo) {
        logger.info("approve quotation generate quotation detail.");

        // 用户序号的map
        Map<String, Integer> sortMap = new HashMap<>();
        sortMap.put(CommonConstant.EMPTY, CommonConstant.ONE);

        // 价格的list转map
        Map<String, QuotationResponseVO> quotationResponseVOMap = quotationResponseList.stream().collect(Collectors.toMap(QuotationResponseVO::getSort, Function.identity()));

        // 报价状态转map
        Map<Integer, String> quotationPriceStatusMap = Arrays.stream(QuotationPriceStatusEnum.values())
                .collect(Collectors.toMap(QuotationPriceStatusEnum::getStatus, QuotationPriceStatusEnum::getCode));

        // 商品状态映射
        Map<String, String> goodsStatusMap = QuotationUtil.setGoodsStatusMap();

        // 开始设置报价单明细的po
        return quotationDetailList.stream().map(createQuotationDetailRequestVO -> {
            // 设置基础的参数
            QuotationListPO quotationListPO = QuotationListPO.builder()
                    .quotationId(quotationInfo.getId())
                    .quotationNumber(quotationInfo.getQuotationNumber())
                    .sort(sortMap.get(CommonConstant.EMPTY))
                    .customerModel(createQuotationDetailRequestVO.getCustomerModel())
                    .quantity(createQuotationDetailRequestVO.getQuantity())
                    .oldQuantity(createQuotationDetailRequestVO.getQuantity())
                    .remark(createQuotationDetailRequestVO.getRemark())
                    .customerMaterialCode(createQuotationDetailRequestVO.getMaterialCode())
                    .customerProductName(createQuotationDetailRequestVO.getCustomerProductName())
                    .fileUrl(createQuotationDetailRequestVO.getFileUrl())
                    .categoryCode(createQuotationDetailRequestVO.getCategoryCode())
                    .confirmationStatus(null)
                    .recommend(CommonConstant.NO)
                    .build();
            // 设置id、创建人、创建时间
            quotationListPO.setId(UUIDUtils.getStringUUID());
            quotationListPO.setCreatedBy(quotationInfo.getCreatedBy());
            quotationListPO.setCreatedDate(quotationInfo.getCreatedDate());
            // 设置价格和交期等内容
            String sortSubscript = createQuotationDetailRequestVO.getQuantity() + CommonConstant.DASH + (quotationListPO.getSort() - CommonConstant.ONE);
            QuotationResponseVO quotationResponseVO = quotationResponseVOMap.get(sortSubscript);
            // 如果型号是标准的
            if (quotationResponseVO.isStandard()) {
                quotationListPO.setModel(quotationResponseVO.getModel());
                quotationListPO.setProductCode(quotationResponseVO.getCode());
                quotationListPO.setProductName(quotationResponseVO.getProductName());
                if (null != quotationResponseVO.getAluminumProfileValueOfFieldLong()) {
                    quotationListPO.setModelLong(quotationResponseVO.getAluminumProfileValueOfFieldLong().divide(BigDecimal.valueOf(1000L), CommonConstant.SIX, RoundingMode.HALF_UP));
                }
                quotationListPO.setMaterialQualityId(quotationResponseVO.getModelValueId());
                quotationListPO.setTechnicalSpecifications(quotationResponseVO.getTechnicalSpecifications());
                quotationListPO.setUnit(quotationResponseVO.getUnit());
                quotationListPO.setMaterialCode(quotationResponseVO.getMaterialCode());
            }
            // 如果是有交期和价格的
            if (quotationResponseVO.isHaveDelivery()
                    && StringUtils.isBlank(createQuotationDetailRequestVO.getFileUrl())
                    && StringUtils.isBlank(createQuotationDetailRequestVO.getRemark())) {
                QuotationUtil.setQuotationPOPriceAndDelivery(quotationListPO, quotationResponseVO);
            }

            // 设置有关价格的参数
            quotationListPO.setPlotId(quotationResponseVO.getModelValueId());
            quotationListPO.setPriceId(quotationResponseVO.getSourcePriceAutoId());
            quotationListPO.setAdditionalPriceId(quotationResponseVO.getSourcePriceAutoId());
            quotationListPO.setSupplierPriceOne(quotationResponseVO.getG1Price());
            quotationListPO.setSupplierPriceTwo(quotationResponseVO.getG2Price());
            quotationListPO.setSupplierPriceThree(quotationResponseVO.getG3Price());

            // 设置计算状态
            quotationListPO.setCalculationStatus(quotationPriceStatusMap.get(quotationResponseVO.getQuotedPriceStatus()));
            // 设置商品状态
            quotationListPO.setGoodsStatus(quotationResponseVO.isStandard() &&
                    quotationResponseVO.isHaveDelivery() ?
                    GoodsStatusEnum.NORMAL.getStatus() :
                    goodsStatusMap.getOrDefault(quotationResponseVO.getErrorType(), GoodsStatusEnum.WRONG_MODEL.getStatus()));
            // 设置报价状态
            quotationListPO.setQuotationStatus(QuotationUtil.setQuotationDetailStatus(quotationResponseVO, createQuotationDetailRequestVO));
            // 设置错误信息
            quotationListPO.setErrorMessage(quotationResponseVO.getMsg());
            // 设置是否标准
            quotationListPO.setStandardStatus(quotationResponseVO.isStandard() && quotationResponseVO.isHaveDelivery() ? CommonConstant.YES : CommonConstant.NO);
            // 排序加1
            sortMap.put(CommonConstant.EMPTY, quotationListPO.getSort() + CommonConstant.ONE);
            return quotationListPO;
        }).collect(Collectors.toList());
    }
}
