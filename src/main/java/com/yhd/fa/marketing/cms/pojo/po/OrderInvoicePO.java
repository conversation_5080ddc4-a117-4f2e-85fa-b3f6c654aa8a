package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_invoice")
public class OrderInvoicePO extends BaseEntity {
    /**
     * 发票单号
     */
    private String invoiceNumber;

    /**
     * 发票编码，从erp获取的
     */
    private String invoiceCode;

    /**
     * 发票类型  general 普通 dedicated 专用
     */
    private String invoiceType;

    /**
     * 发票性质 paper 纸质  electron 电子
     */
    private String invoiceNature;

    /**
     * 收货地址id
     */
    private String userAddressId;

    /**
     * 收货地址
     */
    private String userAddress;

    /**
     * 收货人
     */
    private String consignee;

    /**
     * 收货人联系方式
     */
    private String linkPhone;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 发票抬头
     */
    private String invCustomerName;

    /**
     * 发票税务登记号
     */
    private String invTaxRegNumber;

    /**
     * 发票公司地址
     */
    private String invAddress;

    /**
     * 发票公司电话
     */
    private String invLinkPhone;

    /**
     * 发票开户银行
     */
    private String invManuBank;

    /**
     * 发票银行账户
     */
    private String invAccount;

    /**
     * 发票状态 invoicing 开票中 invoiced 已开票 shipped 已发货 cancel 已取消
     */
    private String invoiceStatus;

    /**
     * 物流编码
     */
    private String logisticsCode;

    /**
     * 物流公司
     */
    private String logisticsName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 发票额度
     */
    private BigDecimal price;

    /**
     * 发货状态  shipped 已发货 unshipped 未发货
     */
    private String deliveryStatus;

    /**
     * 发送给erp状态
     */
    private String sendErpStatus;

    /**
     * 发送给erp时间
     */
    private LocalDateTime sendErpTime;

    /**
     * 错误提示
     */
    private String errorMsg;

    /**
     * 是否已通知业务员
     */
    private String remindSalesmen;

    /**
     * 发票文件信息;财务系统回传
     */
    private String invoiceFile;

    /**
     * 发票号;财务系统回传
     */
    private String erpInvoiceNumber;

    /**
     * 所属地区 DG东莞 SZ苏州
     */
    private String territory;
}

