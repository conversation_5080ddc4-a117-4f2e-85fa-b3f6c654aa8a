package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.dao.OrderCommentsCategoriesRelDAO;
import com.yhd.fa.marketing.cms.dao.OrderCommentsDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCommentsCategoriesRelMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsCategoriesRelPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRemarkRequestVO;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version Id: OrderCommentsReplyLogic.java, v 0.1 2023/3/6 15:17 Exp $
 */
@Component
public class OrderCommentsRemarkLogic {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderCommentsRemarkLogic.class.getName());

    /**
     * 订单评价DAO
     */
    @Resource
    private OrderCommentsDAO orderCommentsDAO;

    @Resource
    private OrderCommentsCategoriesRelDAO orderCommentsCategoriesRelDAO;
    @Resource
    private OrderCommentsCategoriesRelMapper orderCommentsCategoriesRelMapper;


    /**
     * 订单评价详情修改备注
     *
     * @param requestVO 请求参数
     * @return BusinessResponse<Object>
     */
    public BusinessResponse<Object> exec(OrderCommentsRemarkRequestVO requestVO) {
        logger.info("start order comments detail other edit logic.");

        //查询订单评论信息是否存在
        MPJLambdaWrapper<OrderCommentsPO> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(OrderCommentsPO.class).eq(OrderCommentsPO::getId, requestVO.getCommentsId());

        OrderCommentsPO orderCommentsPO = orderCommentsDAO.selectOne(queryWrapper);
        if (ObjectUtil.isNull(orderCommentsPO)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_COMMENTS_INFO_NOT_EXIST);
        }
        //判断是否已回复
        if (StringUtils.equals(orderCommentsPO.getReplied(), CommonConstant.TRUE)) {
            //设置更新条件构造器
            LambdaUpdateWrapper<OrderCommentsPO> updateWrapper = new LambdaUpdateWrapper<OrderCommentsPO>().eq(OrderCommentsPO::getId, requestVO.getCommentsId());

            updateWrapper
                    .set(OrderCommentsPO::getRemark, requestVO.getRemark())
                    .set(OrderCommentsPO::getVisitRecords, requestVO.getVisitRecords());

            updateWrapper.set(OrderCommentsPO::getUpdatedDate, LocalDate.now());
            orderCommentsDAO.update(null, updateWrapper);
            updateCategoriesRelId(orderCommentsPO, requestVO.getProblemCategoriesId());

            orderCommentsDAO.update(null, updateWrapper);
        }
        return BusinessResponseCommon.ok(null);

    }

    /**
     * 更新问题分类关联关系
     *
     * @param orderCommentsPO      订单评论信息
     * @param problemCategoriesIds 问题分类id
     */
    private void updateCategoriesRelId(OrderCommentsPO orderCommentsPO, List<Integer> problemCategoriesIds) {

        // 先删除旧记录， 如果没有传分类id则清空
        orderCommentsCategoriesRelDAO.delete(
                new LambdaUpdateWrapper<OrderCommentsCategoriesRelPO>()
                        .eq(OrderCommentsCategoriesRelPO::getCommentId, orderCommentsPO.getId())
        );

        if (null != problemCategoriesIds && !problemCategoriesIds.isEmpty()) {

            // 构建分类关系对象列表
            List<OrderCommentsCategoriesRelPO> categoriesRelList = problemCategoriesIds.stream()
                    .map(categoryId -> OrderCommentsCategoriesRelPO.builder()
                            .commentId(orderCommentsPO.getId())
                            .categoryId(categoryId)
                            .createdBy(SecurityUtil.getAccountNoAndUserName())
                            .createdDate(LocalDateTime.now())
                            .build())
                    .collect(Collectors.toList());

            // 保存新记录
            orderCommentsCategoriesRelMapper.saveBatch(categoriesRelList);
        }
    }
}
