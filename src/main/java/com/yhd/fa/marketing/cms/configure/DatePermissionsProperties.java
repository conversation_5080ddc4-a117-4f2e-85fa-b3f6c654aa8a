package com.yhd.fa.marketing.cms.configure;

import cn.hutool.extra.spring.SpringUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * &#064;Date  2024/8/07 14:39
 */
@ConfigurationProperties(prefix = "data.permissions", ignoreUnknownFields = false)
@Validated
@Data
public class DatePermissionsProperties {
    /**
     * 查看所有数据角色集合
     */
    private List<String> allDataRoleCodeList = new ArrayList<>();
    /**
     * 自定义角色集合
     */
    private List<String> customizeCodeList = new ArrayList<>();

    public static DatePermissionsProperties getInstance(){
        return SpringUtil.getBean(DatePermissionsProperties.class);
    }
}
