package com.yhd.fa.marketing.cms.sao;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.AddUserIntegralRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.BatchAddUserIntegralRequestVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version Id: IntegralCenterSAO.java, v 0.1 2023/3/14 15:02 JiangYuHong Exp $
 */
@FeignClient(name = "yhd-service-integral-cms")
public interface IntegralCenterSAO {


    /**
     * 添加积分
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @PostMapping(value = "/integral/rule/v1/0/addUserIntegral", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    BusinessResponse<Object> addUserIntegral(@RequestBody AddUserIntegralRequestVO requestVO);


    /**
     * 批量添加积分（慎用，应与积分中心沟通批量添加的时间）
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @PostMapping(value = "/integral/rule/v1/0/batchAddUserIntegral", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    BusinessResponse<Object> batchAddUserIntegral(@RequestBody BatchAddUserIntegralRequestVO requestVO);
}
