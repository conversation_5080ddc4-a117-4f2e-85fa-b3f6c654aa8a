package com.yhd.fa.marketing.cms.dao;

import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.LastOrderDateResponseVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderDAO.java, v0.1 2023/2/17 15:03 yehuasheng Exp $
 */
@Repository("order")
public interface OrderDAO extends MPJBaseMapper<OrderPO> {
    List<LastOrderDateResponseVO> getLastOrderDate(List<String> userCodeList);

    @Select("<script>" +
            "SELECT count(0) FROM (" +
            "${fullSql} " +
            ") table_count;" +
            "</script>")
    Integer countOrderList(@Param("fullSql") String fullSql);
}
