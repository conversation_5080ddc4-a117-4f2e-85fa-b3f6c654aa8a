package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.QuotationPeriodConstant;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: QuotationInfoRequestVO.java, v0.1 2022/12/2 10:56 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuotationInfoRequestVO extends PageRequestVO {
    /**
     * 报价单号
     */
    @Schema(description = "报价单号 至少2个字符", example = "YB2022111614493397793L6J")
    private String quotationNumber;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称 至少2个字符", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-01-01 12:12:12", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-01-01 12:12:12", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 报价单状态
     */
    @Schema(description = "状态 报价状态，quotation快速报价中，finish报价完成，close已终止，outTime超出报价有效期", example = "finish")
    @ValueInEnum(matchTarget = {QuotationStatusConstant.CLOSE,
            QuotationStatusConstant.QUOTATION,
            QuotationStatusConstant.FINISH,
            QuotationStatusConstant.OUT_TIME}, message = "请选择正确的报价单状态")
    private String quotationStatus;

    /**
     * 报价时效
     */
    @Schema(description = "报价时效 oneHours1小时 twoHours2小时 threeHours3小时 seventyTwoHours72小时（3天）", example = "oneHours")
    @ValueInEnum(matchTarget = {QuotationPeriodConstant.ONE_HOURS,
            QuotationPeriodConstant.TWO_HOURS,
            QuotationPeriodConstant.THREE_HOURS,
            QuotationPeriodConstant.SEVENTY_TWO_HOURS}, message = "请选择正确的报价时效")
    private String quotationPeriod;

    /**
     * 是否转订单
     */
    @Schema(description = "是否转订单 true是 false不是", example = "true")
    @ValueInEnum(matchTarget = {CommonConstant.TRUE, CommonConstant.FALSE}, message = "请选择正确的是否已转订单")
    private String transferOrder;

    /**
     * 建单内部人员工号
     */
    @Schema(description = "建单内部人员工号", example = "000001")
    private String insideEmployeeCode;
}
