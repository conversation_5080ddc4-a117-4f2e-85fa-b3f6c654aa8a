package com.yhd.fa.marketing.cms;

import com.yhd.dynamic.ann.EnableDynamicDataSource;
import com.yhd.fa.marketing.cms.configure.DatePermissionsProperties;
import com.yhd.feign.ann.EnableYHDFeignClients;
import com.yhd.job.config.ann.EnableJob;
import com.yhd.lock.ann.EnableYHDLocks;
import com.yhd.lock.properties.RedisConnType;
import com.yhd.springdoc.EnableSpringDoc;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableDynamicDataSource
@EnableSpringDoc
@EnableYHDFeignClients
@EnableDiscoveryClient
@SpringBootApplication(exclude = {OAuth2AutoConfiguration.class, DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.yhd.ucs", "com.yhd.fa.marketing.cms"})
@EnableYHDLocks(redisConnType = RedisConnType.CLUSTER)
@EnableJob
@EnableAsync
@MapperScan(basePackages = "com.yhd.fa.marketing.cms.dao")
@EnableConfigurationProperties({DatePermissionsProperties.class})
public class SrvFaMarketingCmsApplication {

    public static void main(String[] args) {
        System.setProperty("rocketmq.client.logRoot", "/data/tomcat/logs/yhd_service");
        SpringApplication.run(SrvFaMarketingCmsApplication.class, args);
    }

}