package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.*;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.SynchronizeStatusEnum;
import com.yhd.fa.marketing.cms.mapper.OrderListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderMapper;
import com.yhd.fa.marketing.cms.mq.producer.SynchronizationProducer;
import com.yhd.fa.marketing.cms.pojo.dto.SynchronizationCreateOrderDataDTO;
import com.yhd.fa.marketing.cms.pojo.dto.SynchronizationCreateOrderDetailDataDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderListPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.QuotationUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SynchronizationCreateOrderLogic.java, v0.1 2023/2/27 16:43 yehuasheng Exp $
 */
@Component
public class SynchronizationCreateOrderLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(SynchronizationCreateOrderLogic.class.getName());

    /**
     * 订单详情mapper
     */
    @Resource
    private OrderMapper orderMapper;

    /**
     * 订单明细mapper
     */
    @Resource
    private OrderListMapper orderListMapper;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 同步的生产者
     */
    @Resource
    private SynchronizationProducer synchronizationProducer;

    /**
     * 执行同步
     *
     * @param orderId 订单id
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<Void> exec(String orderId) {
        logger.info("start exec synchronization create order logic.");

        // 查询订单详情
        OrderPO orderInfo = orderMapper.getOne(new MPJLambdaWrapper<OrderPO>()
                .selectAll(OrderPO.class)
                .eq(OrderPO::getId, orderId)
                .last(FaDocMarketingCmsConstant.LIMIT)
        );
        if (ObjectUtil.isNull(orderInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_IS_NOT_EXISTS);
        }

        //判断线上订单是否已经支付
        if (StringUtils.equals(orderInfo.getSettlementType(), SettlementTypeConstant.ONLINE) &&
                !StringUtils.equals(orderInfo.getPaymentType(), PaymentTypeConstant.BANK_TRANSFER) &&
                StringUtils.equals(orderInfo.getPaymentStatus(), PaymentStatusConstant.UNPAID)) {
            //订单未支付,不能同步
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_IS_NOT_PAY);
        }

        // 查询订单明细
        List<OrderListPO> orderList = orderListMapper.list(new MPJLambdaWrapper<OrderListPO>().selectAll(OrderListPO.class).eq(OrderListPO::getOrderId, orderId));
        if (CollUtil.isEmpty(orderList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_DETAIL_IS_NOT_EXISTS);
        }

        // 订单明细折扣*100
        orderList.forEach(orderListPO -> {
            Optional.ofNullable(orderListPO.getQuantityDiscount())
                    .ifPresent(quantityDiscountRate
                            -> orderListPO.setQuantityDiscount(quantityDiscountRate.multiply(QuotationUtil.HUNDRED_BIGDECIMAL).setScale(CommonConstant.TWO, RoundingMode.HALF_UP)));

            Optional.ofNullable(orderListPO.getTotalDiscount())
                    .ifPresent(totalDiscountRate
                            -> orderListPO.setTotalDiscount(totalDiscountRate.multiply(QuotationUtil.HUNDRED_BIGDECIMAL).setScale(CommonConstant.TWO, RoundingMode.HALF_UP)));
        });

        // 执行同步
        synchronizationCreateOrder(orderInfo, orderList);

        return BusinessResponse.ok(null);
    }

    /**
     * 同步创建订单
     *
     * @param orderInfo 订单详情
     * @param orderList 订单明细
     */
    @Async("faSynchronizationCreateOrder")
    public void synchronizationCreateOrder(OrderPO orderInfo, List<OrderListPO> orderList) {
        logger.info("start synchronization create order mq.");

        // 转换同步订单明细
        List<SynchronizationCreateOrderDetailDataDTO> synchronizationCreateOrderDetailData = setSynchronizationOrderDetailData(orderList);

        // 转换同步订单详情
        SynchronizationCreateOrderDataDTO synchronizationCreateOrderData = setSynchronizationCreateOrderData(orderInfo, synchronizationCreateOrderDetailData);

        // 同步数据
        synchronizationProducer.synchronizationData(JSON.toJSONString(synchronizationCreateOrderData), BusinessTypeConstant.CREATE_ORDER, orderInfo.getOrderNumber());

        // 更新同步状态
        orderMapper.update(null,
                new LambdaUpdateWrapper<OrderPO>()
                        .set(OrderPO::getSynchronizeStatus, SynchronizeStatusEnum.SYNCHRONIZING.getStatus())
                        .set(OrderPO::getUpdatedDate, LocalDateTime.now())
                        .eq(OrderPO::getId, orderInfo.getId())
        );
    }

    /**
     * 设置同步订单明细数据
     *
     * @param orderList 订单明细
     * @return List<SynchronizationCreateOrderDetailDataDTO>
     */
    private List<SynchronizationCreateOrderDetailDataDTO> setSynchronizationOrderDetailData(List<OrderListPO> orderList) {
        logger.info("set synchronization order detail data.");

        return orderList.stream().map(orderListPO ->
                SynchronizationCreateOrderDetailDataDTO
                        .builder()
                        .sortId(orderListPO.getSortId())
                        .quotationNumber(orderListPO.getQuotationNumber())
                        .quotationSortId(orderListPO.getQuotationSortId())
                        .productCode(orderListPO.getProductCode())
                        .productName(orderListPO.getProductName())
                        .productModel(orderListPO.getProductModel())
                        .customerModel(orderListPO.getCustomerModel())
                        .customerMaterialCode(orderListPO.getCustomerMaterialCode())
                        .customerProductName(orderListPO.getCustomerProductName())
                        .modelLong(orderListPO.getModelLong())
                        .quantity(orderListPO.getQuantity())
                        .isStandard(orderListPO.getIsStandard())
                        .price(orderListPO.getPrice())
                        .discountPrice(orderListPO.getDiscountPrice())
                        .taxDiscountPrice(orderListPO.getTaxDiscountPrice())
                        .totalPrice(orderListPO.getTotalPrice())
                        .payablePrice(orderListPO.getPayablePrice())
                        .quantityDiscount(orderListPO.getQuantityDiscount())
                        .totalDiscount(orderListPO.getTotalDiscount())
                        .delivery(orderListPO.getDelivery())
                        .shipDate(orderListPO.getShipDate())
                        .unit(orderListPO.getUnit())
                        .fileUrl(orderListPO.getFileUrl())
                        .technicalSpecifications(orderListPO.getTechnicalSpecifications())
                        .orderDetailStatus(orderListPO.getOrderDetailStatus())
                        .examineRemark(orderListPO.getExamineRemark())
                        .build()
        ).collect(Collectors.toList());
    }

    /**
     * 同步创建订单
     *
     * @param orderInfo                            订单详情
     * @param synchronizationCreateOrderDetailData 同步数据
     * @return SynchronizationCreateOrderDataDTO
     */
    private SynchronizationCreateOrderDataDTO setSynchronizationCreateOrderData(OrderPO orderInfo, List<SynchronizationCreateOrderDetailDataDTO> synchronizationCreateOrderDetailData) {
        logger.info("set synchronization create order data.");

        // 获取用户信息
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(orderInfo.getUserCode(), orderInfo.getCompanyCode());

        // 组装同步订单的详情数据
        return SynchronizationCreateOrderDataDTO.builder()
                .id(orderInfo.getId())
                .orderNumber(orderInfo.getOrderNumber())
                .orderLevel(orderInfo.getOrderLevel())
                .customerNumber(orderInfo.getCustomerNumber())
                .userCode(orderInfo.getUserCode())
                .companyCode(orderInfo.getCompanyCode())
                .erpCustomerCode(ObjectUtil.isNotNull(userInfo.getCompanyInfo()) ? userInfo.getCompanyInfo().getErpCompanyCode() : null)
                .companyName(orderInfo.getCompanyName())
                .settlementType(orderInfo.getSettlementType())
                .paymentStatus(orderInfo.getPaymentStatus())
                .paymentType(orderInfo.getPaymentType())
                .tradeNo(orderInfo.getTradeNo())
                .mchOrderNo(orderInfo.getMchOrderNo())
                .payDate(orderInfo.getPayDate())
                .totalPrice(orderInfo.getTotalPrice())
                .payablePrice(orderInfo.getPayablePrice())
                .synchronizeStatus(orderInfo.getSynchronizeStatus())
                .contactNumber(orderInfo.getContactNumber())
                .consignee(orderInfo.getConsignee())
                .province(orderInfo.getProvince())
                .city(orderInfo.getCity())
                .town(orderInfo.getTown())
                .provinceId(orderInfo.getProvinceId())
                .cityId(orderInfo.getCityId())
                .townId(orderInfo.getTownId())
                .addressId(orderInfo.getAddressId())
                .address(orderInfo.getAddress())
                .deleteStatus(orderInfo.getDeleteStatus())
                .cancelStatus(orderInfo.getCancelStatus())
                .orderStatus(orderInfo.getOrderStatus())
                .shipping(orderInfo.getShipping())
                .firstShippingDay(orderInfo.getFirstShippingDay())
                .remark(orderInfo.getRemark())
                .orderSource(orderInfo.getOrderSource())
                .orderChannel(orderInfo.getOrderChannel())
                .ipLocation(orderInfo.getIpLocation())
                .territory(orderInfo.getTerritory())
                .createdDate(orderInfo.getCreatedDate())
                .userName(orderInfo.getCreatedBy())
                .orderDetails(synchronizationCreateOrderDetailData)
                .build();
    }
}
