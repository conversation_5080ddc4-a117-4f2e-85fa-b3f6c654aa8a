package com.yhd.fa.marketing.cms.service.impl.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.BaseUserInfoRequestVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.CompanyAndMerchandiserAndSalesmanInfoRequestVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.CompanyInfoListByJobNumberRequestVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndMerchandiserAndSalesmanInfoResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.MerchandiserInfo;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.UserInfo;
import com.yhd.buc.cms.api.sdk.utils.UserApiUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.BaseConstant;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.OrderSourceConstant;
import com.yhd.fa.marketing.cms.dao.OrderDAO;
import com.yhd.fa.marketing.cms.dao.OrderErpLogisticsDAO;
import com.yhd.fa.marketing.cms.dao.OrderErpLogisticsListDAO;
import com.yhd.fa.marketing.cms.dao.OrderListDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.po.*;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbOrderPageRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationAndOrderDeleteRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.*;
import com.yhd.fa.marketing.cms.service.controller.FbOrderApiService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/3/19 8:55
 */
@Service
public class FbOrderApiImpl implements FbOrderApiService {

    @Resource
    private OrderDAO orderDAO;

    @Resource
    private OrderListDAO orderListDAO;

    @Resource
    private OrderErpLogisticsDAO orderErpLogisticsDAO;

    @Resource
    private OrderErpLogisticsListDAO orderErpLogisticsListDAO;

    private static final Logger logger = LogUtils.getLogger(FbOrderApiImpl.class.getName());


    /**
     * Fb订单列表数据获取
     *
     * @param fbOrderPageRequestVO
     * @return
     */
    @Override
    public BusinessResponse<PageInfo<FbOrderListResponseVO>> getFbOrderPage(FbOrderPageRequestVO fbOrderPageRequestVO) {
        logger.info("access FbOrderApiImpl  getFbOrderList ...");
        fbOrderPageRequestVO.parseDate();

        String orderId = fbOrderPageRequestVO.getOrderId();

        String orderNumber = fbOrderPageRequestVO.getOrderNumber();

        List<String> orderStatus = fbOrderPageRequestVO.getOrderStatus();

        String companyName = fbOrderPageRequestVO.getCompanyName();

        String startDate = fbOrderPageRequestVO.getStartDate();

        String endDate = fbOrderPageRequestVO.getEndDate();

        List<String> idList = fbOrderPageRequestVO.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            idList = new ArrayList<>();
        }

        String orderSource = fbOrderPageRequestVO.getOrderSource();

        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getQuotationNumber())) {
            List<OrderListPO> orderListPOS = orderListDAO.selectList(new LambdaQueryWrapper<OrderListPO>()
                    .eq(OrderListPO::getQuotationNumber, fbOrderPageRequestVO.getQuotationNumber()));
            if (CollectionUtils.isNotEmpty(orderListPOS)) {
                List<String> quotationOrderIdList = orderListPOS.stream().map(OrderListPO::getOrderId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(idList)) {
                    idList.addAll(quotationOrderIdList);

                } else {
                    idList.retainAll(quotationOrderIdList);
                    if (CollectionUtils.isEmpty(idList)) {
                        return BusinessResponse.ok(new PageInfo<>());
                    }
                }

            } else {
                return BusinessResponse.ok(PageInfo.of(new ArrayList<>()));
            }
        }


        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OrderPO::getId,
                        OrderPO::getOrderNumber,
                        OrderPO::getOrderStatus,
                        OrderPO::getDeleteStatus,
                        OrderPO::getShipping,
                        OrderPO::getCompanyCode,
                        OrderPO::getPaymentType,
                        OrderPO::getPayDate,
                        OrderPO::getReceivedTime,
                        OrderPO::getTradeNo,
                        OrderPO::getUserCode,
                        OrderPO::getRemark,
                        OrderPO::getCreatedDate,
                        OrderPO::getTotalPrice,
                        OrderPO::getOrderSource)
                .eq(OrderPO::getPlatformCode, BaseConstant.PLATFORM_FB)
                .eq(OrderPO::getDeleteStatus, BaseConstant.FALSE);


        if (CollectionUtils.isNotEmpty(idList)) {
            queryWrapper.in(OrderPO::getId, idList);
        }

        if (StringUtils.isNotBlank(orderId)) {
            queryWrapper.eq(OrderPO::getId, orderId);
        }

        if (StringUtils.isNotBlank(orderNumber)) {
            queryWrapper.like(OrderPO::getOrderNumber, orderNumber);
        }


        if (CollectionUtils.isNotEmpty(orderStatus)) {
            queryWrapper.in(OrderPO::getOrderStatus, orderStatus);
        }

        if (StringUtils.isNotBlank(companyName)) {
            queryWrapper.like(OrderPO::getCompanyName, companyName);
        }

        if (StringUtils.isNotBlank(startDate)) {
            queryWrapper.ge(OrderPO::getCreatedDate, startDate);
        }

        if (StringUtils.isNotBlank(endDate)) {
            queryWrapper.le(OrderPO::getCreatedDate, endDate);
        }

        if (StringUtils.isNotBlank(orderSource)) {
            if (orderSource.equals(OrderSourceConstant.FB_BUY)) {
                queryWrapper.eq(OrderPO::getOrderSource, orderSource);
            } else if (orderSource.equals(OrderSourceConstant.FB_QUOTATION)) {
                queryWrapper.and(p -> p.eq(OrderPO::getOrderSource, OrderSourceConstant.MERGE_QUOTATION).or()
                        .eq(OrderPO::getOrderSource, OrderSourceConstant.QUOTATION));
            }
        }

        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getOrderByField())) {
            queryWrapper.last(" order by " + StrUtil.toUnderlineCase(fbOrderPageRequestVO.getOrderByField()) + " " + fbOrderPageRequestVO.getOrderByType());
        } else {
            queryWrapper.orderByDesc(OrderPO::getCreatedDate);
        }

        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getEmpNo())) {
            //获取当前员工下的企业编码
            CompanyInfoListByJobNumberRequestVO companyInfoListByJobNumberRequestVO = new CompanyInfoListByJobNumberRequestVO();

            companyInfoListByJobNumberRequestVO.setJobNumber(fbOrderPageRequestVO.getEmpNo());

            companyInfoListByJobNumberRequestVO.setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE);

            List<String> companyCodeList = UserApiUtil.getCompanyCodeListByJobNumberMethod(companyInfoListByJobNumberRequestVO);
            if (CollectionUtils.isNotEmpty(companyCodeList)) {
                queryWrapper.in(OrderPO::getCompanyCode, companyCodeList);
            } else {
                return BusinessResponse.ok(PageInfo.of(new ArrayList<>()));
            }

        }

        if (fbOrderPageRequestVO.getPageNum() != -1) {
            PageHelper.startPage(fbOrderPageRequestVO.getPageNum(), fbOrderPageRequestVO.getPageSize());
        }
        List<OrderPO> orderPOList = orderDAO.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(orderPOList)) {
            return BusinessResponse.ok(PageInfo.of(new ArrayList<>()));
        }

        List<String> orderIds = orderPOList.stream().map(OrderPO::getId).collect(Collectors.toList());
        List<OrderListPO> orderListPOS = orderListDAO.selectList(new LambdaQueryWrapper<OrderListPO>()
                .in(OrderListPO::getOrderId, orderIds));
        Map<String, List<OrderListPO>> listMap = orderListPOS.stream().collect(Collectors.groupingBy(OrderListPO::getOrderId));

        PageInfo<OrderPO> of = PageInfo.of(orderPOList);

        PageInfo<FbOrderListResponseVO> pageResponseVOPageInfo =
                JSON.parseObject(JSON.toJSONString(of), new TypeReference<PageInfo<FbOrderListResponseVO>>() {
                });

        List<String> companyCodeList = orderPOList.stream().map(OrderPO::getCompanyCode).distinct().collect(Collectors.toList());
        List<String> userCodeList = orderPOList.stream().map(OrderPO::getUserCode).distinct().collect(Collectors.toList());

        Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyInoMap = new HashMap<>(16);

        if (CollectionUtils.isNotEmpty(companyCodeList)) {
            //获取跟单信息
            CompanyAndMerchandiserAndSalesmanInfoRequestVO companyAndMerchandiserAndSalesmanInfoRequestVO = new CompanyAndMerchandiserAndSalesmanInfoRequestVO();

            companyAndMerchandiserAndSalesmanInfoRequestVO.setPlatformCode(BaseConstant.PLATFORM_FB);

            companyAndMerchandiserAndSalesmanInfoRequestVO.setCompanyCodeList(companyCodeList);

            List<CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyAndMerchandiserAndSalesmanInfoResponseVOS = UserApiUtil.companyAndMerchandiserAndSalesmanInfo(companyAndMerchandiserAndSalesmanInfoRequestVO);

            Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> merchandiserAndSalesmanInfoResponseVOMap =
                    companyAndMerchandiserAndSalesmanInfoResponseVOS.stream().collect(Collectors.toMap(CompanyAndMerchandiserAndSalesmanInfoResponseVO::getCompanyCode, v -> v, (k1, k2) -> k1));

            companyInoMap.putAll(merchandiserAndSalesmanInfoResponseVOMap);
        }

        BaseUserInfoRequestVO baseUserInfoRequestVO = new BaseUserInfoRequestVO();
        baseUserInfoRequestVO.setUserCodeList(new HashSet<>(userCodeList));
        List<UserInfo> userInfoByUserCodesOrUserAccounts = UserApiUtil.getUserInfoByUserCodesOrUserAccounts(baseUserInfoRequestVO);

        Map<String, UserInfo> userInfoMap = userInfoByUserCodesOrUserAccounts.stream().collect(Collectors.toMap(UserInfo::getUserCode, user -> user, (k1, k2) -> k1));
        Map<String, List<OrderListPO>> finalListMap = listMap;
        List<FbOrderListResponseVO> fbOrderListResponseVOList = orderPOList.stream().map(m -> {
            if ((m.getOrderSource().equals(OrderSourceConstant.MERGE_QUOTATION)) || (m.getOrderSource().equals(OrderSourceConstant.QUOTATION))) {
                m.setOrderSource(OrderSourceConstant.FB_QUOTATION);
            }
            UserInfo userInfo = userInfoMap.get(m.getUserCode());
            List<String> quotationNumber = new ArrayList<>();
            if (!finalListMap.isEmpty() && CollectionUtils.isNotEmpty(finalListMap.get(m.getId()))) {
                quotationNumber = finalListMap.get(m.getId()).stream().map(OrderListPO::getQuotationNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            }

            FbOrderListResponseVO build = FbOrderListResponseVO
                    .builder()
                    .id(m.getId())
                    .orderNumber(m.getOrderNumber())
                    .orderStatus(m.getOrderStatus())
                    .deleteStatus(m.getDeleteStatus())
                    .shipping(m.getShipping())
                    .paymentType(m.getPaymentType())
                    .payDate(m.getPayDate() == null ? null : m.getPayDate().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                    .receivedTime(m.getReceivedTime() == null ? null : m.getReceivedTime().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                    .tradeNo(m.getTradeNo())
                    .createdUser(userInfo == null ? null : String.join(CommonConstant.EMPTY, userInfo.getUserName(), CommonConstant.LEFT_BRACKET, userInfo.getUserCode(), CommonConstant.RIGHT_BRACKET))
                    .userContactNumber(userInfo == null ? null : (StringUtils.isNotBlank(userInfo.getMobile()) ? userInfo.getMobile() : userInfo.getEmail()))
                    .remark(m.getRemark())
                    .createdDate(m.getCreatedDate() == null ? null : m.getCreatedDate().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                    .totalPrice(m.getTotalPrice())
                    .orderSource(m.getOrderSource())
                    .quotationNumber(String.join(",", quotationNumber))
                    .build();

            String companyCode = m.getCompanyCode();

            CompanyAndMerchandiserAndSalesmanInfoResponseVO companyAndMerchandiserAndSalesmanInfoResponseVO = companyInoMap.get(companyCode);

            //设置业务员、专属跟单
            if (Objects.nonNull(companyAndMerchandiserAndSalesmanInfoResponseVO)) {
                MerchandiserInfo merchandiserInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getMerchandiserInfo();
                if (Objects.nonNull(merchandiserInfo)) {
                    String join = String.join(CommonConstant.EMPTY, merchandiserInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, merchandiserInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET);
                    build.setMerchandiser(join);
                }

                MerchandiserInfo salesManInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getSalesManInfo();
                if (Objects.nonNull(salesManInfo)) {
                    String join = String.join(CommonConstant.EMPTY, salesManInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, salesManInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET);
                    build.setSalesMan(join);
                }
            }
            return build;
        }).collect(Collectors.toList());

        pageResponseVOPageInfo.setList(fbOrderListResponseVOList);
        return BusinessResponse.ok(pageResponseVOPageInfo);
    }

    /**
     * Fb订单详情数据获取
     *
     * @param id
     * @return
     */
    @Override
    public BusinessResponse<FbOrderDetailResponseVO> getFbOrderDetail(String id) {

        //查询订单主表
        LambdaQueryWrapper<OrderPO> orderQueryWrapper = new LambdaQueryWrapper<>();

        orderQueryWrapper.select(OrderPO::getId,
                        OrderPO::getOrderNumber,
                        OrderPO::getOrderStatus,
                        OrderPO::getDeleteStatus,
                        OrderPO::getShipping,
                        OrderPO::getCompanyCode,
                        OrderPO::getPaymentType,
                        OrderPO::getPayDate,
                        OrderPO::getReceivedTime,
                        OrderPO::getTradeNo,
                        OrderPO::getUserCode,
                        OrderPO::getRemark,
                        OrderPO::getCreatedDate,
                        OrderPO::getTotalPrice,
                        OrderPO::getCustomerNumber)
                .eq(OrderPO::getId, id);

        OrderPO orderPO = orderDAO.selectOne(orderQueryWrapper);

        if (Objects.isNull(orderPO)) {
            return BusinessResponse.fail(FaDocMarketingResponseEnum.ORDER_IS_NOT_EXISTS.getCode(), FaDocMarketingResponseEnum.ORDER_IS_NOT_EXISTS.getDesc());
        }

        //查询订单明细表
        LambdaQueryWrapper<OrderListPO> orderDetailQueryWrapper = new LambdaQueryWrapper<>();
        orderDetailQueryWrapper
                .select(OrderListPO::getId,
                        OrderListPO::getSortId,
                        OrderListPO::getOrderDetailStatus,
                        OrderListPO::getQuantity,
                        OrderListPO::getExamineRemark,
                        OrderListPO::getRemark,
                        OrderListPO::getDiscountPrice,
                        OrderListPO::getTaxDiscountPrice,
                        OrderListPO::getTotalPrice,
                        OrderListPO::getReplyToDelivery,
                        OrderListPO::getEstimatedShippingDate
                )
                .eq(OrderListPO::getOrderId, id);

        List<OrderListPO> orderListPOS = orderListDAO.selectList(orderDetailQueryWrapper);

        if (CollectionUtils.isEmpty(orderListPOS)) {
            return BusinessResponse.fail(FaDocMarketingResponseEnum.ORDER_DETAIL_IS_NOT_EXISTS.getCode(), FaDocMarketingResponseEnum.ORDER_DETAIL_IS_NOT_EXISTS.getDesc());
        }


        //获取明细已发货数量
        Map<Integer, Long> sendMap = getSendList(orderPO);

        BaseUserInfoRequestVO baseUserInfoRequestVO = new BaseUserInfoRequestVO();
        baseUserInfoRequestVO.setUserCodeList(new HashSet<>(Arrays.asList(orderPO.getUserCode())));
        List<UserInfo> userInfoByUserCodesOrUserAccounts = UserApiUtil.getUserInfoByUserCodesOrUserAccounts(baseUserInfoRequestVO);

        Map<String, UserInfo> userInfoMap = userInfoByUserCodesOrUserAccounts.stream().collect(Collectors.toMap(UserInfo::getUserCode, user -> user, (k1, k2) -> k1));
        UserInfo userInfo = userInfoMap.get(orderPO.getUserCode());
        //初始化返参
        FbOrderDetailResponseVO fbOrderDetailResponseVO = FbOrderDetailResponseVO
                .builder()
                .id(orderPO.getId())
                .orderNumber(orderPO.getOrderNumber())
                .orderStatus(orderPO.getOrderStatus())
                .deleteStatus(orderPO.getDeleteStatus())
                .shipping(orderPO.getShipping())
                .paymentType(orderPO.getPaymentType())
                .payDate(orderPO.getPayDate() == null ? null : orderPO.getPayDate().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                .receivedTime(orderPO.getReceivedTime() == null ? null : orderPO.getReceivedTime().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                .tradeNo(orderPO.getTradeNo())
                .createdUser(userInfo == null ? null : String.join(CommonConstant.EMPTY, userInfo.getUserName(), CommonConstant.LEFT_BRACKET, userInfo.getUserCode(), CommonConstant.RIGHT_BRACKET))
                .userContactNumber(userInfo == null ? null : (StringUtils.isNotBlank(userInfo.getMobile()) ? userInfo.getMobile() : userInfo.getEmail()))
                .remark(orderPO.getRemark())
                .createdDate(orderPO.getCreatedDate() == null ? null : orderPO.getCreatedDate().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                .totalPrice(orderPO.getTotalPrice())
                .customerNumber(orderPO.getCustomerNumber())
                .build();

        //组装订单明细项返参
        List<FbOrderDetailResponseVO.FbOrderDetailList> fbOrderDetailListList = orderListPOS.stream().map(m -> {
            FbOrderDetailResponseVO.FbOrderDetailList fbOrderDetailList = fbOrderDetailResponseVO.initFbOrderDetailList();
            fbOrderDetailList.setOrderDetailStatus(m.getOrderDetailStatus());
            fbOrderDetailList.setId(m.getId());
            fbOrderDetailList.setSortId(m.getSortId());
            fbOrderDetailList.setQuantity(m.getQuantity());
            fbOrderDetailList.setRemark(m.getRemark());
            fbOrderDetailList.setExamineRemark(m.getExamineRemark());
            fbOrderDetailList.setDiscountPrice(m.getDiscountPrice());
            fbOrderDetailList.setTotalPrice(m.getTotalPrice());
            fbOrderDetailList.setTaxDiscountPrice(m.getTaxDiscountPrice());
            fbOrderDetailList.setReplyToDelivery(m.getReplyToDelivery() == null ? null : m.getReplyToDelivery().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            fbOrderDetailList.setEstimatedShippingDate(m.getEstimatedShippingDate() == null ? null : m.getEstimatedShippingDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            if (MapUtils.isNotEmpty(sendMap)) {
                Long aLong = sendMap.get(m.getSortId());
                fbOrderDetailList.setSendQuantity(aLong);
            } else {
                fbOrderDetailList.setSendQuantity(0L);
            }
            return fbOrderDetailList;
        }).collect(Collectors.toList());

        //设置订单明细
        fbOrderDetailResponseVO.setDetailList(fbOrderDetailListList);

        //获取跟单、业务员
        CompanyAndMerchandiserAndSalesmanInfoRequestVO companyAndMerchandiserAndSalesmanInfoRequestVO = new CompanyAndMerchandiserAndSalesmanInfoRequestVO();

        companyAndMerchandiserAndSalesmanInfoRequestVO.setPlatformCode(BaseConstant.PLATFORM_FB);

        companyAndMerchandiserAndSalesmanInfoRequestVO.setCompanyCodeList(Arrays.asList(orderPO.getCompanyCode()));

        List<CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyAndMerchandiserAndSalesmanInfoResponseVOS = UserApiUtil.companyAndMerchandiserAndSalesmanInfo(companyAndMerchandiserAndSalesmanInfoRequestVO);

        if (CollectionUtils.isNotEmpty(companyAndMerchandiserAndSalesmanInfoResponseVOS)) {
            CompanyAndMerchandiserAndSalesmanInfoResponseVO companyAndMerchandiserAndSalesmanInfoResponseVO = companyAndMerchandiserAndSalesmanInfoResponseVOS.get(CommonConstant.ZERO);

            MerchandiserInfo merchandiserInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getMerchandiserInfo();
            //设置跟单
            if (Objects.nonNull(merchandiserInfo)) {
                fbOrderDetailResponseVO.setMerchandiser(StringUtils.join(CommonConstant.EMPTY, merchandiserInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, merchandiserInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET));
            }

            MerchandiserInfo salesManInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getSalesManInfo();
            //设置业务员
            if (Objects.nonNull(salesManInfo)) {
                fbOrderDetailResponseVO.setSalesMan(StringUtils.join(CommonConstant.EMPTY, salesManInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, salesManInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET));
            }

        }

        return BusinessResponse.ok(fbOrderDetailResponseVO);
    }


    private Map<Integer, Long> getSendList(OrderPO orderPO) {
        try {
            LambdaQueryWrapper<OrderErpLogisticsPO> orderErpLogisticsPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderErpLogisticsPOLambdaQueryWrapper
                    .select(OrderErpLogisticsPO::getId)
                    .eq(OrderErpLogisticsPO::getOrderNumber, orderPO.getOrderNumber());
            List<OrderErpLogisticsPO> orderErpLogisticsPO = orderErpLogisticsDAO.selectList(orderErpLogisticsPOLambdaQueryWrapper);


            if (CollectionUtils.isNotEmpty(orderErpLogisticsPO)) {
                List<String> logisticsIdList = orderErpLogisticsPO.stream().map(OrderErpLogisticsPO::getId).collect(Collectors.toList());
                //查询发货明细
                LambdaQueryWrapper<OrderErpLogisticsListPO> orderErpLogisticsListPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orderErpLogisticsListPOLambdaQueryWrapper
                        .select(OrderErpLogisticsListPO::getQuantity, OrderErpLogisticsListPO::getOrderSortId)
                        .in(OrderErpLogisticsListPO::getParentId, logisticsIdList);
                List<OrderErpLogisticsListPO> orderErpLogisticsListPOS = orderErpLogisticsListDAO.selectList(orderErpLogisticsListPOLambdaQueryWrapper);

                Map<Integer, Long> integerLongMap = orderErpLogisticsListPOS.stream().collect(Collectors.toMap(OrderErpLogisticsListPO::getOrderSortId, OrderErpLogisticsListPO::getQuantity, Long::sum));
                return integerLongMap;
            }
        } catch (Exception e) {

        }

        return null;
    }

    /**
     * Fb订单单据统计金额
     *
     * @param fbOrderPageRequestVO
     * @return
     */
    @Override
    public BusinessResponse<FbOrderTotalPriceCountResponseVO> getFbOrderCount(FbOrderPageRequestVO fbOrderPageRequestVO) {
        fbOrderPageRequestVO.parseDate();

        FbOrderTotalPriceCountResponseVO fbOrderTotalPriceCountResponseVO = new FbOrderTotalPriceCountResponseVO();
        //数据权限
        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getEmpNo())) {
            //获取当前员工下的企业编码
            CompanyInfoListByJobNumberRequestVO companyInfoListByJobNumberRequestVO = new CompanyInfoListByJobNumberRequestVO();

            companyInfoListByJobNumberRequestVO.setJobNumber(fbOrderPageRequestVO.getEmpNo());

            companyInfoListByJobNumberRequestVO.setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE);

            List<String> companyCodeList = UserApiUtil.getCompanyCodeListByJobNumberMethod(companyInfoListByJobNumberRequestVO);

            if (CollUtil.isEmpty(companyCodeList)) {
                fbOrderTotalPriceCountResponseVO.setTotalPriceAll(BigDecimal.valueOf(BaseConstant.Zero));
                fbOrderTotalPriceCountResponseVO.setTotalPricePart(BigDecimal.valueOf(BaseConstant.Zero));
                return BusinessResponse.ok(fbOrderTotalPriceCountResponseVO);
            }
            fbOrderPageRequestVO.setCompanyCodeList(companyCodeList);
        }

        //查询全部订单总金额
        OrderPO orderPO = new OrderPO();

        QueryWrapper<OrderPO> wrapper = new QueryWrapper<>();

        wrapper.select("sum(total_price) as totalPriceAll");
        wrapper.eq("platform_code", BaseConstant.PLATFORM_FB)
                .eq("delete_status", BaseConstant.FALSE);

        if (CollectionUtils.isNotEmpty(fbOrderPageRequestVO.getCompanyCodeList())) {
            wrapper.in("company_code", fbOrderPageRequestVO.getCompanyCodeList());
        }

        OrderPO totalPriceAll = orderDAO.selectOne(wrapper);

        if (totalPriceAll == null) {
            orderPO.setTotalPriceAll(BigDecimal.valueOf(BaseConstant.Zero));
        } else {
            orderPO.setTotalPriceAll(totalPriceAll.getTotalPriceAll());
        }
        logger.info("get totalPriceAll result:{}", orderPO.getTotalPriceAll());


        //查询订单总金额
        String orderId = fbOrderPageRequestVO.getOrderId();

        String orderNumber = fbOrderPageRequestVO.getOrderNumber();

        List<String> orderStatus = fbOrderPageRequestVO.getOrderStatus();

        String companyName = fbOrderPageRequestVO.getCompanyName();

        List<String> idList = fbOrderPageRequestVO.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            idList = new ArrayList<>();
        }

        String startDate = fbOrderPageRequestVO.getStartDate();

        String endDate = fbOrderPageRequestVO.getEndDate();

        String orderSource = fbOrderPageRequestVO.getOrderSource();

        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getQuotationNumber())) {
            List<OrderListPO> orderListPOS = orderListDAO.selectList(new LambdaQueryWrapper<OrderListPO>()
                    .eq(OrderListPO::getQuotationNumber, fbOrderPageRequestVO.getQuotationNumber()));
            if (CollectionUtils.isNotEmpty(orderListPOS)) {
                List<String> quotationOrderIdList = orderListPOS.stream().map(OrderListPO::getOrderId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(idList)) {
                    idList.addAll(quotationOrderIdList);

                } else {
                    idList.retainAll(quotationOrderIdList);
                    if (CollectionUtils.isEmpty(idList)) {
                        fbOrderTotalPriceCountResponseVO.setTotalPriceAll(orderPO.getTotalPriceAll());
                        fbOrderTotalPriceCountResponseVO.setTotalPricePart(BigDecimal.valueOf(BaseConstant.Zero));
                        return BusinessResponse.ok(fbOrderTotalPriceCountResponseVO);
                    }
                }
            } else {
                fbOrderTotalPriceCountResponseVO.setTotalPriceAll(orderPO.getTotalPriceAll());
                fbOrderTotalPriceCountResponseVO.setTotalPricePart(BigDecimal.valueOf(BaseConstant.Zero));
                return BusinessResponse.ok(fbOrderTotalPriceCountResponseVO);
            }
        }


        // 指定查询的列
        QueryWrapper<OrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("sum(total_price) as totalPricePart");
        queryWrapper.eq("platform_code", BaseConstant.PLATFORM_FB);
        queryWrapper.eq("delete_status", BaseConstant.FALSE);

        if (StringUtils.isNotBlank(orderId)) {
            queryWrapper.eq("id", orderId);
        }

        if (CollectionUtils.isNotEmpty(idList)) {
            queryWrapper.in("id", idList);
        }

        if (StringUtils.isNotBlank(orderNumber)) {
            queryWrapper.like("order_number", orderNumber);
        }

        if (CollectionUtils.isNotEmpty(orderStatus)) {
            queryWrapper.in("order_status", orderStatus);
        }

        if (StringUtils.isNotBlank(companyName)) {
            queryWrapper.like("company_name", companyName);
        }

        if (StringUtils.isNotBlank(startDate)) {
            queryWrapper.ge("created_date", startDate);
        }

        if (StringUtils.isNotBlank(endDate)) {
            queryWrapper.le("created_date", endDate);
        }

        if (StringUtils.isNotBlank(orderSource)) {
            if (orderSource.equals(OrderSourceConstant.FB_BUY)) {
                queryWrapper.eq("order_source", orderSource);
            } else if (orderSource.equals(OrderSourceConstant.FB_QUOTATION)) {
                queryWrapper.and(p -> p.eq("order_source", OrderSourceConstant.MERGE_QUOTATION).or()
                        .eq("order_source", OrderSourceConstant.QUOTATION));
            }

        }

        if (CollectionUtils.isNotEmpty(fbOrderPageRequestVO.getCompanyCodeList())) {
            queryWrapper.in("company_code", fbOrderPageRequestVO.getCompanyCodeList());
        }

        OrderPO totalPricePart = orderDAO.selectOne(queryWrapper);

        if (ObjectUtils.isEmpty(totalPricePart)) {
            orderPO.setTotalPricePart(BigDecimal.valueOf(BaseConstant.Zero));
        } else {
            orderPO.setTotalPricePart(totalPricePart.getTotalPricePart());
        }
        logger.info("get totalPricePart result:{}", orderPO.getTotalPricePart());

        fbOrderTotalPriceCountResponseVO.setTotalPriceAll(orderPO.getTotalPriceAll());
        fbOrderTotalPriceCountResponseVO.setTotalPricePart(orderPO.getTotalPricePart());
        return BusinessResponse.ok(fbOrderTotalPriceCountResponseVO);
    }

    /**
     * Fb订单删除
     *
     * @param fbOrderDeleteRequestVO
     * @return
     */
    @Override
    public BusinessResponse<Object> deleteOrder(FbQuotationAndOrderDeleteRequestVO fbOrderDeleteRequestVO) {
        //批量删除
        orderDAO.update(null, new LambdaUpdateWrapper<OrderPO>()
                .in(OrderPO::getId, fbOrderDeleteRequestVO.getIdList())
                .set(OrderPO::getDeleteStatus, BaseConstant.TRUE));
        return BusinessResponse.ok(null);

    }

    /**
     * 订单列表导出数据
     *
     * @param fbOrderPageRequestVO
     * @return
     */
    @Override
    public BusinessResponse<List<FbOrderExportResponseVO>> getFbOrderExport(FbOrderPageRequestVO fbOrderPageRequestVO) {
        logger.info("access FbOrderApiImpl  getFbOrderListData ...");

        fbOrderPageRequestVO.parseDate();

        String orderId = fbOrderPageRequestVO.getOrderId();

        String orderNumber = fbOrderPageRequestVO.getOrderNumber();

        List<String> orderStatus = fbOrderPageRequestVO.getOrderStatus();

        String companyName = fbOrderPageRequestVO.getCompanyName();

        String startDate = fbOrderPageRequestVO.getStartDate();

        String endDate = fbOrderPageRequestVO.getEndDate();

        List<String> idList = fbOrderPageRequestVO.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            idList = new ArrayList<>();
        }

        String orderSource = fbOrderPageRequestVO.getOrderSource();

        //报价单号不为空时，设置查询条件
        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getQuotationNumber())) {
            List<OrderListPO> orderListPOS = orderListDAO.selectList(new LambdaQueryWrapper<OrderListPO>()
                    .eq(OrderListPO::getQuotationNumber, fbOrderPageRequestVO.getQuotationNumber()));
            if (CollectionUtils.isNotEmpty(orderListPOS)) {
                List<String> quotationOrderIdList = orderListPOS.stream().map(OrderListPO::getOrderId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(idList)) {
                    idList.addAll(quotationOrderIdList);
                } else {
                    idList.retainAll(quotationOrderIdList);
                    if (CollectionUtils.isEmpty(idList)) {
                        return BusinessResponse.ok(null);
                    }
                }
            } else {
                return BusinessResponse.ok(null);
            }
        }

        //设置查询字段
        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OrderPO::getId,
                        OrderPO::getOrderNumber,
                        OrderPO::getOrderStatus,
                        OrderPO::getDeleteStatus,
                        OrderPO::getShipping,
                        OrderPO::getCompanyCode,
                        OrderPO::getPaymentType,
                        OrderPO::getPayDate,
                        OrderPO::getReceivedTime,
                        OrderPO::getTradeNo,
                        OrderPO::getUserCode,
                        OrderPO::getRemark,
                        OrderPO::getCreatedDate,
                        OrderPO::getTotalPrice,
                        OrderPO::getOrderSource)
                .eq(OrderPO::getPlatformCode, BaseConstant.PLATFORM_FB)
                .eq(OrderPO::getDeleteStatus, BaseConstant.FALSE);


        if (CollectionUtils.isNotEmpty(idList)) {
            queryWrapper.in(OrderPO::getId, idList);
        }

        if (StringUtils.isNotBlank(orderId)) {
            queryWrapper.eq(OrderPO::getId, orderId);
        }

        if (StringUtils.isNotBlank(orderNumber)) {
            queryWrapper.like(OrderPO::getOrderNumber, orderNumber);
        }

        if (CollectionUtils.isNotEmpty(orderStatus)) {
            queryWrapper.in(OrderPO::getOrderStatus, orderStatus);
        }

        if (StringUtils.isNotBlank(companyName)) {
            queryWrapper.like(OrderPO::getCompanyName, companyName);
        }

        if (StringUtils.isNotBlank(startDate)) {
            queryWrapper.ge(OrderPO::getCreatedDate, startDate);
        }

        if (StringUtils.isNotBlank(endDate)) {
            queryWrapper.le(OrderPO::getCreatedDate, endDate);
        }

        if (StringUtils.isNotBlank(orderSource)) {
            if (orderSource.equals(OrderSourceConstant.FB_BUY)) {
                queryWrapper.eq(OrderPO::getOrderSource, orderSource);
            } else if (orderSource.equals(OrderSourceConstant.FB_QUOTATION)) {
                queryWrapper.and(p -> p.eq(OrderPO::getOrderSource, OrderSourceConstant.MERGE_QUOTATION).or()
                        .eq(OrderPO::getOrderSource, OrderSourceConstant.QUOTATION));
            }
        }

        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getOrderByField())) {
            queryWrapper.last(" order by " + StrUtil.toUnderlineCase(fbOrderPageRequestVO.getOrderByField()) + " " + fbOrderPageRequestVO.getOrderByType());
        } else {
            queryWrapper.orderByDesc(OrderPO::getCreatedDate);
        }

        //获取当前员工下的企业编码，是否有权限查看数据
        if (StringUtils.isNotBlank(fbOrderPageRequestVO.getEmpNo())) {

            CompanyInfoListByJobNumberRequestVO companyInfoListByJobNumberRequestVO = new CompanyInfoListByJobNumberRequestVO();

            companyInfoListByJobNumberRequestVO.setJobNumber(fbOrderPageRequestVO.getEmpNo());

            companyInfoListByJobNumberRequestVO.setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE);

            List<String> companyCodeList = UserApiUtil.getCompanyCodeListByJobNumberMethod(companyInfoListByJobNumberRequestVO);

            if (CollectionUtils.isNotEmpty(companyCodeList)) {
                queryWrapper.in(OrderPO::getCompanyCode, companyCodeList);
            } else {
                return BusinessResponse.ok(null);
            }
        }

        if (fbOrderPageRequestVO.getPageNum() != -1) {
            PageHelper.startPage(fbOrderPageRequestVO.getPageNum(), fbOrderPageRequestVO.getPageSize());
        }

        //获取订单列表
        List<OrderPO> orderPOList = orderDAO.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(orderPOList)) {
            return BusinessResponse.ok(null);
        }

        //获取订单id集合
        List<String> orderIds = orderPOList.stream().map(OrderPO::getId).collect(Collectors.toList());

        //根据订单id获取订单明细列表
        List<OrderListPO> orderListPOS = orderListDAO.selectList(new LambdaQueryWrapper<OrderListPO>()
                .in(OrderListPO::getOrderId, orderIds));

        List<String> companyCodeList = orderPOList.stream().map(OrderPO::getCompanyCode).distinct().collect(Collectors.toList());

        List<String> userCodeList = orderPOList.stream().map(OrderPO::getUserCode).distinct().collect(Collectors.toList());

        Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyInoMap = new HashMap<>(16);

        if (CollectionUtils.isNotEmpty(companyCodeList)) {
            //获取跟单信息
            CompanyAndMerchandiserAndSalesmanInfoRequestVO companyAndMerchandiserAndSalesmanInfoRequestVO = new CompanyAndMerchandiserAndSalesmanInfoRequestVO();

            companyAndMerchandiserAndSalesmanInfoRequestVO.setPlatformCode(BaseConstant.PLATFORM_FB);

            companyAndMerchandiserAndSalesmanInfoRequestVO.setCompanyCodeList(companyCodeList);

            List<CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyAndMerchandiserAndSalesmanInfoResponseVOS = UserApiUtil.companyAndMerchandiserAndSalesmanInfo(companyAndMerchandiserAndSalesmanInfoRequestVO);

            Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> merchandiserAndSalesmanInfoResponseVOMap =
                    companyAndMerchandiserAndSalesmanInfoResponseVOS.stream().collect(Collectors.toMap(CompanyAndMerchandiserAndSalesmanInfoResponseVO::getCompanyCode, v -> v, (k1, k2) -> k1));

            companyInoMap.putAll(merchandiserAndSalesmanInfoResponseVOMap);
        }

        //获取用户信息
        BaseUserInfoRequestVO baseUserInfoRequestVO = new BaseUserInfoRequestVO();
        baseUserInfoRequestVO.setUserCodeList(new HashSet<>(userCodeList));
        List<UserInfo> userInfoByUserCodesOrUserAccounts = UserApiUtil.getUserInfoByUserCodesOrUserAccounts(baseUserInfoRequestVO);

        Map<String, UserInfo> userInfoMap = userInfoByUserCodesOrUserAccounts.stream().collect(Collectors.toMap(UserInfo::getUserCode, user -> user, (k1, k2) -> k1));

        List<FbOrderListResponseVO> fbOrderListResponseVOList = orderPOList.stream().map(m -> {
            //设置下单来源
            if ((m.getOrderSource().equals(OrderSourceConstant.MERGE_QUOTATION)) || (m.getOrderSource().equals(OrderSourceConstant.QUOTATION))) {
                m.setOrderSource(OrderSourceConstant.FB_QUOTATION);
            }

            UserInfo userInfo = userInfoMap.get(m.getUserCode());

            //获取明细已发货数量
            Map<Integer, Long> sendMap = getSendList(m);

            FbOrderListResponseVO build = FbOrderListResponseVO
                    .builder()
                    .id(m.getId())
                    .orderNumber(m.getOrderNumber())
                    .orderStatus(m.getOrderStatus())
                    .deleteStatus(m.getDeleteStatus())
                    .shipping(m.getShipping())
                    .paymentType(m.getPaymentType())
                    .payDate(m.getPayDate() == null ? null : m.getPayDate().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                    .receivedTime(m.getReceivedTime() == null ? null : m.getReceivedTime().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                    .tradeNo(m.getTradeNo())
                    .createdUser(userInfo == null ? null : String.join(CommonConstant.EMPTY, userInfo.getUserName(), CommonConstant.LEFT_BRACKET, userInfo.getUserCode(), CommonConstant.RIGHT_BRACKET))
                    .userContactNumber(userInfo == null ? null : (StringUtils.isNotBlank(userInfo.getMobile()) ? userInfo.getMobile() : userInfo.getEmail()))
                    .remark(m.getRemark())
                    .createdDate(m.getCreatedDate() == null ? null : m.getCreatedDate().format(DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER)))
                    .totalPrice(m.getTotalPrice())
                    .orderSource(m.getOrderSource())
                    .build();

            List<FbOrderDetailListResponseVO> fbOrderDetailListList;
            if (CollectionUtils.isNotEmpty(orderListPOS)) {
                //组装订单明细项返参
                fbOrderDetailListList = orderListPOS.stream()
                        .filter(p -> m.getId().equals(p.getOrderId())).map(p -> {
                            FbOrderDetailListResponseVO fbOrderDetailList = new FbOrderDetailListResponseVO();
                            fbOrderDetailList.setOrderDetailStatus(p.getOrderDetailStatus());
                            fbOrderDetailList.setDetailId(p.getId());
                            fbOrderDetailList.setSortId(p.getSortId());
                            fbOrderDetailList.setQuantity(p.getQuantity());
                            fbOrderDetailList.setRemark(p.getRemark());
                            fbOrderDetailList.setExamineRemark(p.getExamineRemark());
                            fbOrderDetailList.setDiscountPrice(p.getDiscountPrice());
                            fbOrderDetailList.setTotalPrice(p.getTotalPrice());
                            fbOrderDetailList.setTaxDiscountPrice(p.getTaxDiscountPrice());
                            fbOrderDetailList.setReplyToDelivery(p.getReplyToDelivery() == null ? null : p.getReplyToDelivery().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                            fbOrderDetailList.setEstimatedShippingDate(p.getEstimatedShippingDate() == null ? null : p.getEstimatedShippingDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                            fbOrderDetailList.setQuotationNumber(p.getQuotationNumber());
                            if (MapUtils.isNotEmpty(sendMap)) {
                                Long aLong = sendMap.get(p.getSortId());
                                fbOrderDetailList.setSendQuantity(aLong);
                            } else {
                                fbOrderDetailList.setSendQuantity(0L);
                            }
                            return fbOrderDetailList;
                        }).collect(Collectors.toList());
            } else {
                fbOrderDetailListList = Collections.emptyList();
            }
            //设置订单明细
            build.setDetailList(fbOrderDetailListList);

            String companyCode = m.getCompanyCode();

            CompanyAndMerchandiserAndSalesmanInfoResponseVO companyAndMerchandiserAndSalesmanInfoResponseVO = companyInoMap.get(companyCode);

            //设置业务员、专属跟单
            if (Objects.nonNull(companyAndMerchandiserAndSalesmanInfoResponseVO)) {
                MerchandiserInfo merchandiserInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getMerchandiserInfo();
                if (Objects.nonNull(merchandiserInfo)) {
                    String join = String.join(CommonConstant.EMPTY, merchandiserInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, merchandiserInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET);
                    build.setMerchandiser(join);
                }

                MerchandiserInfo salesManInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getSalesManInfo();
                if (Objects.nonNull(salesManInfo)) {
                    String join = String.join(CommonConstant.EMPTY, salesManInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, salesManInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET);
                    build.setSalesMan(join);
                }
            }
            return build;
        }).collect(Collectors.toList());

        //订单订单明细集为
        List<FbOrderExportResponseVO> fbOrderExportResponseVOS = fbOrderListResponseVOList.stream().flatMap(m -> {
            FbOrderExportResponseVO fbOrderExportResponseVO = new FbOrderExportResponseVO();
            BeanUtils.copyProperties(m, fbOrderExportResponseVO);
            List<FbOrderDetailListResponseVO> detailList = m.getDetailList();
            if (CollectionUtils.isNotEmpty(detailList)) {
                return detailList.stream().map(p -> {
                    FbOrderExportResponseVO fbOrderDetail = new FbOrderExportResponseVO();
                    BeanUtils.copyProperties(fbOrderExportResponseVO, fbOrderDetail);
                    BeanUtils.copyProperties(p, fbOrderDetail);
                    return fbOrderDetail;
                });
            } else {
                return Stream.of(fbOrderExportResponseVO);
            }
        }).collect(Collectors.toList());

        //设置序号
        int sort = 0;
        for (int i = 0; i < fbOrderExportResponseVOS.size(); i++) {
            sort++;
            fbOrderExportResponseVOS.get(i).setSort(sort);
        }
        return BusinessResponse.ok(fbOrderExportResponseVOS);
    }
}
