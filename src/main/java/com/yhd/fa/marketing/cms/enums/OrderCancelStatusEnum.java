package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderCancelStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderCancelStatusEnum.java, v0.1 2023/2/22 14:45 yehuasheng Exp $
 */
@Getter
public enum OrderCancelStatusEnum {
    CANCELING(OrderCancelStatusConstant.CANCELING, "取消中"),
    CANCEL(OrderCancelStatusConstant.CANCEL, "已取消"),
    TURN_DOWN(OrderCancelStatusConstant.TURN_DOWN, "驳回"),
    PROCESSED(OrderCancelStatusConstant.PROCESSED, "已处理"),
    REFUNDED(OrderCancelStatusConstant.REFUNDED, "已退款"),
    ;

    private final String orderCancelStatus;
    private final String orderCancelStatusName;

    OrderCancelStatusEnum(String orderCancelStatus, String orderCancelStatusName) {
        this.orderCancelStatus = orderCancelStatus;
        this.orderCancelStatusName = orderCancelStatusName;
    }
}
