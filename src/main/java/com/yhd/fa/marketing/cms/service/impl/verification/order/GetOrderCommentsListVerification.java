package com.yhd.fa.marketing.cms.service.impl.verification.order;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRequestVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: GetOrderCommentsListVerification.java, v0.1 2023/2/24 18:57 yehuasheng Exp $
 */
@Component
public class GetOrderCommentsListVerification {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(GetOrderCommentsListVerification.class.getName());

    /**
     * 检查订单评论参数
     *
     * @param orderCommentsRequestVO 订单评论参数
     * @param <T>                    T
     * @return BusinessResponse
     */
    public <T> BusinessResponse<T> check(OrderCommentsRequestVO orderCommentsRequestVO) {
        logger.info("start check order comments parameter.");

        // 校验时间是否符合
        boolean checkDateTime = ObjectUtil.isNotNull(orderCommentsRequestVO.getEndDateTime()) && ObjectUtil.isNotNull(orderCommentsRequestVO.getStartDateTime()) && orderCommentsRequestVO.getStartDateTime().isAfter(orderCommentsRequestVO.getEndDateTime());
        if (checkDateTime) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.START_DATE_IS_AFTER_END_TIME_ERROR);
        }

        return BusinessResponse.ok(null);
    }

}
