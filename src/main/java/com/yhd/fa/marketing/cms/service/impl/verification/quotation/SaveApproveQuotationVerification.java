package com.yhd.fa.marketing.cms.service.impl.verification.quotation;

import cn.hutool.core.collection.CollUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.ApproveQuotationResultTypeConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.SaveApproveQuotationRequestVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: SaveApproveQuotationVerification.java, v0.1 2022/12/29 16:38 yehuasheng Exp $
 */
@Component
public class SaveApproveQuotationVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SaveApproveQuotationVerification.class.getName());

    /**
     * 报价单mapper
     */
    @Resource
    private GetApproveQuotationDetailVerification getApproveQuotationDetailVerification;

    /**
     * 检查审核报价单保存的参数
     *
     * @param saveApproveQuotationRequestVO 保存待审核报价单参数
     * @return BusinessResponse
     */
    public BusinessResponse<QuotationPO> check(SaveApproveQuotationRequestVO saveApproveQuotationRequestVO) {
        logger.info("start check save approve quotation verification.");

        if (StringUtils.equals(saveApproveQuotationRequestVO.getResultType(), ApproveQuotationResultTypeConstant.REFUSE)) {
            // 结果是拒绝的
            if (StringUtils.isBlank(saveApproveQuotationRequestVO.getReasonsRefusal())) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.REASONS_REFUSAL_IS_EMPTY);
            }
        } else {
            // 结果是通过的
            if (saveApproveQuotationRequestVO.isChangeToQuotation() && CollUtil.isEmpty(saveApproveQuotationRequestVO.getQuotationDetailList())) {
                // 判断参数是否为空
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.APPROVE_QUOTATION_DETAIL_IS_EMPTY);
            }
        }

        return getApproveQuotationDetailVerification.check(saveApproveQuotationRequestVO.getQuotationId());
    }
}
