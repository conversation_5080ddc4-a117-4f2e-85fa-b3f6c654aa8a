package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderAfterSaleStatusConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleStatusEnum.java, v0.1 2023/2/24 14:40 yehuasheng Exp $
 */
@Getter
@AllArgsConstructor
public enum OrderAfterSaleStatusEnum {
    PROCESSING(OrderAfterSaleStatusConstant.PROCESSING, "处理中"),
    PROCESSED(OrderAfterSaleStatusConstant.PROCESSED, "已处理"),

    PENDING_AUDIT(OrderAfterSaleStatusConstant.PENDING_AUDIT, "待审核"),
    PENDING_PLAN(OrderAfterSaleStatusConstant.PENDING_PLAN, "待售后方案"),
    PENDING_RETURN(OrderAfterSaleStatusConstant.PENDING_RETURN, "待寄回商品"),
    PENDING_CHECK(OrderAfterSaleStatusConstant.PENDING_CHECK, "寄回商品待检测"),
    PENDING_REFUND(OrderAfterSaleStatusConstant.PENDING_REFUND, "待退款"),
    PENDING_SHIP(OrderAfterSaleStatusConstant.PENDING_SHIP, "待发货"),
    PENDING_RECEIVE(OrderAfterSaleStatusConstant.PENDING_RECEIVE, "待收货"),
    PENDING_REPAIR(OrderAfterSaleStatusConstant.PENDING_REPAIR, "待维修"),
    COMPLETED(OrderAfterSaleStatusConstant.COMPLETED, "已完成"),
    CLOSED(OrderAfterSaleStatusConstant.CLOSED, "已关闭"),
    CANCEL(OrderAfterSaleStatusConstant.CANCEL, "已取消"),
    ;

    private final String orderAfterSaleStatus;
    private final String orderAfterSaleStatusName;
}
