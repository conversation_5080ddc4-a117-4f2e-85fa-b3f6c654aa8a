package com.yhd.fa.marketing.cms.dao;

import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.FbQuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationPageRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbQuotationListResponseVO;

import java.util.List;

/**
 * <AUTHOR> 07558-fenggang
 * @since 2024-06-18 16:15:19
 */
public interface FbQuotationDAO extends MPJBaseMapper<FbQuotationPO> {



    List<FbQuotationListResponseVO> selectFbQuotationPage(FbQuotationPageRequestVO fbQuotationPageRequestVO);



    /**
     * 部分报价单的总金额
     * @param fbQuotationPageRequestVO
     */
    FbQuotationPO selectTotalPricePart(FbQuotationPageRequestVO fbQuotationPageRequestVO);
}

