package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: SendMessageToDingDingRequestVO.java, v0.1 2023/3/20 15:48 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendMessageToDingDingRequestVO extends BaseVO {
    /**
     * 模板id
     */
    private String templateId;

    /**
     * 数据
     */
    private transient Map<String, Object> map = new HashMap<>();

    /**
     * 员工工号
     */
    private List<String> receivers;
}
