package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: CouponOrderFinishCountResponseVO.java, v 0.1 2024/8/8 9:50 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CouponOrderFinishCountResponseVO extends BaseVO {

    /**
     * 完成订单数量
     */
    @Schema(description = "完成订单数量", example = "1")
    @Builder.Default
    private Long finishCount = 0L;
}
