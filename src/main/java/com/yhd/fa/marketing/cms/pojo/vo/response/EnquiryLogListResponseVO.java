package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogListResponseVO.java, v0.1 2022/12/8 10:46 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EnquiryLogListResponseVO extends BaseVO {
    /**
     * id
     */
    @Schema(description = "记录id", example = "18a1f4a54c18468a9e6ad120afbfe48f")
    private String id;

    /**
     * 型号
     */
    @Schema(description = "型号", example = "SAD01-D3-L100")
    private String model;

    /**
     * 代码
     */
    @Schema(description = "代码", example = "SAD01")
    private String code;

    /**
     * 数量
     */
    @Schema(description = "数量", example = "1")
    private long quantity;

    /**
     * 原价
     */
    @Schema(description = "ERP原价", example = "0.0")
    private BigDecimal price;

    /**
     * 折扣单价
     */
    @Schema(description = "未税折扣单价", example = "0.0")
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价", example = "0.0")
    private BigDecimal taxDiscountPrice;

    /**
     * 总价小计
     */
    @Schema(description = "总价小计", example = "0.0")
    private BigDecimal totalPrice;

    /**
     * 交期
     */
    @Schema(description = "交期", example = "1")
    private Integer delivery;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称", example = "你猜")
    private String userName;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "用户编码")
    private String userCode;

    /**
     * 用户手机号码
     */
    @Schema(description = "用户手机号码", example = "13111111111")
    private String userPhone;

    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱地址", example = "<EMAIL>")
    private String userEmail;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "你猜")
    private String companyName;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "A001")
    private String companyCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "导向轴")
    private String productName;

    /**
     * 一级分类代码
     */
    @Schema(description = "一级分类代码", example = "A")
    private String typeCode;

    /**
     * 二级分类
     */
    @Schema(description = "二级分类")
    private String catCode;

    /**
     * 系列代码
     */
    @Schema(description = "系列代码",
            example = "A01-01")
    private String goodsCode;

    /**
     * 跟单名称
     */
    @Schema(description = "跟单员名称", example = "林杏金")
    private String merchandiser;

    /**
     * 业务员
     */
    @Schema(description = "业务员", example = "林杏金")
    private String salesman;

    /**
     * 是否标准
     */
    @Schema(description = "是否标准", example = "true")
    private String isStandard;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息", example = "超出数量")
    private String errorMsg;

    /**
     * 客户等级 资料客户1, 潜在客户2,意向客户3,成交客户4,长期客户5,FANS客户6,沉睡客户7,沉睡可转移8
     */
    @Schema(description = "客户等级 资料客户1, 潜在客户2,意向客户3,成交客户4,长期客户5,FANS客户6,沉睡客户7,沉睡可转移8", example = "潜在客户")
    private String customerGrade;

    /**
     * 用户类型 0 Personal 个人用户,  1 Enterprise 普通企业用户, 2 PendingEnterprise 待认证企业用户,  3 VerifiedEnterprise 认证企业用户
     */
    @Schema(description = "用户类型 Personal 个人用户, Enterprise 普通企业用户, PendingEnterprise 待认证企业用户, VerifiedEnterprise 认证企业用户", example = "VerifiedEnterprise")
    private String userType;

    /**
     * IP地址
     */
    @Schema(description = "IP地址", example = "127.0.0.1")
    private String ip;

    /**
     * 用户职位
     */
    @Schema(description = "用户职位", example = "总经理")
    private String occupation;

    /**
     * 区域
     */
    @Schema(description = "区域", example = "华南")
    private String IpRegion;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-12-08 16:43:20")
    private LocalDateTime createdDate;

    @Schema(description = "注册时间", example = "2022-12-08 16:43:20")
    private LocalDateTime registerDate;
}
