package com.yhd.fa.marketing.cms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: CancelStatusEnum.java, v 0.1 2022/11/23 17:11 JiangYuHong Exp $
 */
@AllArgsConstructor
@Getter
public enum CancelStatusEnum {


    CANCELING("canceling", "取消中"),

    PROCESSED("processed", "已处理"),

    TURN_DOWN("turnDown", "已驳回"),

    REFUNDED("refunded", "已退款"),
    ;

    private final String code;
    private final String desc;

}
