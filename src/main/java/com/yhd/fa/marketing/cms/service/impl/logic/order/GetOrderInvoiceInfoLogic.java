package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.*;
import com.yhd.fa.marketing.cms.mapper.OrderInvoiceListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderInvoiceMapper;
import com.yhd.fa.marketing.cms.pojo.dto.InvoiceFileInfoDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderInvoiceListPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderInvoicePO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceDetailListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceDetailResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderInvoiceInfoLogic.java, v0.1 2023/2/23 15:56 yehuasheng Exp $
 */
@Component
public class GetOrderInvoiceInfoLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderInvoiceInfoLogic.class.getName());

    /**
     * 订单发票mapper
     */
    @Resource
    private OrderInvoiceMapper orderInvoiceMapper;

    /**
     * 订单发票明细mapper
     */
    @Resource
    private OrderInvoiceListMapper orderInvoiceListMapper;

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 获取订单发票详情逻辑
     *
     * @param orderInvoiceId 发票订单id
     * @return BusinessResponse<OrderInvoiceDetailResponseVO>
     */
    public BusinessResponse<OrderInvoiceDetailResponseVO> exec(String orderInvoiceId) {
        logger.info("exec get order invoice detail info.");

        // 获取订单发票详情
        OrderInvoiceDetailResponseVO orderInvoiceDetailResponseVO = getOrderInvoiceInfo(orderInvoiceId);

        // 判断订单发票是否存在
        if (ObjectUtil.isNull(orderInvoiceDetailResponseVO)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_INVOICE_IS_NOT_EXISTS);
        }

        // 设置订单发票其他值
        setOrderInvoiceOtherValue(orderInvoiceDetailResponseVO);

        return BusinessResponse.ok(orderInvoiceDetailResponseVO);
    }

    /**
     * 查询订单发票详情
     *
     * @param orderInvoiceId 订单发票id
     * @return OrderInvoiceDetailResponseVO
     */
    private OrderInvoiceDetailResponseVO getOrderInvoiceInfo(String orderInvoiceId) {
        logger.info("get order invoice detail info.");

        return orderInvoiceMapper.selectJoinOne(OrderInvoiceDetailResponseVO.class, setQueryWrapper(orderInvoiceId));
    }

    /**
     * 设置查询条件和字段
     *
     * @param orderInvoiceId 订单发票id
     * @return MPJLambdaWrapper<OrderInvoicePO>
     */
    private MPJLambdaWrapper<OrderInvoicePO> setQueryWrapper(String orderInvoiceId) {
        logger.info("set get order invoice detail.");

        MPJLambdaWrapper<OrderInvoicePO> queryWrapper = OrderUtil.setOrderInvoiceQueryWrapperSelectAs()
                .selectAs(OrderInvoicePO::getDeliveryStatus, OrderInvoiceDetailResponseVO::getDeliveryStatus)
                .selectAs(OrderInvoicePO::getUserAddressId, OrderInvoiceDetailResponseVO::getUserAddressId)
                .selectAs(OrderInvoicePO::getUserAddress, OrderInvoiceDetailResponseVO::getUserAddress)
                .selectAs(OrderInvoicePO::getConsignee, OrderInvoiceDetailResponseVO::getConsignee)
                .selectAs(OrderInvoicePO::getLinkPhone, OrderInvoiceDetailResponseVO::getLinkPhone)
                .selectAs(OrderInvoicePO::getEmail, OrderInvoiceDetailResponseVO::getEmail)
                .selectAs(OrderInvoicePO::getInvTaxRegNumber, OrderInvoiceDetailResponseVO::getInvTaxRegNumber)
                .selectAs(OrderInvoicePO::getInvAddress, OrderInvoiceDetailResponseVO::getInvAddress)
                .selectAs(OrderInvoicePO::getInvLinkPhone, OrderInvoiceDetailResponseVO::getInvLinkPhone)
                .selectAs(OrderInvoicePO::getInvManuBank, OrderInvoiceDetailResponseVO::getInvManuBank)
                .selectAs(OrderInvoicePO::getInvAccount, OrderInvoiceDetailResponseVO::getInvAccount)
                .selectAs(OrderInvoicePO::getLogisticsCode, OrderInvoiceDetailResponseVO::getLogisticsCode)
                .selectAs(OrderInvoicePO::getLogisticsName, OrderInvoiceDetailResponseVO::getLogisticsName)
                .selectAs(OrderInvoicePO::getRemarks, OrderInvoiceDetailResponseVO::getRemarks)
                .selectAs(OrderInvoicePO::getSendErpTime, OrderInvoiceDetailResponseVO::getSynchronizationDate)
                .selectAs(OrderInvoicePO::getInvoiceFile, OrderInvoiceDetailResponseVO::getInvoiceFileInfoStr)
                .eq(OrderInvoicePO::getId, orderInvoiceId)
                .last(FaDocMarketingCmsConstant.LIMIT);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderInvoicePO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        return queryWrapper;
    }

    /**
     * 设置订单发票其他值
     *
     * @param orderInvoiceInfo 订单发票详情
     */
    private void setOrderInvoiceOtherValue(OrderInvoiceDetailResponseVO orderInvoiceInfo) {
        logger.info("set order invoice other value.");

        // 获取用户
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(orderInvoiceInfo.getUserCode(), orderInvoiceInfo.getCompanyCode());
        Optional.ofNullable(userInfo).ifPresent(userAndMerchandiserAndResourcesResponseVO -> {
            // 设置用户名
            if (null != userAndMerchandiserAndResourcesResponseVO.getUserInfo()) {
                orderInvoiceInfo.setUserName(userAndMerchandiserAndResourcesResponseVO.getUserInfo().getUserName());
            }

            // 设置跟单
            Optional.ofNullable(userInfo.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserInfo -> {
                orderInvoiceInfo.setMerchandiser(merchandiserInfo.getEmployeeCode() + CommonConstant.SLASH + merchandiserInfo.getEmployeeName());
                orderInvoiceInfo.setMerchandiserContact(merchandiserInfo.getMobile());
            });

            // 设置业务员
            Optional.ofNullable(userInfo.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> {
                orderInvoiceInfo.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
                orderInvoiceInfo.setSalesmanContact(salesMan.getMobile());
            });
        });

        // 设置发票状态
        Map<String, String> orderInvoiceStatusMap = Arrays.stream(OrderInvoiceStatusEnum.values()).collect(Collectors.toMap(OrderInvoiceStatusEnum::getOrderInvoiceStatus, OrderInvoiceStatusEnum::getOrderInvoiceStatusName));
        orderInvoiceInfo.setOrderInvoiceStatusName(orderInvoiceStatusMap.get(orderInvoiceInfo.getOrderInvoiceStatus()));

        // 设置发票类型
        Map<String, String> orderInvoiceTypeMap = Arrays.stream(OrderInvoiceTypeEnum.values()).collect(Collectors.toMap(OrderInvoiceTypeEnum::getOrderInvoiceType, OrderInvoiceTypeEnum::getOrderInvoiceTypeName));
        orderInvoiceInfo.setOrderInvoiceTypeName(orderInvoiceTypeMap.get(orderInvoiceInfo.getOrderInvoiceType()));

        // 设置发票性质
        Map<String, String> orderInvoiceNatureMap = Arrays.stream(OrderInvoiceNatureEnum.values()).collect(Collectors.toMap(OrderInvoiceNatureEnum::getOrderInvoiceNature, OrderInvoiceNatureEnum::getOrderInvoiceNatureName));
        orderInvoiceInfo.setOrderInvoiceNatureName(orderInvoiceNatureMap.get(orderInvoiceInfo.getOrderInvoiceNature()));

        // 设置发货状态
        Map<String, String> orderInvoiceDeliveryStatusMap = Arrays.stream(OrderInvoiceDeliveryStatusEnum.values()).collect(Collectors.toMap(OrderInvoiceDeliveryStatusEnum::getOrderInvoiceDeliveryStatus, OrderInvoiceDeliveryStatusEnum::getOrderInvoiceDeliveryStatusName));
        orderInvoiceInfo.setDeliveryStatusName(orderInvoiceDeliveryStatusMap.get(orderInvoiceInfo.getDeliveryStatus()));

        //设置发票文件信息
        if (StringUtils.isNotBlank(orderInvoiceInfo.getInvoiceFileInfoStr())){
            orderInvoiceInfo.setInvoiceFileInfo(JSONUtil.toList(orderInvoiceInfo.getInvoiceFileInfoStr(), InvoiceFileInfoDTO.class));
            orderInvoiceInfo.setInvoiceFileInfoStr(null);
        }

        // 设置订单发票详情
        orderInvoiceInfo.setOrderInvoiceDetails(getOrderInvoiceDetails(orderInvoiceInfo.getOrderInvoiceId()));
    }

    /**
     * 获取订单发票详情
     *
     * @param orderInvoiceId 订单发票id
     * @return List<OrderInvoiceDetailListResponseVO>
     */
    private List<OrderInvoiceDetailListResponseVO> getOrderInvoiceDetails(String orderInvoiceId) {
        logger.info("get order invoice details.");

        return orderInvoiceListMapper.selectJoinList(OrderInvoiceDetailListResponseVO.class,
                new MPJLambdaWrapper<OrderInvoiceListPO>()
                        .selectAs(OrderInvoiceListPO::getId, OrderInvoiceDetailListResponseVO::getOrderInvoiceDetailId)
                        .selectAs(OrderInvoiceListPO::getOrderId, OrderInvoiceDetailListResponseVO::getOrderId)
                        .selectAs(OrderInvoiceListPO::getOrderNumber, OrderInvoiceDetailListResponseVO::getOrderNumber)
                        .selectAs(OrderInvoiceListPO::getPayablePrice, OrderInvoiceDetailListResponseVO::getPayablePrice)
                        .selectAs(OrderInvoiceListPO::getOrderCreatedDate, OrderInvoiceDetailListResponseVO::getOrderCreatedDate)
                        .eq(OrderInvoiceListPO::getOrderInvoiceId, orderInvoiceId)
        );
    }
}
