package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderAfterSaleTypeConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleTypeEnum.java, v0.1 2023/2/24 14:42 yehuasheng Exp $
 */
@Getter
@AllArgsConstructor
public enum OrderAfterSaleTypeEnum {
    EXCHANGE(OrderAfterSaleTypeConstant.EXCHANGE, "补换货"),
    REFUND(OrderAfterSaleTypeConstant.REFUND, "退货/退款"),
    REPAIR(OrderAfterSaleTypeConstant.REPAIR, "维修"),
    ;

    private final String orderAfterSaleType;
    private final String orderAfterSaleTypeName;

}
