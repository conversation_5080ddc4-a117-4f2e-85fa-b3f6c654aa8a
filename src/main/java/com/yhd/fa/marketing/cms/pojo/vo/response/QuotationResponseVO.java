package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.common.util.CommonConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: QuotationVO.java, v 0.1 2020/6/12 11:25 wanggengfa Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuotationResponseVO extends BaseVO {
    /**
     * 完整型号
     */
    @Schema(description = "型号", example = "SAD01-D3-L100", required = true)
    private String model;

    /**
     * 数量
     */
    @Schema(description = "数量", example = "1", required = true)
    private long quantity;

    /**
     * 序号
     */
    @Schema(description = "排序", example = "1-0", required = true)
    private String sort;

    /**
     * 原价
     */
    @Schema(description = "原单价", example = "10.00")
    private BigDecimal price;

    /**
     * 折扣后未税单价
     */
    @Schema(description = "未税折扣单价", example = "10.0000")
    private BigDecimal priceWithoutTax;

    /**
     * 折扣后含税单价
     */
    @Schema(description = "含税折扣单价 x1.13", example = "11.3000")
    private BigDecimal priceWithTax;

    /**
     * 总金额
     */
    @Schema(description = "含税总价", example = "11.30")
    private BigDecimal total;

    /**
     * 历史最低折扣后未税单价
     */
    @Schema(description = "历史最低价", example = "10.00")
    private BigDecimal historyLowestPrice;

    /**
     * 基准价日期
     */
    @Schema(description = "基准价日期", example = "2022-01-01 00:00:00")
    private LocalDateTime fixDate;

    /**
     * 长期固定价
     */
    @Schema(description = "长期固定价", example = "10.00")
    private BigDecimal longTermFixedPrice;

    /**
     * 数量折扣
     */
    @Schema(description = "数量折扣", example = "1.0")
    private BigDecimal amountDiscountRate;

    /**
     * 等级折扣
     */
    @Schema(description = "等级折扣", example = "1.0")
    private BigDecimal levelDiscountRate;

    /**
     * 代码等级折扣
     */
    @Schema(description = "代码等级折扣", example = "1.0")
    private BigDecimal codeLevelDiscountRate;

    /**
     * 微调率
     */
    @Schema(description = "微调折扣", example = "1.0")
    private BigDecimal fineTuningsRate;

    /**
     * 总折扣
     */
    @Schema(description = "总折扣", example = "1.0")
    private BigDecimal totalDiscount;

    /**
     * 内部折扣
     */
    @Schema(description = "内部折扣", example = "1.0")
    private BigDecimal internalDiscountRate;

    /**
     * 虚拟折扣
     */
    @Schema(description = "虚拟折扣", example = "1.0")
    private BigDecimal virtualDiscountRate;

    /**
     * 是否标准型
     */
    @Schema(description = "是否标准型 true是 false不是", example = "true", allowableValues = {CommonConstant.TRUE, CommonConstant.FALSE}, required = true)
    private boolean standard;

    /**
     * 是否有交期
     */
    @Schema(description = "是否有交期 true有 false没有", example = "true", allowableValues = {CommonConstant.TRUE, CommonConstant.FALSE})
    private boolean haveDelivery;

    /**
     * 交货日期
     */
    @Schema(description = "交期天数", example = "1")
    private Integer delivery;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息", example = "型号错误")
    private String msg;

    /**
     * 错误类型
     */
    @Schema(description = "错误类型 outnumbering超出数量，modelError型号错误，modelCodeError型号代码错误，noUnitPrice没有单价，noPrice没有价格，noDelivery没有交期，formulaError公式错误，goodsOffShelf商品下架，goodsHalfSales商品停售", example = "modelError")
    private String errorType;

    /**
     * 最大数量
     */
    @Schema(description = "最大数量", example = "100")
    private Long maxQuantity;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码", example = "123456")
    private String materialCode;

    /**
     * 产品代码
     */
    @Schema(description = "产品代码", example = "SAD01")
    private String code;

    /**
     * 单位
     */
    @Schema(description = "单位", example = "PCS")
    private String unit;

    /**
     * 型号选型值id
     */
    @Schema(description = "型号选型值id", example = "1")
    private Integer modelValueId;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "导向轴")
    private String productName;

    /**
     * 铝型材长度
     */
    @Schema(description = "铝型材长度", example = "2000")
    private BigDecimal aluminumProfileValueOfFieldLong;

    /**
     * 铝型材技术要求
     */
    @Schema(description = "铝型材技术要求", example = "很长一段字")
    private String technicalSpecifications;

    /**
     * 三级分类
     */
    @Schema(description = "类别编码", example = "A01.01.01")
    private String typeCode;

    /**
     * 供应商价格1，存在NULL
     */
    @Schema(description = "供应商价格1")
    private BigDecimal g1Price;

    /**
     * 供应商价格2，存在NULL
     */
    @Schema(description = "供应商价格2")
    private BigDecimal g2Price;

    /**
     * 供应商价格3，存在NULL
     */
    @Schema(description = "供应商价格3")
    private BigDecimal g3Price;

    /**
     * sa_baojiatypelist5取原价对应autoid
     */
    @Schema(description = "sa_baojiatypelist5取原价对应autoid", example = "1")
    private int sourcePriceAutoId;

    /**
     * 报价状态
     */
    @Schema(description = "报价状态", example = "2")
    private int quotedPriceStatus;

    /**
     * 是否有调整原价 0没有 1有
     */
    @Schema(description = "是否有调整原价 0没有 1有", example = "0")
    private byte isAdjustOriginalCost;

    /**
     * 调整后的原单价
     */
    @Schema(description = "调整后的原单价", example = "10.00")
    private BigDecimal oldPrice;

    /**
     * 调整后的微调率
     */
    @Schema(description = "调整后的微调率", example = "1.0")
    private BigDecimal oldFineTuningsRate;

    /**
     * 调整后的虚拟折扣
     */
    @Schema(description = "调整后的虚拟折扣", example = "1.0")
    private BigDecimal oldVirtualDiscountRate;

    /**
     * 调整后的内部折扣
     */
    @Schema(description = "调整后的内部折扣", example = "1.0")
    private BigDecimal oldInternalDiscountRate;

    /**
     * 调整后的总折扣率
     */
    @Schema(description = "调整后的总折扣率", example = "1.0")
    private BigDecimal oldTotalDiscount;

    /**
     * 是否含税折扣单价保存2位数、未税折扣单价保存4位  1是   null、0、2不是
     */
    @Schema(description = "是否含税折扣单价保存2位数、未税折扣单价保存4位  1是   null、0、2不是", example = "0")
    private byte isDoubleDigit;

    /**
     * 旧未税折扣单价
     */
    @Schema(description = "旧未税折扣单价", example = "10.00")
    private BigDecimal oldPriceWithoutTax;

    /**
     * 旧含税折扣单价
     */
    @Schema(description = "旧含税折扣单价", example = "10.00")
    private BigDecimal oldPriceWithTax;

    /**
     * 旧的含税总价
     */
    @Schema(description = "旧含税折扣单价", example = "11.30")
    private BigDecimal oldTotal;

    /**
     * 前端使用的序号
     */
    @Schema(description = "前端使用的序号 用户前端展示", example = "1")
    private Integer frontEndSort;
}
