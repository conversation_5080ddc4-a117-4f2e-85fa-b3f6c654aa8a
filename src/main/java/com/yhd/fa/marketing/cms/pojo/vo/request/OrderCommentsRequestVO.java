package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsRequestVO.java, v0.1 2023/2/24 18:34 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderCommentsRequestVO extends PageRequestVO {
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-01-01 10:10:10")
    private LocalDateTime startDateTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-01-01 10:10:10")
    private LocalDateTime endDateTime;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "YI2022111615065597799D0M")
    private String orderNumber;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 是否已评论 true 已评论 false 未评论
     */
    @Schema(description = "是否已评论 true 已评论 false 未评论", example = "true")
    private String replied;

    /**
     * 产品描述相符度
     */
    @Schema(description = "产品描述相符度", example = "1")
    private List<Integer> productDescRating;

    /**
     * 人员服务态度
     */
    @Schema(description = "人员服务态度", example = "1")
    private List<Integer> personnelServiceRating;

    /**
     * 产品交付时效
     */
    @Schema(description = "产品交付时效", example = "1")
    private List<Integer> productDeliveryRating;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "已生成工单")
    private String remark;

    /**
     * 问题分类id
     */
    @Schema(description = "问题分类id列表", example = "[1,2]")
    private List<Integer> problemCategoriesId;
}
