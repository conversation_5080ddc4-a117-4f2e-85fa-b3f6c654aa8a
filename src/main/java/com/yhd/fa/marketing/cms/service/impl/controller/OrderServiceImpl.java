package com.yhd.fa.marketing.cms.service.impl.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.dao.InvoiceDAO;
import com.yhd.fa.marketing.cms.dao.OrderDAO;
import com.yhd.fa.marketing.cms.pojo.po.InvoicePO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCollectionLogPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.CheckUserOrderResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.LastOrderDateResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInfoResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderListResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderService;
import com.yhd.fa.marketing.cms.service.impl.logic.order.*;
import com.yhd.fa.marketing.cms.service.impl.verification.order.GetOrderListVerification;
import com.yhd.fa.marketing.cms.service.impl.verification.order.SynchronizationOrderCollectionVerification;
import com.yhd.fa.marketing.cms.service.impl.verification.order.UpdateOrderPaymentSerialNumberVerification;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderServiceImpl.java, v0.1 2022/12/2 14:24 yehuasheng Exp $
 */
@Service
public class OrderServiceImpl implements OrderService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderServiceImpl.class.getName());

    /**
     * 订单列表校验
     */
    @Resource
    private GetOrderListVerification getOrderListVerification;

    /**
     * 订单列表逻辑
     */
    @Resource
    private GetOrderListLogic getOrderListLogic;

    /**
     * 获取订单详情逻辑
     */
    @Resource
    private GetOrderInfoLogic getOrderInfoLogic;

    /**
     * 同步创建订单逻辑
     */
    @Resource
    private SynchronizationCreateOrderLogic synchronizationCreateOrderLogic;

    /**
     * 检查同步订单收款单
     */
    @Resource
    private SynchronizationOrderCollectionVerification synchronizationOrderCollectionVerification;

    /**
     * 重置同步收款单
     */
    @Resource
    private SynchronizationOrderCollectionLogic synchronizationOrderCollectionLogic;

    /**
     * 检查更新订单支付流水号
     */
    @Resource
    private UpdateOrderPaymentSerialNumberVerification updateOrderPaymentSerialNumberVerification;

    /**
     * 更新订单支付流水号逻辑
     */
    @Resource
    private UpdateOrderPaymentSerialNumberLogic updateOrderPaymentSerialNumberLogic;

    /**
     * 检查用户昨天下单逻辑
     */
    @Resource
    private CheckUserOrderLogic checkUserOrderLogic;

    @Resource
    private UpdateOrderEggStatusLogic updateOrderEggStatusLogic;

    @Resource
    private OrderDAO orderDAO;

    /**
     * 发票DAO
     */
    @Resource
    private InvoiceDAO invoiceDAO;

    /**
     * 获取订单列表
     *
     * @param orderListRequestVO 订单列表请求参数
     * @return BusinessResponse<PageInfo < OrderListResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<OrderListResponseVO>> getOrderList(OrderListRequestVO orderListRequestVO) {
        logger.info("start get order list service.");

        // 校验参数
        BusinessResponse<PageInfo<OrderListResponseVO>> checkOrderParameter = getOrderListVerification.check(orderListRequestVO);
        if (!checkOrderParameter.success()) {
            return checkOrderParameter;
        }

        // 获取订单列表逻辑
        return getOrderListLogic.exec(orderListRequestVO);
    }

    /**
     * 获取订单详情页信息
     *
     * @param orderId 订单id
     * @return BusinessResponse<OrderInfoResponseVO>
     */
    @Override
    public BusinessResponse<OrderInfoResponseVO> getOrderInfo(String orderId) {
        logger.info("start get order info service.");

        // 执行逻辑
        return getOrderInfoLogic.exec(orderId);
    }

    /**
     * 同步创建订单
     *
     * @param orderId 订单id
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<Void> synchronizationCreateOrder(String orderId) {
        logger.info("start synchronization create order service.");

        // 执行同步
        return synchronizationCreateOrderLogic.exec(orderId);
    }

    /**
     * 同步订单收款单记录
     *
     * @param orderNumber 订单号
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<Void> synchronizationOrderCollection(String orderNumber) {
        logger.info("start synchronization order collection service.");

        // 检查
        BusinessResponse<OrderCollectionLogPO> check = synchronizationOrderCollectionVerification.check(orderNumber);
        if (!check.success()) {
            return BusinessResponse.fail(check.getRt_code(), check.getRt_msg());
        }

        // 执行同步
        return synchronizationOrderCollectionLogic.exec(check.getData().getId());
    }

    /**
     * 更新订单支付方式以及流水号
     *
     * @param updateOrderPaymentSerialNumberRequestVO 更新的内容
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<Void> updateOrderPaymentSerialNumber(UpdateOrderPaymentSerialNumberRequestVO updateOrderPaymentSerialNumberRequestVO) {
        logger.info("start update order payment serial number.");

        // 校验参数
        BusinessResponse<Void> check = updateOrderPaymentSerialNumberVerification.check(updateOrderPaymentSerialNumberRequestVO);
        if (!check.success()) {
            return check;
        }

        // 执行逻辑
        return updateOrderPaymentSerialNumberLogic.exec(updateOrderPaymentSerialNumberRequestVO);
    }

    /**
     * 检查用户昨天是否下过订单
     *
     * @param userCodeListRequestVO 用户编码集合
     * @return BusinessResponse<List < CheckUserOrderResponseVO>>
     */
    @Override
    public BusinessResponse<List<CheckUserOrderResponseVO>> checkUserOrder(UserCodeListRequestVO userCodeListRequestVO) {
        logger.info("start check user order service.");

        // 执行逻辑
        return checkUserOrderLogic.exec(userCodeListRequestVO);
    }

    /**
     * 更新订单砸金蛋状态
     *
     * @param updateOrderEggStatusRequestVO 请求参数
     * @return BusinessResponse<Object>
     */
    @Override
    public BusinessResponse<Object> updateOrderEggStatus(UpdateOrderEggStatusRequestVO updateOrderEggStatusRequestVO) {
        logger.info("start update order egg status service.");

        // 执行逻辑
        return updateOrderEggStatusLogic.exec(updateOrderEggStatusRequestVO);
    }

    /**
     * 根据用户编码集合获取最后下单日期
     *
     * @param lastOrderDateRequestVO 请求参数
     * @return BusinessResponse<List<LastOrderDateResponseVO>>
     */
    @Override
    public BusinessResponse<List<LastOrderDateResponseVO>> getLastOrderDate(LastOrderDateRequestVO lastOrderDateRequestVO) {
        List<String> userCodeList = lastOrderDateRequestVO.getUserCodeList();
        List<LastOrderDateResponseVO> list = orderDAO.getLastOrderDate(userCodeList);
        return BusinessResponse.ok(list);
    }

    /**
     * 更新发票信息公司名称
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @Override
    public BusinessResponse<Object> updateInvoiceInfoCompanyName(UpdateInvoiceCompanyNameRequestVO requestVO) {
        logger.info("start update invoice info company name logic.");

        //执行更新
        int update = invoiceDAO.update(null, new LambdaUpdateWrapper<InvoicePO>()
                .set(InvoicePO::getUpdatedBy, "userCenterUpdateCompanyName")
                .set(InvoicePO::getUpdatedDate, LocalDateTime.now())
                .set(InvoicePO::getCompanyName, requestVO.getCompanyName())
                .eq(InvoicePO::getCompanyCode, requestVO.getCompanyCode()));

        if (update <= CommonConstant.ZERO) {
            logger.error("no invoice info company name updated. parameter:{}", requestVO);
            return BusinessResponseCommon.ok(null);
        }

        logger.info("update invoice info company name success. updateCount:{}", update);
        return BusinessResponseCommon.ok(null);
    }
}
