package com.yhd.fa.marketing.cms.pojo.po;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.common.pojo.po.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (FbQuotation)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 16:15:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "fb_quotation")
public class FbQuotationPO extends BaseEntity {

    /**
     * 报价单号
     */
    private String quotationNumber;
    /**
     * 报价单等级，ordCo普通企业，certCo认证企业
     */
    private String quotationLevel;
    /**
     * 客户报价单号
     */
    private String customerQuotationNumber;
    /**
     * 企业编码
     */
    private String companyCode;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 采购用户编码
     */
    private String purchaseUserCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 报价完成时间
     */
    private LocalDateTime quotationCompletionDate;
    /**
     * erp报价人员
     */
    private String quotationUserCode;
    /**
     * 报价状态，not_quoted未报价。quotation快速报价中，finish报价完成，close已终止，outTime超出报价有效期
     */
    private String quotationStatus;
    /**
     * 总价
     */
    private Double totalPrice;
    /**
     * 应付金额
     */
    private Double payablePrice;
    /**
     * 销售报价单号
     */
    private String saleQuotationNumber;
    /**
     * 同步状态，synchronized已同步，notSynchronized未同步，synchronizing同步中
     */
    private String synchronizationStatus;
    /**
     * 同步时间
     */
    private Date synchronizationDate;
    /**
     * 同步返回的内容
     */
    private String synchronizationMessage;
    /**
     * 审核状态，agree同意、refuse拒绝、checkPending待审核
     */
    private String examineStatus;
    /**
     * 删除状态，normal正常，recycle已加入回收站，deleted永久删除
     */
    private String deleteStatus;
    /**
     * 删除时间
     */
    private Date deleteDate;
    /**
     * 是否已转订单，true已转，false未转
     */
    private String transferOrder;
    /**
     * 是否已完成报价推送给用户，pushed已推送，not_pushed未推送
     */
    private String pushUser;
    /**
     * 是否未报价推送给业务员，not_pushed未推送，pushed_two_min为报价单2分钟推送给业务员，pushed_three_hour未报价2小时推送给业务员
     */
    private String pushSalesman;
    /**
     * 无效原因
     */
    private String invalidReason;
    /**
     * 渠道来源 pc电脑端 wap手机端 app端 wechat微信 applet小程序
     */
    private String channelType;
    /**
     * 是否内部人员创建
     */
    private String isInsideCreated;
    /**
     * 内部员工编号
     */
    private String insideEmployeeCode;
    /**
     * 员工名字
     */
    private String employeeName;
    /**
     * 平台标识
     */
    private String platformCode;



    @Schema(description = "公司归属")
    private String ownershipCompany;

    @Schema(description = "跟单员")
    private String merchandiser;

    @Schema(description = "业务员")
    private String operator;


    @Schema(description = "销售部门")
    private String unitName;

    @TableField(exist = false)
    @Schema(description = "全部订单总金额")
    private BigDecimal totalPriceAll;

    @TableField(exist = false)
    @Schema(description = "部分订单总金额")
    private BigDecimal totalPricePart;
}

