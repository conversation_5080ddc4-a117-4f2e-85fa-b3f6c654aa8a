package com.yhd.fa.marketing.cms.service.controller;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.CouponOrderCountRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderCountResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderFinishCountResponseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderMarketingService.java, v 0.1 2024/8/8 9:39 JiangYuHong Exp $
 */
public interface OrderMarketingService {

    /**
     * 优惠券订单完成
     * @param couponId 优惠券id
     * @return businessResponse
     */
    BusinessResponse<CouponOrderFinishCountResponseVO> couponOrderFinish(List<String> couponId);

    /**
     * 优惠券订单统计
     *
     * @param requestVO 请求参数
     * @return businessResponse
     */
    BusinessResponse<List<CouponOrderCountResponseVO>> couponOrderCount(CouponOrderCountRequestVO requestVO);
}
