package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "报价详情列表")
public class FbOrderDetailListResponseVO extends BaseVO {
    @Schema(description = "明细id")
    private String detailId;

    @Schema(description = "明细序号")
    private Integer sortId;

    @Schema(description = "报价单号")
    private String quotationNumber;

    @Schema(description = "总数")
    private Long quantity;

    @Schema(description = "已发货数")
    private Long sendQuantity;

    @Schema(description = "订单明细状态;明细状态  unpaid 待支付、untreated 待确认、confirm 已确认、stockUp 备货中、takeDelivered 待收货、finish 已完成、cancelling 取消中、cancel 已取消、closed 已关闭")
    private String orderDetailStatus;

    @Schema(description = "怡合达备注")
    private String examineRemark;

    @Schema(description = "订单明细备注")
    private String remark;

    /**
     * 折扣单价
     */
    @Schema(description = "折扣单价", example = "8.00")
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价", example = "9.04")
    private BigDecimal taxDiscountPrice;

    /**
     * 原总价
     */
    @Schema(description = "原总价", example = "9.04")
    private BigDecimal totalPrice;

    @Schema(description = "回复延迟交期")
    private String replyToDelivery;

    @Schema(description = "预计发货日期")
    private String estimatedShippingDate;

//    @Schema(description = "报价方式")
//    private String quotationType;
//
//    @Schema(description = "报价人类别")
//    private String quotationUserType;
//
//    @Schema(description = "材料")
//    private String rawMaterial;
//
//    @Schema(description = "表面处理")
//    private String surfaceTreatment;
//
//    @Schema(description = "热处理")
//    private String heatTreatment;
//
//    @Schema(description = "利润系数")
//    private String profitCoefficient;
//
//    @Schema(description = "类别")
//    private String categoryCode;
}
