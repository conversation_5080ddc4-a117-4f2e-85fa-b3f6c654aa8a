package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "删除订单、报价单入参")
public class FbQuotationAndOrderDeleteRequestVO extends BaseVO {

    @Schema(description = "id集合")
    @NotEmpty(message = "id集合不能为空")
    private List<String> idList;


}
