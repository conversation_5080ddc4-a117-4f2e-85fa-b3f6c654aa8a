package com.yhd.fa.marketing.cms.web.order;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRemarkRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsReplyRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProblemCategoriesListResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderCommentsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsController.java, v0.1 2023/2/24 17:31 yehuasheng Exp $
 */
@Tag(name = "订单评价接口", description = "订单评价接口包括评价列表、评价详情、提交回复")
@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
public class OrderCommentsController {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(OrderCommentsController.class.getName());

    /**
     * 订单评论服务
     */
    @Resource
    private OrderCommentsService orderCommentsService;

    /**
     * 获取订单评论列表
     *
     * @param orderCommentsRequestVO 订单评论请求参数
     * @return BusinessResponse<PageInfo < OrderCommentsResponseVO>>
     */
    @Operation(summary = "订单评价列表")
    @PostMapping(value = UriConstant.ORDER_COMMENTS_LIST, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<PageInfo<OrderCommentsResponseVO>> orderComments(@RequestBody @Validated OrderCommentsRequestVO orderCommentsRequestVO) {
        logger.info("request get order comments list api. parameter orderCommentsRequestVO:{}", orderCommentsRequestVO);

        // 开始时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<PageInfo<OrderCommentsResponseVO>> businessResponse = orderCommentsService.getOrderCommentsList(orderCommentsRequestVO);
        logger.info("response get order comments list api result businessResponse:{}", businessResponse);
        logger.info("response get order comments list api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 订单评论详情页
     *
     * @param orderCommentsId 订单评论id
     * @return BusinessResponse<OrderCommentsDetailResponseVO>
     */
    @Operation(summary = "订单评论详情页", parameters = {@Parameter(name = "orderCommentsId", description = "订单评论id", example = "d8215a066a464aafb863c7a9c2c3ed20", required = true)})
    @GetMapping(value = UriConstant.ORDER_COMMENTS_DETAIL)
    public BusinessResponse<OrderCommentsDetailResponseVO> orderCommentsDetail(@RequestParam String orderCommentsId) {
        logger.info("request get order comments detail api. parameter orderCommentsId:{}", orderCommentsId);

        // 开始时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<OrderCommentsDetailResponseVO> businessResponse = orderCommentsService.getOrderCommentsInfo(orderCommentsId);
        logger.info("response get order comments detail api result businessResponse:{}", businessResponse);
        logger.info("response get order comments detail api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }


    /**
     * 订单评论回复
     *
     * @param requestVO 订单评论回复请求参数
     * @return BusinessResponse<Object>
     */
    @Operation(summary = "订单评论回复")
    @PostMapping(value = UriConstant.ORDER_COMMENTS_REPLY)
    public BusinessResponse<Object> orderCommentsReply(@RequestBody @Validated OrderCommentsReplyRequestVO requestVO) {

        logger.info("request order comments reply api. parameter:{}", requestVO);

        // 开始时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<Object> businessResponse = orderCommentsService.orderCommentsReply(requestVO);
        logger.info("response order comments reply api result businessResponse:{}", businessResponse);
        logger.info("response order comments reply api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;

    }

    /**
     * 订单评论详情页修改备注
     *
     * @param requestVO 订单评论详情页修改备注
     * @return BusinessResponse
     */
    @Operation(summary = "订单评论详情页修改备注")
    @PostMapping(value = UriConstant.ORDER_COMMENTS_DETAIL_OTHER_EDIT)
    public BusinessResponse<Object> orderCommentsRemark(@RequestBody @Validated OrderCommentsRemarkRequestVO requestVO) {
        logger.info("request order comments detail other edit api. parameter:{}", requestVO);
        // 开始时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<Object> businessResponse = orderCommentsService.orderCommentsRemark(requestVO);
        logger.info("response order comments detail other edit api result  businessResponse:{}", businessResponse);
        logger.info("response order comments detail other edit api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;

    }

    /**
     * 获取订单评论问题分类列表
     *
     * @return BusinessResponse
     */
    @Operation(summary = "获取订单评论问题分类列表")
    @GetMapping(value = UriConstant.ORDER_COMMENTS_PROBLEM_CATEGORIES_LIST)
    public BusinessResponse<List<ProblemCategoriesListResponseVO>> orderCommentsProblemCategoriesList() {
        logger.info("request order comments problem categories list api.");
        // 开始时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<List<ProblemCategoriesListResponseVO>> businessResponse = orderCommentsService.orderCommentsProblemCategoriesList();
        logger.info("response order comments problem categories list api result  businessResponse:{}", businessResponse);
        logger.info("response order comments problem categories list api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

}
