package com.yhd.fa.marketing.cms.pojo.vo.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class EnquiryListExportResponseVO extends BaseVO {

    /**
     * 产品名称
     */
    @Excel(name = "产品信息", width = 30)
    private String productName;

    /**
     * 数量
     */
    @Excel(name = "数量", width = 8, type = 10)
    private BigDecimal quantity;

    /**
     * 含税折扣单价
     */
    @Excel(name = "含税单价", width = 15, type = 10)
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总金额(元) total_price
     */
    @Excel(name = "含税小计(元)", width = 15, type = 10)
    private BigDecimal totalPrice;

    /**
     * 交期
     */
    @Excel(name = "发货天数", width = 10, type = 10)
    private Integer delivery;

    /**
     * 跟单员
     */
    @Excel(name = "跟单员", width = 18, type = 10)
    private String merchandiser;

    /**
     * 业务员
     */
    @Excel(name = "业务员", width = 18, type = 10)
    private String salesman;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称", width = 32)
    private String companyName;

    /**
     * 用户id
     */
    private String userCode;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称", width = 18, type = 10)
    private String userName;

    /**
     * 用户手机号码
     */
    @Excel(name = "用户手机号码", width = 15)
    private String userPhone;

    /**
     * 用户邮箱
     */
    @Excel(name = "用户邮箱", width = 25)
    private String userEmail;

    /**
     * 创建时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    private LocalDateTime registerDate;

    @Excel(name = "查询日期", width = 30)
    private String createdDateStr;

    @Excel(name = "注册时间", width = 30)
    private String registerDateStr;

}
