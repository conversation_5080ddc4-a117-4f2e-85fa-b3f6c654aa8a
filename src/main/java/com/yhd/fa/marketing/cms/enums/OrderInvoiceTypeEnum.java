package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderInvoiceTypeConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceTypeEnum.java, v0.1 2023/2/23 14:08 yehuasheng Exp $
 */
@Getter
public enum OrderInvoiceTypeEnum {
    GENERAL(OrderInvoiceTypeConstant.GENERAL, "普通"),
    DEDICATED(OrderInvoiceTypeConstant.DEDICATED, "专用"),
    ELECTRON_GENERAL(OrderInvoiceTypeConstant.ELECTRON_GENERAL, "全电普通"),
    ELECTRON_DEDICATED(OrderInvoiceTypeConstant.ELECTRON_DEDICATED, "全电专用"),
    ;

    private final String orderInvoiceType;
    private final String orderInvoiceTypeName;

    OrderInvoiceTypeEnum(String orderInvoiceType, String orderInvoiceTypeName) {
        this.orderInvoiceType = orderInvoiceType;
        this.orderInvoiceTypeName = orderInvoiceTypeName;
    }
}
