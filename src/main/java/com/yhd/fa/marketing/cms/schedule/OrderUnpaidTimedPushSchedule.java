package com.yhd.fa.marketing.cms.schedule;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.common.util.UUIDUtils;
import com.yhd.fa.marketing.cms.constant.*;
import com.yhd.fa.marketing.cms.mapper.OrderMapper;
import com.yhd.fa.marketing.cms.mapper.OrderTimedPushLogMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderTimedPushLogPO;
import com.yhd.fa.marketing.cms.service.sao.UnifiedPushMessageService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: OrderUnpaidTimedPushSchedule.java, v0.1 2023/9/27 08:46 yehuasheng Exp $
 */
@Component
@RefreshScope
public class OrderUnpaidTimedPushSchedule {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderUnpaidTimedPushSchedule.class.getName());

    /**
     * 定时推送mapper
     */
    @Resource
    private OrderTimedPushLogMapper orderTimedPushLogMapper;

    /**
     * 订单mapper
     */
    @Resource
    private OrderMapper orderMapper;

    /**
     * 模板id
     */
    @Value("${schedule.push.order.unpaidTemplateId}")
    private String templateId;

    /**
     * 用户中心信息
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 推送消息服务
     */
    @Resource
    private UnifiedPushMessageService unifiedPushMessageService;

    /**
     * 订单未支付定时推送
     */
    @XxlJob("orderUnpaidTimedPush")
    public void orderUnpaidTimedPush() {
        logger.info("start order unpaid timed push schedule.");

        // 查询需要定时推送的1个小时内未支付订单
        LocalDateTime nowDateTime = LocalDateTime.now();

        // 查询订单语句
        // SELECT
        //  id
        // FROM
        //  `fa_order`
        // WHERE id NOT IN
        //  (SELECT
        //    order_id
        //  FROM
        //    `fa_order_timed_push_log`
        //  WHERE push_platform = 'dingding'
        //    AND push_type = 'unpaid'
        //    AND created_date >= '当前时间前7天')
        //  AND order_status = 'unpaid'
        //  AND payment_status = 'unpaid'
        //  AND payment_type != 'bankTransfer'
        //  AND created_date <= '当前时间前1小时'
        //  AND created_date >= '当前时间前7天'
        List<OrderPO> orderList = orderMapper.list(new LambdaQueryWrapper<OrderPO>()
                .notInSql(OrderPO::getId, "SELECT `order_id` FROM `fa_order_timed_push_log` WHERE `push_platform` = '" + TimedPushLogPushPlatformConstant.DING_DING + "' AND `push_type` = '" + OrderTimedPushLogPushTypeConstant.UNPAID + "' AND `created_date` >= '" + nowDateTime.minusDays(CommonConstant.SEVEN) + "'")
                .eq(OrderPO::getOrderStatus, OrderStatusConstant.UNPAID)
                .eq(OrderPO::getPaymentStatus, PaymentStatusConstant.UNPAID)
                .ne(OrderPO::getPaymentType, PaymentTypeConstant.BANK_TRANSFER)
                .le(OrderPO::getCreatedDate, nowDateTime.minusHours(CommonConstant.ONE))
                .ge(OrderPO::getCreatedDate, nowDateTime.minusDays(CommonConstant.ONE))
                .select(OrderPO::getId, OrderPO::getOrderNumber, OrderPO::getCompanyCode, OrderPO::getUserCode, OrderPO::getCreatedDate, OrderPO::getCompanyName, OrderPO::getPayablePrice)
        );

        // 处理定时推送业务
        if (CollUtil.isNotEmpty(orderList)) {
            // 获取订单对应用户的业务员
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userMap = getUserInfo(orderList);

            // 推送钉钉
            List<String> orderIdPushFailList = pushOrderUnpaidToDingDing(orderList, userMap);

            // 过滤发送失败的订单
            orderList.removeIf(orderPO -> orderIdPushFailList.contains(orderPO.getId()));

            // 插入数据库
            insetOrderTimedPushLogData(orderList, userMap);
        }
    }

    /**
     * 获取推送的用户对应的业务员
     *
     * @param orderList 订单列表
     * @return Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO>
     */
    private Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserInfo(List<OrderPO> orderList) {
        logger.info("order unpaid timed push get pusher");

        // 提取用户编码和企业编码
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = orderList
                .stream()
                .map(
                        orderPO -> UserCodeAndCompanyCodeDTO
                                .builder()
                                .companyCode(orderPO.getCompanyCode())
                                .userCode(orderPO.getUserCode()).build())
                .collect(Collectors.toList());

        // 订单对于的用户编码
        Map<String, String> orderIdToUserMap = orderList.stream().collect(Collectors.toMap(OrderPO::getId, OrderPO::getUserCode, (a, b) -> a));

        // 获取业务员信息
        Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userInfoMap = userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);

        return orderIdToUserMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, orderCompanyCode -> userInfoMap.get(orderCompanyCode.getValue())));
    }

    /**
     * 添加定时推送未支付订单的记录
     *
     * @param orderList 订单列表
     * @param userMap   推送的业务员map
     */
    private void insetOrderTimedPushLogData(List<OrderPO> orderList, Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userMap) {
        logger.info("schedule insert data for db fa_order_timed_push_log");

        // 判断订单是否为空
        if (CollUtil.isEmpty(orderList)) {
            return;
        }

        // 插入记录
        List<OrderTimedPushLogPO> orderTimedPushLogPOS = orderList
                .stream()
                .map(orderPO
                        -> {
                    OrderTimedPushLogPO orderTimedPushLogPO = OrderTimedPushLogPO
                            .builder()
                            .orderId(orderPO.getId())
                            .pushType(OrderTimedPushLogPushTypeConstant.UNPAID)
                            .pushPlatform(TimedPushLogPushPlatformConstant.DING_DING)
                            .pushTemplateId(templateId)
                            .pusher(JSON.toJSONString(userMap.get(orderPO.getId())))
                            .build();

                    orderTimedPushLogPO.setId(UUIDUtils.getStringUUID());
                    orderTimedPushLogPO.setCreatedDate(LocalDateTime.now());
                    orderTimedPushLogPO.setCreatedBy(FaDocMarketingCmsConstant.ADMIN_STRING);

                    return orderTimedPushLogPO;
                })
                .collect(Collectors.toList());

        orderTimedPushLogMapper.saveBatch(orderTimedPushLogPOS);
    }

    /**
     * 推送订单未支付的钉钉消息
     *
     * @param orderList 订单列表
     * @param userMap   推送人
     * @return List<String>
     */
    private List<String> pushOrderUnpaidToDingDing(List<OrderPO> orderList, Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userMap) {
        logger.info("push order unpaid to dingding api.");

        // 定义返回订单的列表
        List<String> orderIdList = new ArrayList<>();

        orderList.stream().filter(orderPO -> userMap.containsKey(orderPO.getId())
                        && Optional.ofNullable(userMap.get(orderPO.getId()).getSalesManInfo()).isPresent())
                .forEach(orderPO -> {
                    // 组装要替换的内容
                    Map<String, Object> replaceContent = new HashMap<>();
                    replaceContent.put("orderNumber", orderPO.getOrderNumber());
                    replaceContent.put("userName", userMap.get(orderPO.getId()).getUserInfo().getUserName());
                    replaceContent.put("companyName", orderPO.getCompanyName());
                    replaceContent.put("userLink", Optional.ofNullable(userMap.get(orderPO.getId()).getUserInfo().getMobile()).orElse(userMap.get(orderPO.getId()).getUserInfo().getEmail()));
                    replaceContent.put("price", orderPO.getPayablePrice());
                    replaceContent.put("createdDate", orderPO.getCreatedDate());

                    // 推送人
                    List<String> receivers = Collections.singletonList(userMap.get(orderPO.getId()).getSalesManInfo().getEmployeeCode());

                    // 推送
                    BusinessResponse<String> businessResponse = unifiedPushMessageService.sendDingDingOneToMany(templateId, receivers, replaceContent);
                    if (!businessResponse.success()) {
                        orderIdList.add(orderPO.getId());
                    }
                });

        return orderIdList;
    }
}
