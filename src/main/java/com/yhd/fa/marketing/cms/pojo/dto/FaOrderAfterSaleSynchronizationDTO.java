package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: FaOrderAfterSaleSynchronizationDTO.java, v0.1 2023/3/2 10:35 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaOrderAfterSaleSynchronizationDTO extends BaseDTO {

    /**
     * 订单售后id
     */
    private String id;

    /**
     * 售后单号
     */
    private String afterSaleNumber;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单支付方式
     * unknown 暂时未知的支付方式
     * aliPay 支付宝、
     * weChatPay 微信支付、
     * bankTransfer 银行转账、
     * unionPay银联、
     * monthlyKnot 月结、
     * monthlyKnot30 月结30天、
     * monthlyKnot60 月结60天、
     * monthlyKnot90 月结90天
     */
    private String orderPaymentType;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * erp编码
     */
    private String erpCompanyCode;

    /**
     * 是否收到货
     */
    private String shipmentReceiptStatus;

    /**
     * 售后类型：退货退款 refund；换货 exchange；维修 repair
     */
    private String afterSaleType;

    /**
     * 图片路径
     */
    private String imagePath;

    /**
     * 申请理由
     */
    private String suggestReason;

    /**
     * 问题描述
     */
    private String problemDescription;

    /**
     * 售后价格
     */
    private BigDecimal afterSalePrice;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请人电话
     */
    private String applicantPhone;

    /**
     * 申请人邮箱
     */
    private String applicantEmail;

    /**
     * 所属地区 DGYHD东莞 SZYHD苏州
     */
    private String territory;

    /**
     * 用户寄回商品快递公司名称
     */
    private String userReturnCourier;

    /**
     * 用户寄回商品快递单号
     */
    private String userReturnTrackingNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 订单售后详情
     */
    private List<FaOrderAfterSaleDetailSynchronizationDTO> orderAfterSaleDetails;
}
