package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsReplyRequestVO.java, v 0.1 2023/3/6 14:11  $
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderCommentsRemarkRequestVO extends BaseVO {

    @Schema(description = "订单评价id")
    @NotEmpty(message = "请操作正确的订单评价信息")
    private String commentsId;

    /**
     * 备注
     */
    @Schema(description = "备注(All:全部、workOrder：已生成工单、invalidComplaint无效投诉、suggestions:改善建议)", example = "已生成工单")
    private String remark;

    /**
     * 问题分类id
     */
    @Schema(description = "问题分类id列表", example = "[1,2,3]")
    private List<Integer> problemCategoriesId;

    /**
     * 回访记录
     */
    @Schema(description = "回访记录", example = "已回访")
    private String visitRecords;
}
