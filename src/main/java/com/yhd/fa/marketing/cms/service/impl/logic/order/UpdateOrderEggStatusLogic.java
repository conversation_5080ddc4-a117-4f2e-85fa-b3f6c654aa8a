package com.yhd.fa.marketing.cms.service.impl.logic.order;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.mapper.OrderMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.UpdateOrderEggStatusRequestVO;
import jodd.util.StringUtil;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: UpdateOrderEggStatusLogic.java, v 0.1 2024/4/7 上午10:43 JiangYuHong Exp $
 */
@Component
public class UpdateOrderEggStatusLogic {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(UpdateOrderEggStatusLogic.class.getName());


    /**
     * 订单mapper
     */
    @Resource
    private OrderMapper orderMapper;


    public BusinessResponse<Object> exec(UpdateOrderEggStatusRequestVO updateOrderEggStatusRequestVO) {
        logger.info("start update order egg status logic.");
        // 检查来源
        if (!StringUtil.equals(updateOrderEggStatusRequestVO.getRequestSource(),"integralMall")) {
            return BusinessResponse.fail("来源错误");
        }


        // 更新订单蛋状态
        boolean update = orderMapper.update(null,
                new LambdaUpdateWrapper<OrderPO>()
                        .eq(OrderPO::getIntegralEgg, CommonConstant.FALSE)
                        .ge(OrderPO::getCreatedDate, updateOrderEggStatusRequestVO.getStartTime())
                        .lt(OrderPO::getCreatedDate, updateOrderEggStatusRequestVO.getEndTime())
                        .set(OrderPO::getIntegralEgg, CommonConstant.TRUE)
        );

        if (!update){
            return BusinessResponse.fail("更新失败砸金蛋状态失败!");
        }

        return BusinessResponse.ok(null);
    }
}
