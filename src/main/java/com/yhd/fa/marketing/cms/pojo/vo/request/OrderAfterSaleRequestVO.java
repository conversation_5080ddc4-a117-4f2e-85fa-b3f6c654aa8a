package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.OrderAfterSaleStatusConstant;
import com.yhd.fa.marketing.cms.constant.OrderAfterSaleTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleRequestVO.java, v0.1 2023/2/24 14:33 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderAfterSaleRequestVO extends PageRequestVO {
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-01-01 10:10:10", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDateTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-01-01 10:10:10", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDateTime;

    /**
     * 订单售后单号
     */
    @Schema(description = "订单售后单号", example = "ACI201811136064")
    private String orderAfterSaleNumber;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "MYI0000013941364829")
    private String orderNumber;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "广东唯康教育科技股份有限公司")
    private String companyName;

    /**
     * 订单售后状态
     */
    @Schema(description = "状态：待审核 pendingAudit； 待售后方案(ERP发起单审核同意) pendingPlan； 待寄回商品 pendingReturn； 寄回商品待检测 pendingCheck； 待退款 pendingRefund； pendingShip； 待收货 pendingReceive； 待上门维修 pendingRepair； 售后完成 completed； 售后关闭 closed；用户取消 cancel；退款中 refunding", example = "processing")
    @ValueInEnum(matchTarget = {
            OrderAfterSaleStatusConstant.PENDING_AUDIT,
            OrderAfterSaleStatusConstant.PENDING_PLAN,
            OrderAfterSaleStatusConstant.PENDING_RETURN,
            OrderAfterSaleStatusConstant.PENDING_CHECK,
            OrderAfterSaleStatusConstant.PENDING_REFUND,
            OrderAfterSaleStatusConstant.PENDING_SHIP,
            OrderAfterSaleStatusConstant.PENDING_RECEIVE,
            OrderAfterSaleStatusConstant.PENDING_REPAIR,
            OrderAfterSaleStatusConstant.COMPLETED,
            OrderAfterSaleStatusConstant.CLOSED,
            OrderAfterSaleStatusConstant.CANCEL,
            CommonConstant.EMPTY }, message = "订单售后状态错误")
    private String orderAfterSaleStatus;

    /**
     * 订单售后类型
     */
    @Schema(description = "订单售后类型 exchange 换货 refund 退款 repair 维修", example = "exchange换货")
    @ValueInEnum(matchTarget = {
            OrderAfterSaleTypeConstant.EXCHANGE,
            OrderAfterSaleTypeConstant.REFUND,
            OrderAfterSaleTypeConstant.REPAIR,
            CommonConstant.EMPTY }, message = "订单售后类型错误")
    private String afterSaleType;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态 true已同步 false未同步", example = "true")
    private String synchronizationStatus;

    /**
     * 是否已评论 true 已评论 false 未评论
     */
    @Schema(description = "是否已评论 true 已评论 false 未评论", example = "true")
    private String replyStatus;
}
