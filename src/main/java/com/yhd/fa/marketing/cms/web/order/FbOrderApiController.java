package com.yhd.fa.marketing.cms.web.order;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbOrderPageRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationAndOrderDeleteRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderExportResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderTotalPriceCountResponseVO;
import com.yhd.fa.marketing.cms.service.controller.FbOrderApiService;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 8:38
 */
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
@RestController
@Tag(name = "非标订单cmsApi对接", description = "非标非标订单cmsApi对接")
public class FbOrderApiController {

    @Resource
    private FbOrderApiService fbOrderApiService;

    @Operation(summary = "Fb订单分页列表数据获取")
    @PostMapping(value = "/fb/order-page", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb订单列表数据获取")
    public BusinessResponse<PageInfo<FbOrderListResponseVO>> getFbOrderPage(@RequestBody @Validated FbOrderPageRequestVO fbOrderPageRequestVO) {
        return fbOrderApiService.getFbOrderPage(fbOrderPageRequestVO);
    }


    @Operation(summary = "Fb订单详情数据获取")
    @GetMapping(value = "/fb/order-detail")
    @SysLog(value = "Fb订单详情数据获取")
    public BusinessResponse<FbOrderDetailResponseVO> getFbOrderDetail(@RequestParam(value = "id") String id) {
        return fbOrderApiService.getFbOrderDetail(id);
    }

    @Operation(summary = "Fb订单单据统计金额")
    @PostMapping(value = "/fb/order-count", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb订单列表数据获取")
    public BusinessResponse<FbOrderTotalPriceCountResponseVO> getFbOrderCount(@RequestBody @Validated FbOrderPageRequestVO fbOrderPageRequestVO) {
        return fbOrderApiService.getFbOrderCount(fbOrderPageRequestVO);
    }

    @Operation(summary = "Fb订单删除")
    @PostMapping(value = "/fb/order-delete", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb订单删除")
    public BusinessResponse<Object> deleteFbQuotation(@RequestBody @Validated FbQuotationAndOrderDeleteRequestVO fbOrderDeleteRequestVO) {
        return fbOrderApiService.deleteOrder(fbOrderDeleteRequestVO);
    }

    @Operation(summary = "Fb订单分页列表导出数据")
    @PostMapping(value = "/fb/order-export", consumes = MediaType.APPLICATION_JSON)
    @SysLog(value = "Fb订单列表导出数据")
    public BusinessResponse<List<FbOrderExportResponseVO>> getFbOrderExport(@RequestBody @Validated FbOrderPageRequestVO fbOrderPageRequestVO) {
        return fbOrderApiService.getFbOrderExport(fbOrderPageRequestVO);
    }

}
