package com.yhd.fa.marketing.cms.service.controller;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.response.ExcelFileAnalysisResponseVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: QuotationUploadFileService.java, v0.1 2023/1/3 17:48 yehuasheng Exp $
 */
public interface QuotationUploadFileService {
    /**
     * 解析excel的内容
     *
     * @param excelFile excel文件流
     * @param userCode  用户编码
     * @return BusinessResponse<List < ExcelFileAnalysisResponseVO>>
     */
    BusinessResponse<List<ExcelFileAnalysisResponseVO>> excelFileAnalysis(MultipartFile excelFile, String userCode);
}
