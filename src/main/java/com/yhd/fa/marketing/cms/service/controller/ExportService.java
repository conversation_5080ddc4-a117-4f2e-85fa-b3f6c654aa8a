package com.yhd.fa.marketing.cms.service.controller;

import com.yhd.fa.marketing.cms.pojo.vo.request.ExportEnquiryRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryListExportResponseVO;
import org.springframework.validation.BindingResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ExportService {
    /**
     * 导出价单excel文件
     * @param requestVO 导出报价单参数
     * @param result    校验的返回值
     * @param response  返回的内容
     */
    void exportExcel(ExportEnquiryRequestVO requestVO, BindingResult result, HttpServletResponse response);
}
