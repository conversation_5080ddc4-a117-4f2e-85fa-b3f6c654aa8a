package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderInvoiceNatureConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceNatureEnum.java, v0.1 2023/2/23 14:14 yehuasheng Exp $
 */
@Getter
public enum OrderInvoiceNatureEnum {
    PAPER(OrderInvoiceNatureConstant.PAPER, "纸质"),
    ELECTRON(OrderInvoiceNatureConstant.ELECTRON, "电子"),
    ALL_ELECTRON(OrderInvoiceNatureConstant.ALL_ELECTRON, "全电"),
    ;

    private final String orderInvoiceNature;
    private final String orderInvoiceNatureName;

    OrderInvoiceNatureEnum(String orderInvoiceNature, String orderInvoiceNatureName) {
        this.orderInvoiceNature = orderInvoiceNature;
        this.orderInvoiceNatureName = orderInvoiceNatureName;
    }
}
