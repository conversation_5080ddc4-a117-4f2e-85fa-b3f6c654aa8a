package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: UpdateOrderEggStatusRequestVO.java, v 0.1 2024/4/7 上午10:34 JiangYuHong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateOrderEggStatusRequestVO extends BaseVO {

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2023-02-21 08:47:00")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2023-02-21 08:47:00")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 请求来源
     */
    @Schema(description = "请求来源", example = "integralMall")
    @NotEmpty(message = "请求来源不能为空")
    private String requestSource;
}
