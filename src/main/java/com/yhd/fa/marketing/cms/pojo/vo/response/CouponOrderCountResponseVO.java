package com.yhd.fa.marketing.cms.pojo.vo.response;


import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: CouponOrderCountResponseVO.java, v 0.1 2025/7/7 11:12 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CouponOrderCountResponseVO extends BaseVO {

    /**
     * 促销活动id
     */
    @Schema(description = "促销活动id", example = "1")
    private String promotionId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "YI202212030829479779T5GL")
    private String orderNumber;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "HA5814973528")
    private String companyCode;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "139779")
    private String userCode;

    /**
     * 总金额
     */
    @Schema(description = "总金额", example = "10.00")
    private BigDecimal totalMoney;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-01-01 10:10:10")
    private LocalDateTime createdDate;
}
