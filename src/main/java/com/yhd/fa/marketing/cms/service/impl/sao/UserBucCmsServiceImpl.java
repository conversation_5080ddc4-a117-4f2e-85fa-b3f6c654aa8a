package com.yhd.fa.marketing.cms.service.impl.sao;

import cn.hutool.core.collection.CollUtil;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.*;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.*;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.UserInfo;
import com.yhd.buc.cms.api.sdk.utils.UserApiUtil;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CustomerCompanyListResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: UserBucCmsServiceImpl.java, v0.1 2022/12/5 9:03 yehuasheng Exp $
 */
@Service
public class UserBucCmsServiceImpl implements UserBucCmsService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(UserBucCmsServiceImpl.class.getName());

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 根据用户编码获取用户基础信息
     *
     * @param userCode 用户编码
     * @return List<BaseUserInfoResponseVO>
     */
    @Override
    public List<UserInfo> getUserBaseListByUserCode(List<String> userCode) {
        logger.info("get user base by user code for yhd-buc-cms-foreign service.");

        // 设置参数
        BaseUserInfoRequestVO baseUserInfoRequestVO = BaseUserInfoRequestVO
                .builder()
                .userCodeList(new HashSet<>(userCode))
                .build();
        // 请求接口
        List<UserInfo> userInfoByUserCodesOrUserAccounts = UserApiUtil.getUserInfoByUserCodesOrUserAccounts(baseUserInfoRequestVO);
        logger.info("get user base by user code success userInfoByUserCodesOrUserAccounts:{}", userInfoByUserCodesOrUserAccounts);

        if (CollUtil.isEmpty(userInfoByUserCodesOrUserAccounts)) {
            return new ArrayList<>();
        }

        return userInfoByUserCodesOrUserAccounts;
    }

    /**
     * 根据用户编码获取用户基础信息
     *
     * @param userCode 用户编码
     * @return Map<String, BaseUserInfoResponseVO>
     */
    @Override
    public Map<String, UserInfo> getUserBaseMapByUserCode(List<String> userCode) {
        logger.info("get user base map by user code.");

        // 获取用户信息集合
        List<UserInfo> userBaseList = getUserBaseListByUserCode(userCode);

        // 设置map
        Map<String, UserInfo> userBaseMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userBaseList)) {
            userBaseMap.putAll(userBaseList.stream().collect(Collectors.toMap(UserInfo::getUserCode, Function.identity(), (a, b) -> a)));
        }

        return userBaseMap;
    }

    /**
     * 获取用户所有信息
     *
     * @param userCode    用户编码
     * @param companyCode 企业编码
     * @return CompanyAndUserAndMerchandiserAndResourcesResponseVO
     */
    @Override
    public CompanyAndUserAndMerchandiserAndResourcesResponseVO getUserInfo(String userCode, String companyCode) {
        logger.info("get all user info for yhd-buc-cms-foreign service.");

        // 设置请求参数
        UserAndCompanyAndMerchandiserRequestVO userAndCompanyAndMerchandiserRequestVO = UserAndCompanyAndMerchandiserRequestVO
                .builder()
                .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .userCodeList(Arrays.asList(userCode.split(CommonConstant.SYMBOL_COMMA)))
                .build();
        // 请求接口
        List<CompanyAndUserAndMerchandiserAndResourcesResponseVO> userAndCompanyByUserCodes = UserApiUtil.getUserAndCompanyByUserCodes(userAndCompanyAndMerchandiserRequestVO);
        logger.info("get all user info userAndCompanyByUserCodes:{}", userAndCompanyByUserCodes);

        if (CollUtil.isEmpty(userAndCompanyByUserCodes)) {
            return new CompanyAndUserAndMerchandiserAndResourcesResponseVO();
        }

        // 获取企业信息
        if (StringUtils.isNotBlank(companyCode)) {
            // 设置请求企业信息
            CompanyAndMerchandiserAndSalesmanInfoRequestVO companyAndMerchandiserAndSalesmanInfoRequestVO = CompanyAndMerchandiserAndSalesmanInfoRequestVO
                    .builder()
                    .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                    .companyCodeList(Collections.singletonList(companyCode))
                    .build();
            List<CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyInfoList = UserApiUtil.companyAndMerchandiserAndSalesmanInfo(companyAndMerchandiserAndSalesmanInfoRequestVO);
            if (CollUtil.isNotEmpty(companyInfoList)) {
                CompanyAndMerchandiserAndSalesmanInfoResponseVO companyInfo = companyInfoList.get(CommonConstant.ZERO);
                userAndCompanyByUserCodes.forEach(companyAndUserAndMerchandiserAndResourcesResponseVO -> {
                    companyAndUserAndMerchandiserAndResourcesResponseVO.setMerchandiserInfo(companyInfo.getMerchandiserInfo());
                    companyAndUserAndMerchandiserAndResourcesResponseVO.setSalesManInfo(companyInfo.getSalesManInfo());
                });
            }
        }

        return userAndCompanyByUserCodes.get(CommonConstant.ZERO);
    }

    /**
     * 根据用户编码集合获取用户信息-企业信息-跟单信息
     *
     * @param userCodeAndCompanyCodeDTO 用户编码和企业编码集合
     * @return Map<String, UserAndCompanyAndMerchandiserResponseVO>
     */
    @Override
    public Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserAndCompanyBaseInfo(List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO) {
        logger.info("start get user and company base info data for user service.");

        // 设置请求的用户编码集合
        List<String> userCodeList = userCodeAndCompanyCodeDTO
                .stream()
                .map(UserCodeAndCompanyCodeDTO::getUserCode)
                .collect(Collectors.toList());
        userCodeList.addAll(userCodeAndCompanyCodeDTO
                .stream()
                .map(UserCodeAndCompanyCodeDTO::getPurchaseUserCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList()));
        List<String> userCode = userCodeList.stream().distinct().collect(Collectors.toList());
        // 设置请求的企业编码集合
        List<String> companyCode = userCodeAndCompanyCodeDTO
                .stream()
                .map(UserCodeAndCompanyCodeDTO::getCompanyCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        // 设置入参用户对应的企业
        Map<String, String> userCompanyCodeMap = userCodeAndCompanyCodeDTO.stream().filter(userCodeAndCompanyCode -> StringUtils.isNotBlank(userCodeAndCompanyCode.getCompanyCode())).collect(Collectors.toMap(UserCodeAndCompanyCodeDTO::getUserCode, UserCodeAndCompanyCodeDTO::getCompanyCode, (a, b) -> a));

        // 设置请求参数
        UserAndCompanyAndMerchandiserRequestVO userAndCompanyAndMerchandiserRequestVO = UserAndCompanyAndMerchandiserRequestVO
                .builder()
                .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .userCodeList(userCode)
                .build();
        // 请求
        List<CompanyAndUserAndMerchandiserAndResourcesResponseVO> userAndCompanyByUserCodes = UserApiUtil.getUserAndCompanyByUserCodes(userAndCompanyAndMerchandiserRequestVO);

        // 组装一个代码对应一个对象
        Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userInfo = new HashMap<>();
        if (CollUtil.isNotEmpty(userAndCompanyByUserCodes)) {
            userInfo.putAll(userAndCompanyByUserCodes
                    .stream()
                    .collect(Collectors
                            .toMap(CompanyAndUserAndMerchandiserAndResourcesResponseVO::getUserCode,
                                    Function.identity(),
                                    (a, b) -> a)
                    )
            );
        }

        if (CollUtil.isNotEmpty(companyCode)) {
            // 设置请求企业信息
            CompanyAndMerchandiserAndSalesmanInfoRequestVO companyAndMerchandiserAndSalesmanInfoRequestVO = CompanyAndMerchandiserAndSalesmanInfoRequestVO
                    .builder()
                    .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                    .companyCodeList(companyCode)
                    .build();
            List<CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyInfoList = UserApiUtil.companyAndMerchandiserAndSalesmanInfo(companyAndMerchandiserAndSalesmanInfoRequestVO);
            if (CollUtil.isNotEmpty(companyInfoList)) {
                Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyInfo = companyInfoList
                        .stream()
                        .collect(Collectors
                                .toMap(CompanyAndMerchandiserAndSalesmanInfoResponseVO::getCompanyCode,
                                        Function.identity(),
                                        (a, b) -> a)
                        );
                userInfo.values().stream().filter(companyAndUserAndMerchandiserAndResourcesResponseVO -> null != companyAndUserAndMerchandiserAndResourcesResponseVO.getCompanyInfo()).forEach(companyAndUserAndMerchandiserAndResourcesResponseVO -> {
                    if (userCompanyCodeMap.containsKey(companyAndUserAndMerchandiserAndResourcesResponseVO.getUserCode())
                            && companyInfo.containsKey(userCompanyCodeMap.get(companyAndUserAndMerchandiserAndResourcesResponseVO.getUserCode()))) {
                        companyAndUserAndMerchandiserAndResourcesResponseVO.setSalesManInfo(companyInfo.get(userCompanyCodeMap.get(companyAndUserAndMerchandiserAndResourcesResponseVO.getUserCode())).getSalesManInfo());
                        companyAndUserAndMerchandiserAndResourcesResponseVO.setMerchandiserInfo(companyInfo.get(userCompanyCodeMap.get(companyAndUserAndMerchandiserAndResourcesResponseVO.getUserCode())).getMerchandiserInfo());
                    }
                });
            }
        }

        return userInfo;
    }

    /**
     * 根据企业名称搜索企业列表
     *
     * @param companyName 企业名称
     * @return List<CustomerCompanyListResponseVO>
     */
    @Override
    public List<CustomerCompanyListResponseVO> searchCompanyListByCompanyName(String companyName) {
        logger.info("start exec request search company api.");

        // 设置请求参数
        CompanySearchRequestVO companySearchRequestVO = CompanySearchRequestVO
                .builder()
                .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .companyName(companyName)
                .build();
        // 请求获取企业信息
        List<CompanySearchResponseVO> companySearchResponseVOS = UserApiUtil.companySearch(companySearchRequestVO);
        logger.info("search company success companySearchResponseVOS:{}", companySearchResponseVOS);

        if (CollUtil.isEmpty(companySearchResponseVOS)) {
            return new ArrayList<>();
        }

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            List<String> companyCodeList = SecurityUtil.getOperatorCompanyCode();
            if (CollUtil.isNotEmpty(companyCodeList)) {
                // 过滤业务员非业务员的企业
                companySearchResponseVOS.removeIf(companySearchResponseVO -> !companyCodeList.contains(companySearchResponseVO.getCompanyCode()));

                if (CollUtil.isEmpty(companySearchResponseVOS)) {
                    return new ArrayList<>();
                }
            } else {
                //关联企业为空 直接返回空交给上一层判断
                return new ArrayList<>();
            }
        }


        // 组装返回参数
        return companySearchResponseVOS
                .stream()
                .map(companySearchResponseVO
                        -> CustomerCompanyListResponseVO
                        .builder()
                        .companyCode(companySearchResponseVO.getCompanyCode())
                        .companyName(companySearchResponseVO.getCompanyName())
                        .erpCompanyCode(companySearchResponseVO.getErpCompanyCode())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 根据企业编码获取用户列表
     *
     * @param companyCode 企业编码
     * @return List<CustomerUserListResponseVO>
     */
    @Override
    public List<CompanyUserCustomResponseVO> getUserListByCompanyCode(String companyCode) {
        logger.info("start get user list by company code.");

        // 设置请求参数
        CompanyUserCustomListRequestVO companyUserCustomListRequestVO = CompanyUserCustomListRequestVO
                .builder()
                .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .companyCode(companyCode)
                .build();
        // 获取用户
        List<CompanyUserCustomResponseVO> customerUserList = UserApiUtil.companyUserCustomList(companyUserCustomListRequestVO);
        logger.info("get user list by company code success customerUserList:{}", customerUserList);

        if (CollUtil.isEmpty(customerUserList)) {
            return new ArrayList<>();
        }

        return customerUserList;
    }

    /**
     * 根据手机号码或者邮箱查询用户编码
     *
     * @param model 手机号码
     * @param email 电子邮箱
     * @return List<String>
     */
    @Override
    public List<String> getUserCodeByModelOrEmail(String model, String email) {
        logger.info("start get user code by account.");

        // 设置返回默认用户
        List<String> codes = new ArrayList<>();
        codes.add(FaDocMarketingCmsConstant.ADMIN_STRING);

        // 设置请求参数
        SearchUserRequestVO searchUserRequestVO = SearchUserRequestVO
                .builder()
                .email(email)
                .mobile(model)
                .build();
        // 请求接口
        List<UserSearchResponseVO> userSearchResponse = UserApiUtil.searchByMobileAndEmail(searchUserRequestVO);
        logger.info("get user code by account success userSearchResponse:{}", userSearchResponse);

        // 组合要添加的用户编码
        if (CollUtil.isNotEmpty(userSearchResponse)) {
            codes.addAll(userSearchResponse.stream().map(UserSearchResponseVO::getUserCode).collect(Collectors.toList()));
        }

        return codes;
    }


    /**
     * 根据企业编码获取企业信息
     *
     * @param companyCode 企业编码列表
     * @return Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO>
     */
    @Override
    public Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> getCompanyAndMerchandiserAndSalesmanInfo(List<String> companyCode) {

        logger.info("start get company and merchandiser and salesman info data for user service.");

        //设置请求参数
        CompanyAndMerchandiserAndSalesmanInfoRequestVO requestVO = CompanyAndMerchandiserAndSalesmanInfoRequestVO.builder()
                .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .companyCodeList(companyCode).build();

        List<CompanyAndMerchandiserAndSalesmanInfoResponseVO> businessResponse = UserApiUtil.companyAndMerchandiserAndSalesmanInfo(requestVO);

        //將businessResponse转成Map
        Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(businessResponse)) {
            companyInfoMap.putAll(businessResponse
                    .stream()
                    .collect(Collectors
                            .toMap(CompanyAndMerchandiserAndSalesmanInfoResponseVO::getCompanyCode,
                                    Function.identity(),
                                    (a, b) -> a)
                    )
            );
        }

        return companyInfoMap;
    }

    /**
     * 根据收货地址id获取收货地址完整信息
     *
     * @param addressId   地址id
     * @param userCode    用户编码
     * @param addressType 地址类型 invoice(发票地址),shipping（收货地址)
     * @return ShippingAddressResponseVO
     */
    @Override
    public ShippingAddressResponseVO getUserAddressByAddressId(String addressId, String addressType, String userCode) {

        logger.info("start get address info by addressId.");

        ShippingAddressRequestVO requestVO = ShippingAddressRequestVO.builder()
                .addressId(addressId)
                .userCode(userCode)
                .type(addressType).build();
        return UserApiUtil.getShippingAddressByUserCodeAndAddressIdMethod(requestVO);

    }


    @Override
    public Map<String, EnquiryLogInfoResponseVO> getEnquiryLogInfo(List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO) {
        logger.info("start get user and company base info data for user service.");

        // 设置请求的用户编码集合
        List<String> userCodeList = userCodeAndCompanyCodeDTO
                .stream()
                .map(UserCodeAndCompanyCodeDTO::getUserCode)
                .collect(Collectors.toList());
        userCodeList.addAll(userCodeAndCompanyCodeDTO
                .stream()
                .map(UserCodeAndCompanyCodeDTO::getPurchaseUserCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList()));
        List<String> userCode = userCodeList.stream().distinct().collect(Collectors.toList());
        // 设置请求的企业编码集合
        List<String> companyCode = userCodeAndCompanyCodeDTO
                .stream()
                .map(UserCodeAndCompanyCodeDTO::getCompanyCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        // 设置入参用户对应的企业
        Map<String, String> userCompanyCodeMap = userCodeAndCompanyCodeDTO.stream().filter(userCodeAndCompanyCode -> StringUtils.isNotBlank(userCodeAndCompanyCode.getCompanyCode())).collect(Collectors.toMap(UserCodeAndCompanyCodeDTO::getUserCode, UserCodeAndCompanyCodeDTO::getCompanyCode, (a, b) -> a));

        // 设置请求参数
        UserAndCompanyAndMerchandiserRequestVO userAndCompanyAndMerchandiserRequestVO = UserAndCompanyAndMerchandiserRequestVO
                .builder()
                .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .userCodeList(userCode)
                .build();
        // 请求
        List<EnquiryLogInfoResponseVO> enquiryLogInfoResponseVOList = UserApiUtil.getEnquiryLogInfo(userAndCompanyAndMerchandiserRequestVO);

        // 组装一个代码对应一个对象
        Map<String, EnquiryLogInfoResponseVO> userInfo = new HashMap<>();
        if (CollUtil.isNotEmpty(enquiryLogInfoResponseVOList)) {
            userInfo.putAll(enquiryLogInfoResponseVOList
                    .stream()
                    .collect(Collectors
                            .toMap(EnquiryLogInfoResponseVO::getUserCode,
                                    Function.identity(),
                                    (a, b) -> a)
                    )
            );
        }

        return userInfo;
    }
}
