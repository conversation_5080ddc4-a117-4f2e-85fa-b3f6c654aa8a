package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: HelpUserCancelOrderBasicsResponseVO.java, v 0.1 2023/3/1 16:20 JiangYuHong Exp $
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HelpUserCancelOrderBasicsResponseVO extends BaseVO {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单明细id
     */
    private int sortId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣后含税单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总金额
     */
    private BigDecimal totalPrice;

    /**
     * 明细状态  unpaid 待支付、untreated 待确认、confirm 已确认、stockUp 备货中、takeDelivered 待收货、finish 已完成、cancelling 取消中、cancel 已取消、closed 已关闭
     */
    private String orderDetailStatus;

    /**
     * 状态颜色
     */
    private String orderDetailStatusColor;
}
