package com.yhd.fa.marketing.cms.util;

import com.yhd.common.util.LogUtils;
import io.seata.core.exception.TransactionException;
import io.seata.core.model.TransactionManager;
import io.seata.tm.TransactionManagerHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

/**
 * <AUTHOR>
 * @version Id: SeataUtil.java, v0.1 2022/8/23 19:50 yehuasheng Exp $
 */
public class SeaTaUtil {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SeaTaUtil.class.getName());

    private SeaTaUtil() {
    }

    /**
     * 回滚
     *
     * @param xid seata进程id
     */
    public static void rollback(String xid) {
        logger.info("seata error rollback. xid:{}", xid);

        if (StringUtils.isNotBlank(xid)) {
            try {
                TransactionManager transactionManager = TransactionManagerHolder.get();
                transactionManager.rollback(xid);
            } catch (TransactionException transactionException) {
                logger.error("seata rollback fail. transactionException errorMsg:{} xid:{}", transactionException.getMessage(), xid);
            } catch (Exception e) {
                logger.error("seata rollback fail. Exception errorMsg:{}", e.getMessage());
            }
        } else {
            logger.error("seata not xid.");
        }
    }
}
