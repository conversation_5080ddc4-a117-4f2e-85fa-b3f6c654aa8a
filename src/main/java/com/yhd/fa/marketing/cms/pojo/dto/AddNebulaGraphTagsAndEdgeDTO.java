package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: AddNebulaGraphTagsAndEdgeRequestVO.java, v0.1 2023/2/4 8:13 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddNebulaGraphTagsAndEdgeDTO extends BaseDTO {
    /**
     * 开始点的名称tag
     */
    private String startTagName;

    /**
     * 开始点的数据
     */
    private String startTagJsonData;

    /**
     * 结束点的名称tag
     */
    private String endTagName;

    /**
     * 结束点的数据
     */
    private String endTagJsonData;

    /**
     * 边的名称
     */
    private String edgeName;

    /**
     * 边的数据
     */
    private String edgeJsonData;
}
