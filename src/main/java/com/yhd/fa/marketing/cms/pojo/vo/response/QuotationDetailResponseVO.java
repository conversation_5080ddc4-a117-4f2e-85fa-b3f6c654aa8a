package com.yhd.fa.marketing.cms.pojo.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: QuotationDetailResponseVO.java, v0.1 2022/12/6 10:11 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuotationDetailResponseVO extends QuotationInfoResponseVO {
    /**
     * 商品详情
     */
    @Schema(description = "商品详情")
    private List<QuotationGoodsDetailResponseVO> goodsDetail;
}
