package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version Id: QuotationPO.java, v0.1 2023/2/2 11:13 yehuasheng Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddNebulaGraphQuotationDTO extends BaseDTO {
    /**
     * id
     */
    private String id;

    /**
     * 报价单号
     */
    private String quotationNumber;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 创建时间
     */
    private Date createdDate;
}
