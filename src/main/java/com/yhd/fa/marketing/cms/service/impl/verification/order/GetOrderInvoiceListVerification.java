package com.yhd.fa.marketing.cms.service.impl.verification.order;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderInvoiceRequestVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: GetOrderInvoiceListVerification.java, v0.1 2023/2/23 10:21 yehuasheng Exp $
 */
@Component
public class GetOrderInvoiceListVerification {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(GetOrderInvoiceListVerification.class.getName());

    /**
     * 检查订单发票参数
     *
     * @param orderInvoiceRequestVO 发票参数
     * @param <T>                   T
     * @return BusinessResponse
     */
    public <T> BusinessResponse<T> check(OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("start check order invoice list parameter.");

        // 校验时间是否符合
        boolean checkDateTime = ObjectUtil.isNotNull(orderInvoiceRequestVO.getEndDateTime()) && ObjectUtil.isNotNull(orderInvoiceRequestVO.getStartDateTime()) && orderInvoiceRequestVO.getStartDateTime().isAfter(orderInvoiceRequestVO.getEndDateTime());
        if (checkDateTime) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.START_DATE_IS_AFTER_END_TIME_ERROR);
        }

        return BusinessResponse.ok(null);
    }
}
