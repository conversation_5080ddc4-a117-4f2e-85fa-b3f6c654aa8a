package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderInvoiceDeliveryStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceDeliveryStatusEnum.java, v0.1 2023/2/23 19:53 yehuasheng Exp $
 */
@Getter
public enum OrderInvoiceDeliveryStatusEnum {
    SHIPPED(OrderInvoiceDeliveryStatusConstant.SHIPPED, "已发货"),
    UNSHIPPED(OrderInvoiceDeliveryStatusConstant.UNSHIPPED, "未发货"),
    ;

    private final String orderInvoiceDeliveryStatus;
    private final String orderInvoiceDeliveryStatusName;

    OrderInvoiceDeliveryStatusEnum(String orderInvoiceDeliveryStatus, String orderInvoiceDeliveryStatusName) {
        this.orderInvoiceDeliveryStatus = orderInvoiceDeliveryStatus;
        this.orderInvoiceDeliveryStatusName = orderInvoiceDeliveryStatusName;
    }
}
