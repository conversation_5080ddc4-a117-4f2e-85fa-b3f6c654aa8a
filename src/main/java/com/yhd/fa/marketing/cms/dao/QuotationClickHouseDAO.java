package com.yhd.fa.marketing.cms.dao;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationClickHousePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @version Id: QuotationClickHouseDAO.java, v 0.1 2025/4/25 15:22 JiangYuHong Exp $
 */
@DS("ch")
public interface QuotationClickHouseDAO extends MPJBaseMapper<QuotationClickHousePO> {

    @Select("<script>" +
            "SELECT count(0) FROM (" +
            "${fullSql} " +
            ") table_count;" +
            "</script>")
    Integer countOrderList(@Param("fullSql") String fullSql);
}
