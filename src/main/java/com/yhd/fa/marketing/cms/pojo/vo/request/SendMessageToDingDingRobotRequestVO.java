package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: SendMessageToDingDingRobotRequestVO.java, v0.1 2023/3/20 14:20 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendMessageToDingDingRobotRequestVO extends BaseVO {
    /**
     * 模板id
     */
    private String templateId;

    /**
     * 要@的手机号码对象
     */
    private List<String> receivers;

    /**
     * 是否@所有人：true是，false否
     */
    private String atAll;

    /**
     * 数据
     */
    private transient Map<String, Object> map = new HashMap<>();
}
