package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: OrderLogisticsDetailsResponseVO.java, v0.1 2023/2/20 10:15 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderLogisticsDetailsResponseVO extends BaseVO {
    /***
     * 订单物流明细id
     */
    @Schema(description = "订单物流明细id", example = "000326504fb64693b321950aa44a6b2e")
    private String orderLogisticsDetailId;

    /**
     * 订单物流id
     */
    @Schema(description = "订单物流id", example = "00011567bede4e82ba96a029d364ec3c")
    private String orderLogisticsId;

    /**
     * 订单序号
     */
    @Schema(description = "订单序号", example = "1")
    private int orderSort;

    /**
     * 产品型号
     */
    @Schema(description = "产品型号", example = "SAD01-D3-L100")
    private String productModel;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "脚杯")
    private String productName;

    /**
     * 发货数量
     */
    @Schema(description = "发货数量", example = "1")
    private long quantity;

    /**
     * 总数量
     */
    @Schema(description = "总数量", example = "1")
    private long totalQuantity;
}
