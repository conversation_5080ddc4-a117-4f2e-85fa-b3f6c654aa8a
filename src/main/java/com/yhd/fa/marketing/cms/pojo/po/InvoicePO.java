package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: InvoicePO.java, v 0.1 2022/11/29 10:03 JiangYuHong Exp $
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("fa_invoice")
@EqualsAndHashCode(callSuper = true)
public class InvoicePO extends BaseEntity {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * erp编码
     */
    private String erpCode;

    /**
     * 发票类型 general 普通 dedicated 专用
     */
    private String invoiceType;

    /**
     * 发票抬头
     */
    private String companyName;

    /**
     * 发票税务登记号
     */
    private String taxRegNo;

    /**
     * 发票公司地址
     */
    private String companyAddress;

    /**
     * 发票公司联系电话
     */
    private String contactNumber;

    /**
     * 发票公司开户银行账号
     */
    private String bankNumber;

    /**
     * 发票公司开户银行名称
     */
    private String bankName;

    /**
     * 审核状态 checkPending 待审核 pass 通过
     */
    private String invoiceStatus;

    /**
     * 一般纳税人证明
     */
    private String taxCertificate;

    /**
     * 营业执照 URL
     */
    private String businessLicense;

    /**
     * 审核不通过拒绝理由
     */
    private String refuseReason;

    /**
     * 发票信息申请变更单据号
     */
    private String changeOrderNumber;

    /**
     * 同步状态
     */
    private String syncStatus;

    /**
     * 同步时间
     */
    private LocalDateTime syncDate;

    /**
     * 同步信息
     */
    private String syncMsg;

}