package com.yhd.fa.marketing.cms.web.order;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderInvoiceRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderInvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceController.java, v0.1 2023/2/23 8:28 yehuasheng Exp $
 */
@Tag(name = "订单发票接口", description = "发票接口包括：发票列表、发票详情、重新同步发票")
@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
public class OrderInvoiceController {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderInvoiceController.class.getName());

    /**
     * 发票服务
     */
    @Resource
    private OrderInvoiceService orderInvoiceService;

    /**
     * 订单发票列表
     *
     * @param orderInvoiceRequestVO 订单发票列表请求参数
     * @return BusinessResponse<PageInfo < OrderInvoiceResponseVO>>
     */
    @Operation(summary = "订单发票列表")
    @PostMapping(value = UriConstant.ORDER_INVOICE_LIST, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<PageInfo<OrderInvoiceResponseVO>> orderInvoiceList(@RequestBody @Validated OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("request get order invoice list api parameter orderInvoiceRequestVO:{}", orderInvoiceRequestVO);

        // 执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<PageInfo<OrderInvoiceResponseVO>> businessResponse = orderInvoiceService.getOrderInvoiceList(orderInvoiceRequestVO);
        logger.info("response get order invoice list api result businessResponse:{}", businessResponse);
        logger.info("get order invoice list api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 订单发票详情页
     *
     * @param orderInvoiceId 订单发票id
     * @return BusinessResponse<OrderInvoiceDetailResponseVO>
     */
    @Operation(summary = "订单发票详情页", parameters = {@Parameter(name = "orderInvoiceId", description = "订单发票id", example = "b7e99891b00c4d718938ce22b83cdda0", required = true)})
    @GetMapping(value = UriConstant.ORDER_INVOICE_DETAIL)
    public BusinessResponse<OrderInvoiceDetailResponseVO> orderInvoiceInfo(@RequestParam String orderInvoiceId) {
        logger.info("request get order invoice detail api parameter orderInvoiceId:{}", orderInvoiceId);

        // 执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<OrderInvoiceDetailResponseVO> businessResponse = orderInvoiceService.getOrderInvoiceInfo(orderInvoiceId);
        logger.info("response get order invoice detail api result businessResponse:{}", businessResponse);
        logger.info("get order invoice detail api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 同步订单发票
     *
     * @param orderInvoiceId 订单发票id
     * @return BusinessResponse<Void>
     */
    @Operation(summary = "同步订单发票", parameters = {@Parameter(name = "orderInvoiceId", description = "订单发票id", example = "5012551d99714d3e93c6f649aa87dd1d", required = true)})
    @GetMapping(value = UriConstant.SYNCHRONIZATION_ORDER_INVOICE)
    public BusinessResponse<Void> synchronizationOrderInvoice(@RequestParam String orderInvoiceId) {
        logger.info("request get synchronization order invoice api parameter orderInvoiceId:{}", orderInvoiceId);

        // 执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<Void> businessResponse = orderInvoiceService.synchronizationOrderInvoice(orderInvoiceId);
        logger.info("response get synchronization order invoice api result businessResponse:{}", businessResponse);
        logger.info("get synchronization order invoice api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }
}
