package com.yhd.fa.marketing.cms.web.order;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderAfterSaleRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderAfterSaleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleController.java, v0.1 2023/2/23 20:32 yehuasheng Exp $
 */
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
@RestController
@Tag(name = "订单售后接口", description = "订单售后接口包括，售后列表 售后详情 再次同步售后")
public class OrderAfterSaleController {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderAfterSaleController.class.getName());

    /**
     * 订单售后服务
     */
    @Resource
    private OrderAfterSaleService orderAfterSaleService;

    /**
     * 订单售后列表
     *
     * @param orderAfterSaleRequestVO 订单售后请求参数
     * @return BusinessResponse<PageInfo < OrderAfterSaleResponseVO>>
     */
    @Operation(summary = "订单售后列表")
    @PostMapping(value = UriConstant.ORDER_AFTER_SALE_LIST, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<PageInfo<OrderAfterSaleResponseVO>> orderAfterSaleList(@RequestBody @Validated OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("request get order after sale list api. parameter orderAfterSaleRequestVO:{}", orderAfterSaleRequestVO);

        // 开始时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<PageInfo<OrderAfterSaleResponseVO>> businessResponse = orderAfterSaleService.getOrderAfterSaleList(orderAfterSaleRequestVO);
        logger.info("response get order after sale list api result businessResponse:{}", businessResponse);
        logger.info("response get order after sale list api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 订单售后详情页
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<OrderAfterSaleDetailResponseVO>
     */
    @Operation(summary = "订单售后详情", parameters = {
            @Parameter(
                    name = "orderAfterSaleId",
                    description = "订单售后id",
                    example = "44b07af2969d49d4bc8c02baf77f1d39",
                    required = true)})
    @GetMapping(value = UriConstant.ORDER_AFTER_SALE_DETAIL)
    public BusinessResponse<OrderAfterSaleDetailResponseVO> orderAfterSaleInfo(@RequestParam String orderAfterSaleId) {
        logger.info("request get order after sale detail api. parameter orderAfterSaleId:{}", orderAfterSaleId);

        // 开始时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<OrderAfterSaleDetailResponseVO> businessResponse = orderAfterSaleService.getOrderAfterSaleInfo(orderAfterSaleId);
        logger.info("response get order after sale detail api result businessResponse:{}", businessResponse);
        logger.info("response get order after sale detail api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 同步订单售后
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<Void>
     */
    @Operation(summary = "同步订单售后", parameters = {
            @Parameter(
                    name = "orderAfterSaleId",
                    description = "订单售后id",
                    example = "44b07af2969d49d4bc8c02baf77f1d39",
                    required = true)})
    @GetMapping(value = UriConstant.SYNCHRONIZATION_ORDER_AFTER_SALE)
    public BusinessResponse<Void> synchronizationOrderAfterSale(@RequestParam String orderAfterSaleId) {
        logger.info("request get synchronization order after sale api parameter orderAfterSaleId:{}", orderAfterSaleId);

        // 执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<Void> businessResponse = orderAfterSaleService.synchronizationOrderAfterSale(orderAfterSaleId);
        logger.info("response get synchronization order after sale api result businessResponse:{}", businessResponse);
        logger.info("get synchronization order after sale api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }
}
