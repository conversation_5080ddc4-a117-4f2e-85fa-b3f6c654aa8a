package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version Id: OrderCommentsCategoriesRelPO.java, v 0.1 2025/5/19 17:22 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("fa_order_comments_categories_rel")
public class OrderCommentsCategoriesRelPO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 评论ID
     */
    private String commentId;

    /**
     * 问题分类ID
     */
    private Integer categoryId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private LocalDateTime updatedDate;
}