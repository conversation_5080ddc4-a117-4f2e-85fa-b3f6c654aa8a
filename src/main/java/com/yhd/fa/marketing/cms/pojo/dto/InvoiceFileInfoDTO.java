package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: InvoiceFileInfoDTO.java, v 0.1 2024/6/17 下午5:27 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InvoiceFileInfoDTO extends BaseDTO {

    /**
     * 发票号
     */
    @Schema(description = "发票号", example = "123456789")
    private String invoiceNo;

    /**
     * 文件信息
     */
    @Schema(description = "文件信息", example = "123456789")
    private List<InvoiceFileDTO> file;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    private static class InvoiceFileDTO extends BaseDTO{

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "123456789")
        private String fileName;

        /**
         * 文件类型 1:ofd 2:pdf 3:xml
         */
        @Schema(description = "文件类型  1:ofd 2:pdf 3:xml", example = "1")
        private Integer fileType;

        /**
         * 下载地址
         */
        @Schema(description = "下载地址", example = "http://www.baidu.com")
        private String url;

    }
}
