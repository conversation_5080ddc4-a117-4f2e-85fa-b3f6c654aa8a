package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionCategoryRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationCategoryResponseVO;
import com.yhd.fa.marketing.cms.service.sao.ProductCenterCmsService;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: GetApproveQuotationCategoryLogic.java, v0.1 2022/12/19 11:20 yehuasheng Exp $
 */
@Component
public class GetApproveQuotationCategoryLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetApproveQuotationCategoryLogic.class.getName());

    /**
     * 产品中心的服务
     */
    @Resource
    private ProductCenterCmsService productCenterCmsService;

    /**
     * 获取产品的类别
     *
     * @param approveQuotationSelectionCategoryRequestVO 请求获取产品类别的参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    public BusinessResponse<List<ApproveQuotationCategoryResponseVO>> exec(ApproveQuotationSelectionCategoryRequestVO approveQuotationSelectionCategoryRequestVO) {
        logger.info("exec approve quotation get product category code.");

        // 调用服务
        return productCenterCmsService.getProductCategoryCode(approveQuotationSelectionCategoryRequestVO);
    }
}
