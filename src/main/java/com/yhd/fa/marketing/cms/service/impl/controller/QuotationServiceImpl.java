package com.yhd.fa.marketing.cms.service.impl.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.*;
import com.yhd.fa.marketing.cms.service.controller.QuotationService;
import com.yhd.fa.marketing.cms.service.impl.logic.quotation.*;
import com.yhd.fa.marketing.cms.service.impl.verification.quotation.*;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: QuotationServiceImpl.java, v0.1 2022/12/2 11:07 yehuasheng Exp $
 */
@Service
public class QuotationServiceImpl implements QuotationService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(QuotationServiceImpl.class.getName());

    /**
     * 获取报价单列表的校验服务
     */
    @Resource
    private GetQuotationListVerification getQuotationListVerification;

    /**
     * 获取报价单列表的逻辑
     */
    @Resource
    private GetQuotationListLogic getQuotationListLogic;

    /**
     * 获取报价单详情的校验
     */
    @Resource
    private GetQuotationDetailVerification getQuotationDetailVerification;

    /**
     * 获取报价单详情逻辑
     */
    @Resource
    private GetQuotationDetailLogic getQuotationDetailLogic;

    /**
     * 获取审核报价单列表逻辑
     */
    @Resource
    private GetApproveQuotationListLogic getApproveQuotationListLogic;

    /**
     * 检查审核报价单详情
     */
    @Resource
    private GetApproveQuotationDetailVerification getApproveQuotationDetailVerification;

    /**
     * 获取审核报价单详情的逻辑
     */
    @Resource
    private GetApproveQuotationDetailLogic getApproveQuotationDetailLogic;

    /**
     * 获取产品分类的服务
     */
    @Resource
    private GetApproveQuotationTypeLogic getApproveQuotationTypeLogic;

    /**
     * 获取产品类别的服务
     */
    @Resource
    private GetApproveQuotationCategoryLogic getApproveQuotationCategoryLogic;

    /**
     * 检查客户型号逻辑
     */
    @Resource
    private CheckCustomerModelLogic checkCustomerModelLogic;

    /**
     * 校验报价待审核报价单
     */
    @Resource
    private SaveApproveQuotationVerification saveApproveQuotationVerification;

    /**
     * 报价单审核逻辑
     */
    @Resource
    private SaveApproveQuotationLogic saveApproveQuotationLogic;

    /**
     * 获取企业列表逻辑
     */
    @Resource
    private GetCustomerListLogic getCustomerListLogic;

    /**
     * 检查创建报价单
     */
    @Resource
    private CreateQuotationVerification createQuotationVerification;

    /**
     * 创建报价单逻辑
     */
    @Resource
    private CreateQuotationLogic createQuotationLogic;

    /**
     * 同步报价单逻辑
     */
    @Resource
    private SynchronizationQuotationLogic synchronizationQuotationLogic;

    /**
     * 获取企业列表校验企业名称参数
     */
    @Resource
    private GetCompanyListByCompanyNameVerification getCompanyListByCompanyNameVerification;

    /**
     * 根据企业名称获取企业列表
     */
    @Resource
    private GetCompanyListByCompanyNameLogic getCompanyListByCompanyNameLogic;

    /**
     * 获取用户列表逻辑
     */
    @Resource
    private GetUserListByCompanyCodeLogic getUserListByCompanyCodeLogic;

    /**
     * 获取报价单列表
     *
     * @param quotationInfoRequestVO 获取报价单列表的参数
     * @return BusinessResponse<PageInfo < QuotationInfoResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<QuotationInfoResponseVO>> getQuotationList(QuotationInfoRequestVO quotationInfoRequestVO) {
        logger.info("start exec get quotation list.");

        // 校验参数
        BusinessResponse<PageInfo<QuotationInfoResponseVO>> checkParameter = getQuotationListVerification.check(quotationInfoRequestVO);
        if (!checkParameter.success()) {
            return checkParameter;
        }

        // 执行逻辑
        return getQuotationListLogic.exec(quotationInfoRequestVO);
    }

    /**
     * 获取报价单详情
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<QuotationDetailResponseVO>
     */
    @Override
    public BusinessResponse<QuotationDetailResponseVO> getQuotationDetail(String quotationId) {
        logger.info("start exec get quotation detail.");

        // 校验报价单id
        BusinessResponse<QuotationDetailResponseVO> check = getQuotationDetailVerification.check(quotationId);
        if (!check.success()) {
            return check;
        }

        // 进行逻辑
        return getQuotationDetailLogic.exec(check.getData());
    }

    /**
     * 获取审核报价单列表
     *
     * @param approveQuotationRequestVO 审核报价单参数
     * @return BusinessResponse<PageInfo < ApproveQuotationResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<ApproveQuotationResponseVO>> getApproveQuotationList(ApproveQuotationRequestVO approveQuotationRequestVO) {
        logger.info("start get approve quotation list service.");

        // 进行逻辑
        return getApproveQuotationListLogic.exec(approveQuotationRequestVO);
    }

    /**
     * 获取审核报价单详情
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<ApproveQuotationResponseVO>
     */
    @Override
    public BusinessResponse<ApproveQuotationDetailResponseVO> getApproveQuotationDetail(String quotationId) {
        logger.info("start get approve quotation detail service.");

        // 检查审核报价单详情
        BusinessResponse<QuotationPO> check = getApproveQuotationDetailVerification.check(quotationId);
        if (!check.success()) {
            return BusinessResponseCommon.fail(check.getRt_code(), check.getRt_msg());
        }

        // 设置报价单详情
        return getApproveQuotationDetailLogic.exec(check.getData());
    }

    /**
     * 获取审核报价单明细中型号的类别编码
     *
     * @param approveQuotationSelectionTypeRequestVO 请求获取类别参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    @Override
    public BusinessResponse<List<ApproveQuotationTypeResponseVO>> getApproveQuotationType(ApproveQuotationSelectionTypeRequestVO approveQuotationSelectionTypeRequestVO) {
        logger.info("start get approve quotation type service.");

        // 执行逻辑
        return getApproveQuotationTypeLogic.exec(approveQuotationSelectionTypeRequestVO);
    }

    /**
     * 获取审核报价单明细中型号的类别编码
     *
     * @param approveQuotationSelectionCategoryRequestVO 请求获取类别参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    @Override
    public BusinessResponse<List<ApproveQuotationCategoryResponseVO>> getApproveQuotationCategory(ApproveQuotationSelectionCategoryRequestVO approveQuotationSelectionCategoryRequestVO) {
        logger.info("start get approve quotation category service.");

        // 执行逻辑
        return getApproveQuotationCategoryLogic.exec(approveQuotationSelectionCategoryRequestVO);
    }

    /**
     * 检查客户型号价格和交期
     *
     * @param checkApproveQuotationParameterRequestVO 请求参数
     * @return BusinessResponse<List < QuotationResponseVO>>
     */
    @Override
    public BusinessResponse<List<QuotationResponseVO>> checkCustomerModel(CheckApproveQuotationParameterRequestVO checkApproveQuotationParameterRequestVO) {
        logger.info("start check customer model service.");

        // 执行逻辑
        return checkCustomerModelLogic.exec(checkApproveQuotationParameterRequestVO);
    }

    /**
     * 保存待审核报价单
     *
     * @param approveQuotationRequestVO 待审核报价单参数
     * @return BusinessResponse<Object>
     */
    @Override
    public BusinessResponse<Object> saveApproveQuotation(SaveApproveQuotationRequestVO approveQuotationRequestVO) {
        logger.info("start save approve quotation service.");

        // 检查参数
        BusinessResponse<QuotationPO> check = saveApproveQuotationVerification.check(approveQuotationRequestVO);
        if (!check.success()) {
            return BusinessResponseCommon.fail(check.getRt_code(), check.getRt_msg());
        }

        // 进行逻辑
        return saveApproveQuotationLogic.exec(approveQuotationRequestVO, check.getData());
    }

    /**
     * 获取客户信息列表
     *
     * @param customerListRequestVO 请求获取客户信息参数
     * @return BusinessResponse<PageInfo < CustomerListResponseVO>>
     */
    @Override
    public BusinessResponse<List<CustomerCompanyListResponseVO>> getCustomerList(CustomerListRequestVO customerListRequestVO) {
        logger.info("start get customer list service.");

        // 进行逻辑
        return getCustomerListLogic.exec(customerListRequestVO);
    }

    /**
     * 创建报价单
     *
     * @param createQuotationRequestVO 创建报价单参数
     * @return BusinessResponse<String>
     */
    @Override
    public BusinessResponse<String> createQuotation(CreateQuotationRequestVO createQuotationRequestVO) {
        logger.info("start create quotation service.");

        // 校验参数
        BusinessResponse<CompanyAndUserAndMerchandiserAndResourcesResponseVO> check = createQuotationVerification.check(createQuotationRequestVO);
        if (!check.success()) {
            return BusinessResponseCommon.fail(check.getRt_code(), check.getRt_msg());
        }

        // 创建报价单
        return createQuotationLogic.exec(check.getData(), createQuotationRequestVO);
    }

    /**
     * 同步报价单
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<Void> synchronizationQuotation(String quotationId) {
        logger.info("start synchronization quotation service.");

        // 执行同步逻辑
        return synchronizationQuotationLogic.exec(quotationId);
    }

    /**
     * 获取企业列表
     *
     * @param companyNameRequestVO 企业名称
     * @return BusinessResponse<List < CustomerUserResponseVO>>
     */
    @Override
    public BusinessResponse<List<CustomerCompanyListResponseVO>> getCompanyListByCompanyName(CompanyNameRequestVO companyNameRequestVO) {
        logger.info("start get company list by company name.");

        // 校验参数
        BusinessResponse<List<CustomerCompanyListResponseVO>> check = getCompanyListByCompanyNameVerification.check(companyNameRequestVO.getCompanyName());
        if (!check.success()) {
            return check;
        }

        // 进行逻辑
        return getCompanyListByCompanyNameLogic.exec(companyNameRequestVO.getCompanyName());
    }

    /**
     * 根据企业编码获取用户列表
     *
     * @param userListByCompanyCodeRequestVO 企业编码请求参数
     * @return BusinessResponse<List < CustomerUserResponseVO>>
     */
    @Override
    public BusinessResponse<List<CustomerUserResponseVO>> getUserListByCompanyCode(UserListByCompanyCodeRequestVO userListByCompanyCodeRequestVO) {
        logger.info("start get user list by company code.");

        // 进行逻辑
        return getUserListByCompanyCodeLogic.exec(userListByCompanyCodeRequestVO.getCompanyCode());
    }
}
