package com.yhd.fa.marketing.cms.service.impl.sao;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.ReturnCouponRequestVO;
import com.yhd.fa.marketing.cms.sao.FaMarketingSystemSAO;
import com.yhd.fa.marketing.cms.service.sao.FaMarketingSystemService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: FaMarketingSystemServiceImpl.java, v 0.1 2024/8/13 10:22 JiangYuHong Exp $
 */
@Service
public class FaMarketingSystemServiceImpl implements FaMarketingSystemService {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(FaMarketingSystemServiceImpl.class.getName());

    @Resource
    private FaMarketingSystemSAO faMarketingSystemSAO;

    /**
     * 退还优惠券
     *
     * @param requestVO 请求参数
     * @return businessResponse
     */
    @Override
    public BusinessResponse<Object> returnCoupon(ReturnCouponRequestVO requestVO) {

        logger.info("start request return coupon api. parameter:{}", requestVO);

        BusinessResponse<Object> businessResponse = new BusinessResponse<>();
        try {
            businessResponse = faMarketingSystemSAO.returnCoupon(requestVO);

            logger.info("request return coupon api. businessResponse:{}", businessResponse);
        } catch (Exception e) {
            logger.error("request return coupon api. exception:{}", e.toString());
        }

        return businessResponse;
    }
}
