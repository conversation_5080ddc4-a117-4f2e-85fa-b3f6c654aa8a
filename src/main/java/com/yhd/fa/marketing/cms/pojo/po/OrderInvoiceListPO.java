package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_invoice_list")
public class OrderInvoiceListPO extends BaseEntity {
    /**
     * 索取发票id
     */
    private String orderInvoiceId;

    /**
     * 索取发票的订单id
     */
    private String orderId;

    /**
     * 索取发票的订单号
     */
    private String orderNumber;

    /**
     * 索取发票的订单金额
     */
    private BigDecimal payablePrice;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreatedDate;
}

