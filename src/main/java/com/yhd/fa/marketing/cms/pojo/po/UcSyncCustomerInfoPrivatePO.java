package com.yhd.fa.marketing.cms.pojo.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 客户资料私有表--用户中心
 *
 * <AUTHOR>
 * @version Id: UcSyncCustomerInfoPrivatePO.java, v 0.1 2025/5/7 14:48 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "uc_sync_customer_info_private")
@EqualsAndHashCode(callSuper = true)
public class UcSyncCustomerInfoPrivatePO extends BaseEntity {

    /**
     * 客户编号
     */
    @TableField(value = "customer_no")
    private String customerNo;

    /**
     * 子公司标识 东莞010000,怡惠购040000,苏州050000,工品070000
     */
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 逻辑删除 0正常，1删除
     */
    @TableField(value = "is_delete")
    @TableLogic
    private Integer isDelete;

    /**
     * 客户名称"
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 业务员工号
     */
    @TableField(value = "sales_no")
    private String salesNo;

    /**
     * 业务员名称
     */
    @TableField(value = "sales_name")
    private String salesName;

    /**
     * 客户状态 未分配1  已分配2 已申请3 黑明单4
     */
    @TableField(value = "customer_status")
    private Integer customerStatus;

    /**
     * 付款方式 1现金 2电汇 3当期支票 4超期支票 5银行汇款 6企业承兑 7快递代收现金 8业务担保
     */
    @TableField(value = "payment_type")
    private Integer paymentType;

    /**
     * 异常类型：正常下单0 ,暂停下单1
     */
    @TableField(value = "exception_type")
    private Integer exceptionType;

    /**
     * 交易状况 正常交易1 暂停交易2
     */
    @TableField(value = "transaction_status")
    private Integer transactionStatus;

    /**
     * 客户信用额度类型 特级客户0 A+级客户1 A级客户2 B级客户3 C级客户4 D级客户5 E级客户6
     */
    @TableField(value = "credit_quota_type")
    private Integer creditQuotaType;

    /**
     * 客户信用额度金额
     */
    @TableField(value = "credit_limit_amount")
    private BigDecimal creditLimitAmount;

    /**
     * 是否白名单客户 否0 是1
     */
    @TableField(value = "is_white_list_customer")
    private Integer isWhiteListCustomer;

    /**
     * 折扣等级 A-1 B-2 C-3 D-4 B+-5 C+-6 D+-7
     */
    @TableField(value = "discount_level")
    private int discountLevel;

    /**
     * FA内部折扣
     */
    @TableField(value = "fa_internal_discount")
    private BigDecimal faInternalDiscount;

    /**
     * 铝型材内部折扣
     */
    @TableField(value = "profile_internal_discount")
    private BigDecimal profileInternalDiscount;

    /**
     * 是否取历史最低价 是 1/否 0
     */
    @TableField(value = "is_history_low_price")
    private Integer isHistoryLowPrice;

    /**
     * 归属公司 东莞-DGYHD 苏州-SZYHD
     */
    @TableField(value = "ownership_company")
    private Integer ownershipCompany;
}
