package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: CheckApproveQuotationParameterRequestVO.java, v0.1 2022/12/23 10:28 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckApproveQuotationParameterRequestVO extends BaseVO {
    /**
     * 用户编码
     */
    @NotEmpty(message = "用户编码不能为空")
    @Schema(description = "用户编码", example = "12")
    private String userCode;

    /**
     * 报价型号、数量等列表
     */
    @Valid
    @NotNull(message = "列表不能为空")
    @Size(min = 1, max = 2000, message = "列表不能少于1且不能大于2000")
    @Schema(description = "报价型号、数量等列表")
    private List<CheckApproveQuotationParameterModelListRequestVO> list;
}
