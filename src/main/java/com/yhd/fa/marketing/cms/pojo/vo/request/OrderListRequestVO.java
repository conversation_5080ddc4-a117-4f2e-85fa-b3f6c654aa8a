package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderListRequestVO.java, v0.1 2023/2/16 20:12 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderListRequestVO extends PageRequestVO {
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-01-01 10:10:10")
    private LocalDateTime startDateTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-01-01 10:10:10")
    private LocalDateTime endDateTime;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "MYI202211221106059833DEVT")
    @Length(min = 2, message = "订单号至少输入2个字符")
    private String orderNumber;

    /**
     * 企业名称
     */
    @Length(min = 2, message = "企业名称至少输入2个字符")
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态 unpaid 待支付、onWay 在途单、finish 已完成、cancel 已取消、closed已关闭")
    @ValueInEnum(matchTarget = {CommonConstant.EMPTY, OrderStatusConstant.UNPAID, OrderStatusConstant.ON_WAY, OrderStatusConstant.FINISH, OrderStatusConstant.CANCEL, OrderStatusConstant.CLOSED, PaymentStatusConstant.PAYMENT_FAILED}, message = "请选择正确的订单状态类型")
    private String orderStatus;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型 unknown未支付、aliPay支付宝支付、weChatPay微信支付、unionPay企业网银、offlinePay线下支付、monthly信用月结", example = "aliPay")
    @ValueInEnum(matchTarget = {CommonConstant.EMPTY, PaymentTypeConstant.UNKNOWN, PaymentTypeConstant.ALI_PAY, PaymentTypeConstant.WECHAT_PAY, PaymentTypeConstant.OFFLINE_PAY, PaymentTypeConstant.MONTHLY, PaymentTypeConstant.UNION_PAY}, message = "请选择正确的支付类型")
    private String paymentType;

    /**
     * 下单渠道 pc端 wap手机端 weChat微信 app端 applet小程序
     */
    @Schema(description = "下单渠道 pc端 wap手机端 weChat微信 app端 applet小程序", example = "pc")
    @ValueInEnum(matchTarget = {CommonConstant.EMPTY, OrderChannelConstant.PC, OrderChannelConstant.APPLET, OrderChannelConstant.APP, OrderChannelConstant.WAP, OrderChannelConstant.WECHAT}, message = "请选择正确的下单渠道")
    private String orderChannel;

    /**
     * 下单来源 cart
     */
    @Schema(description = "下单来源 cart购物车 buy一键购买 quotation报价单 offlineQuotation线下报价单 mergeQuotation合并报价单 mergeOfflineQuotation线下合并报价单", example = "buy")
    @ValueInEnum(matchTarget = {CommonConstant.EMPTY, OrderSourceConstant.BUY, OrderSourceConstant.CART, OrderSourceConstant.MERGE_OFFLINE_QUOTATION, OrderSourceConstant.MERGE_QUOTATION, OrderSourceConstant.QUOTATION, OrderSourceConstant.OFFLINE_QUOTATION}, message = "请选择正确的下单来源")
    private String orderSource;

}
