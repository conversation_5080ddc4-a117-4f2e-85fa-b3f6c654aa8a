package com.yhd.fa.marketing.cms.web.quotation;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.cms.log.common.annotation.Log;
import com.yhd.cms.log.common.annotation.Tables;
import com.yhd.cms.log.common.enums.LogTypeEnum;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.*;
import com.yhd.fa.marketing.cms.service.controller.QuotationService;
import com.yhd.fa.marketing.cms.util.SeaTaUtil;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: QuotationController.java, v0.1 2022/12/1 17:06 yehuasheng Exp $
 */
@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
@Tag(name = "报价单管理", description = "接口包含报价单列表、报价单审核、创建报价单、查看询价记录")
public class QuotationController {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(QuotationController.class.getName());

    /**
     * 报价单服务
     */
    @Resource
    private QuotationService quotationService;

    /**
     * 获取报价单列表
     *
     * @param quotationInfoRequestVO 获取报价单列表参数
     * @return BusinessResponse<PageInfo < QuotationInfoResponseVO>>
     */
    @Operation(summary = "报价单列表")
    @PostMapping(value = UriConstant.QUOTATION_LIST, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<PageInfo<QuotationInfoResponseVO>> quotationList(@RequestBody @Validated QuotationInfoRequestVO quotationInfoRequestVO) {
        logger.info("request faDoc marketing cms quotation list api parameter quotationInfoRequestVO:{}", quotationInfoRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取报价单列表接口
        BusinessResponse<PageInfo<QuotationInfoResponseVO>> businessResponse = quotationService.getQuotationList(quotationInfoRequestVO);
        logger.info("faDoc marketing cms get quotation list success response result businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms get quotation list cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 获取报价单详情
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<QuotationDetailResponseVO>
     */
    @Operation(summary = "报价单详情", parameters = {@Parameter(name = "quotationId", description = "报价单id", example = "63e2afde1d8e4fefad47213cc43d2499", required = true)})
    @GetMapping(value = UriConstant.QUOTATION_DETAIL)
    public BusinessResponse<QuotationDetailResponseVO> quotationDetail(@RequestParam String quotationId) {
        logger.info("request faDoc marketing cms quotation detail api parameter quotationId:{}", quotationId);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取报价单列表接口
        BusinessResponse<QuotationDetailResponseVO> businessResponse = quotationService.getQuotationDetail(quotationId);
        logger.info("faDoc marketing cms quotation detail success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms quotation detail cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 获取审核报价单列表
     * 已弃用因为生成报价单不需要再审核了，可以直接下去ERP
     *
     * @param approveQuotationRequestVO 审核报价单列表参数
     * @return BusinessResponse<PageInfo < ApproveQuotationResponseVO>>
     */
    @Deprecated
    @Operation(summary = "审核报价单列表 已废弃")
    @PostMapping(value = UriConstant.APPROVE_QUOTATION_LIST, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<PageInfo<ApproveQuotationResponseVO>> approveQuotationList(@RequestBody ApproveQuotationRequestVO approveQuotationRequestVO) {
        logger.info("request faDoc marketing cms approve quotation list api parameter approveQuotationRequestVO:{}", approveQuotationRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取报价单列表接口
        BusinessResponse<PageInfo<ApproveQuotationResponseVO>> businessResponse = quotationService.getApproveQuotationList(approveQuotationRequestVO);
        logger.info("faDoc marketing cms approve quotation list success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms approve quotation list cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 获取审核报价单详情
     * 已弃用因为生成报价单不需要再审核了，可以直接下去ERP
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<ApproveQuotationDetailResponseVO>
     */
    @Deprecated
    @Operation(summary = "审核报价单详情 已废弃", parameters = {@Parameter(name = "quotationId", description = "报价单id", example = "63e2afde1d8e4fefad47213cc43d2499", required = true)})
    @GetMapping(value = UriConstant.APPROVE_QUOTATION_DETAIL)
    public BusinessResponse<ApproveQuotationDetailResponseVO> approveQuotationDetail(@RequestParam String quotationId) {
        logger.info("request faDoc marketing cms approve quotation detail api parameter quotationId:{}", quotationId);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取审核报价详情单接口
        BusinessResponse<ApproveQuotationDetailResponseVO> businessResponse = quotationService.getApproveQuotationDetail(quotationId);
        logger.info("faDoc marketing cms approve quotation detail success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms approve quotation detail cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 获取待审核报价单的产品分类、代码
     * 已弃用因为生成报价单不需要再审核了，可以直接下去ERP
     *
     * @param approveQuotationSelectionTypeRequestVO 产品分类参数
     * @return BusinessResponse<List < ApproveQuotationTypeResponseVO>>
     */
    @Deprecated
    @Operation(summary = "待审核报价单分类选择 已废弃")
    @PostMapping(value = UriConstant.APPROVE_QUOTATION_SELECTION_TYPE)
    public BusinessResponse<List<ApproveQuotationTypeResponseVO>> approveQuotationSelectionType(@RequestBody ApproveQuotationSelectionTypeRequestVO approveQuotationSelectionTypeRequestVO) {
        logger.info("request faDoc marketing cms approve quotation selection type parameter approveQuotationSelectionTypeRequestVO:{}", approveQuotationSelectionTypeRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取审核报价详情单接口
        BusinessResponse<List<ApproveQuotationTypeResponseVO>> businessResponse = quotationService.getApproveQuotationType(approveQuotationSelectionTypeRequestVO);
        logger.info("faDoc marketing cms approve quotation selection type success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms approve quotation selection type cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 待审核报价单提交结果
     * 已弃用因为生成报价单不需要再审核了，可以直接下去ERP
     *
     * @param approveQuotationRequestVO 修改报价单明细的参数
     * @return BusinessResponse<Object>
     */
    @Deprecated
    @Operation(summary = "待审核报价单提交结果 已废弃")
    @GlobalTransactional(name = "faDocMarketingCmsSaveQuotation")
    @PutMapping(value = UriConstant.SAVE_APPROVE_QUOTATION)
    @Log(value = "提交审核报价单记录", type = LogTypeEnum.UPDATED, table = {@Tables(tableName = "fa_quotation", fields = {@Tables.Field(isAlias = true, alias = "quotationId")})})
    public BusinessResponse<Object> saveApproveQuotation(@RequestBody @Validated SaveApproveQuotationRequestVO approveQuotationRequestVO) {
        logger.info("request faDoc marketing cms save approve quotation api parameter approveQuotationRequestVO:{}", approveQuotationRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        BusinessResponse<Object> businessResponse = quotationService.saveApproveQuotation(approveQuotationRequestVO);
        logger.info("faDoc marketing cms save approve quotation api success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms save approve quotation api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        // 判断是否手动回滚
        if (!businessResponse.success()) {
            // 手动回滚
            SeaTaUtil.rollback(RootContext.getXID());
        }

        return businessResponse;
    }

    /**
     * 待审核报价单择类别编号
     * 已弃用因为生成报价单不需要再审核了，可以直接下去ERP
     *
     * @param approveQuotationSelectionCategoryRequestVO 请求获取类别编码的参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    @Deprecated
    @Operation(summary = "待审核报价单择类别编号 已废弃")
    @PostMapping(value = UriConstant.APPROVE_QUOTATION_SELECTION_CATEGORY_NUMBER)
    public BusinessResponse<List<ApproveQuotationCategoryResponseVO>> approveQuotationSelectionCategory(@RequestBody @Validated ApproveQuotationSelectionCategoryRequestVO approveQuotationSelectionCategoryRequestVO) {
        logger.info("request faDoc marketing cms approve quotation selection category number parameter approveQuotationSelectionCategoryRequestVO:{}.", approveQuotationSelectionCategoryRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取审核报价详情单接口
        BusinessResponse<List<ApproveQuotationCategoryResponseVO>> businessResponse = quotationService.getApproveQuotationCategory(approveQuotationSelectionCategoryRequestVO);
        logger.info("faDoc marketing cms approve quotation selection category number success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms approve quotation selection category number cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 检查待审核报价单客户客户型号询价
     * 已弃用因为生成报价单不需要再审核了，可以直接下去ERP
     *
     * @param checkApproveQuotationParameterRequestVO 询价参数
     * @return BusinessResponse<List < QuotationResponseVO>>
     */
    @Deprecated
    @Operation(summary = "检查待审核报价单客户客户型号询价 已废弃")
    @PostMapping(value = UriConstant.CHECK_APPROVE_QUOTATION_CUSTOMER_MODEL)
    public BusinessResponse<List<QuotationResponseVO>> checkApproveQuotationCustomerModel(@RequestBody @Validated CheckApproveQuotationParameterRequestVO checkApproveQuotationParameterRequestVO) {
        logger.info("request faDoc marketing cms approve quotation check customer model. parameter checkApproveQuotationParameterRequestVO:{}", checkApproveQuotationParameterRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取审核报价接口
        BusinessResponse<List<QuotationResponseVO>> businessResponse = quotationService.checkCustomerModel(checkApproveQuotationParameterRequestVO);
        logger.info("faDoc marketing cms approve quotation check customer model success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms approve quotation check customer model cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 帮客户下单获取企业信息列表
     *
     * @param customerListRequestVO 获取客户企业列表请求参数
     * @return BusinessResponse<PageInfo < CustomerListResponseVO>>
     */
    @Operation(summary = "帮客户下单获取企业信息")
    @PostMapping(value = UriConstant.CUSTOMER_LIST)
    public BusinessResponse<List<CustomerCompanyListResponseVO>> customerList(@RequestBody @Validated CustomerListRequestVO customerListRequestVO) {
        logger.info("request faDoc marketing cms get customer list. parameter customerListRequestVO:{}", customerListRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<List<CustomerCompanyListResponseVO>> businessResponse = quotationService.getCustomerList(customerListRequestVO);
        logger.info("faDoc marketing cms get customer list success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms get customer list cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 根据企业名称获取企业列表
     *
     * @param companyNameRequestVO 企业名称
     * @return public BusinessResponse<List<CustomerUserResponseVO>>
     */
    @Operation(summary = "根据企业名称获取企业列表")
    @PostMapping(value = UriConstant.CUSTOMER_COMPANY)
    public BusinessResponse<List<CustomerCompanyListResponseVO>> customerUserName(@RequestBody @Validated CompanyNameRequestVO companyNameRequestVO) {
        logger.info("request faDoc marketing cms customer company list for company name api companyNameRequestVO:{}", companyNameRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<List<CustomerCompanyListResponseVO>> businessResponse = quotationService.getCompanyListByCompanyName(companyNameRequestVO);
        logger.info("faDoc marketing cms get customer company list for company name success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms get customer company list for company name cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 根据企业编码获取用户列表
     *
     * @param userListByCompanyCodeRequestVO 企业编码请求参数
     * @return BusinessResponse<List < CustomerUserResponseVO>>
     */
    @Operation(summary = "根据企业编码获取用户列表")
    @PostMapping(value = UriConstant.CUSTOMER_USER)
    public BusinessResponse<List<CustomerUserResponseVO>> userList(@RequestBody @Validated UserListByCompanyCodeRequestVO userListByCompanyCodeRequestVO) {
        logger.info("request faDoc marketing cms customer user list for company code api parameter userListByCompanyCodeRequestVO:{}", userListByCompanyCodeRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<List<CustomerUserResponseVO>> businessResponse = quotationService.getUserListByCompanyCode(userListByCompanyCodeRequestVO);
        logger.info("faDoc marketing cms customer user list for company code success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms customer user list for company code cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 创建报价单
     *
     * @param createQuotationRequestVO 创建报价单参数
     * @return BusinessResponse<String>
     */
    @GlobalTransactional(name = "faDocMarketingCmsCreateQuotation", rollbackFor = Exception.class)
    @Operation(summary = "帮客户创建报价单")
    @PostMapping(value = UriConstant.CREATE_QUOTATION)
    public BusinessResponse<String> createQuotation(@RequestBody @Validated CreateQuotationRequestVO createQuotationRequestVO) {
        logger.info("request faDoc marketing cms create quotation api parameter createQuotationRequestVO:{}", createQuotationRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        BusinessResponse<String> businessResponse = quotationService.createQuotation(createQuotationRequestVO);
        logger.info("faDoc marketing cms create quotation api success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms create quotation api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        // 判断是否手动回滚
        if (!businessResponse.success()) {
            // 手动回滚
            SeaTaUtil.rollback(RootContext.getXID());
        }

        return businessResponse;
    }

    /**
     * 重新同步报价单
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<Void>
     */
    @Operation(summary = "重新同步报价单", parameters = {@Parameter(name = "quotationId", description = "报价单id", example = "0aff782a67094454ab697bb7bb69ac2d")})
    @GetMapping(value = UriConstant.SYNCHRONIZATION_QUOTATION)
    public BusinessResponse<Void> synchronizationQuotation(@RequestParam String quotationId) {
        logger.info("request faDoc marketing cms synchronization quotation api parameter quotationId:{}", quotationId);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<Void> businessResponse = quotationService.synchronizationQuotation(quotationId);
        logger.info("faDoc marketing cms synchronization quotation api success response businessResponse:{}", businessResponse);
        logger.info("faDoc marketing cms synchronization quotation api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }
}
