package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleStatusResponseVO.java, v 0.1 2023/11/13 15:07 JiangYuHong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderAfterSaleStatusResponseVO extends BaseVO {


    /**
     * 状态：待审核 pendingAudit；
     * 待寄回商品 pendingReturn；
     * 寄回商品待检测 pendingCheck；
     * 待退款 pendingRefund；
     * 待发货 pendingShip；
     * 待收货 pendingReceive；
     * 待上门维修 pendingRepair；
     * 售后完成 completed；
     * 售后关闭 closed；
     * 用户取消 cancel
     * 退款中 refunding
     *
     */
    @Schema(description = "状态：待审核 pendingAudit；待寄回商品 pendingReturn；寄回商品待检测 pendingCheck；待退款 pendingRefund；待发货 pendingShip；待收货 pendingReceive；待上门维修 pendingRepair；售后完成 completed；售后关闭 closed；用户取消 cancel", example = "processing")
    private String afterSaleStatus;

    /**
     * 状态节点时间
     */
    @Schema(description = "状态节点时间", example = "2022-01-01 10:10:10")
    private LocalDateTime statusTime;

}
