package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.OrderStatusConstant;
import com.yhd.fa.marketing.cms.dao.OrderDAO;
import com.yhd.fa.marketing.cms.dao.OrderMarketingDAO;
import com.yhd.fa.marketing.cms.pojo.po.OrderMarketingPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CouponOrderCountRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderCountResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderFinishCountResponseVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: OrderMarketingLogic.java, v 0.1 2024/8/8 9:46 JiangYuHong Exp $
 */
@Component
public class OrderMarketingLogic {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderMarketingLogic.class.getName());


    @Resource
    private OrderDAO orderDAO;

    @Resource
    private OrderMarketingDAO orderMarketingDAO;

    /**
     * 优惠券订单完成
     *
     * @param couponId 优惠券id
     * @return businessResponse
     */
    public BusinessResponse<CouponOrderFinishCountResponseVO> couponOrderFinish(List<String> couponId) {

        logger.info("start marketing coupon order finish logic.");

        CouponOrderFinishCountResponseVO countResponseVO = new CouponOrderFinishCountResponseVO();

        // 查询关联信息表数据
        List<OrderMarketingPO> orderMarketingPOS = orderMarketingDAO.selectList(
                new LambdaQueryWrapper<OrderMarketingPO>()
                        .in(OrderMarketingPO::getCouponId, couponId)
        );

        if (CollUtil.isEmpty(orderMarketingPOS)){
            return BusinessResponseCommon.ok(countResponseVO);
        }

        // 查询订单信息
        List<String> orderNumberList = orderMarketingPOS.stream().distinct().map(OrderMarketingPO::getOrderNumber).collect(Collectors.toList());

        Long count = orderDAO.selectCount(
                new LambdaQueryWrapper<OrderPO>()
                        .eq(OrderPO::getOrderStatus, OrderStatusConstant.FINISH)
                        .in(OrderPO::getOrderNumber, orderNumberList)
        );

        countResponseVO.setFinishCount(count);

        return BusinessResponseCommon.ok(countResponseVO);
    }

    /**
     * 优惠券订单统计
     *
     * @param requestVO 请求参数
     * @return businessResponse
     */
    public BusinessResponse<List<CouponOrderCountResponseVO>> couponOrderCount(CouponOrderCountRequestVO requestVO) {

        logger.info("start marketing coupon order count logic.");

        List<CouponOrderCountResponseVO> couponOrderCountResponseVOS = orderMarketingDAO.selectJoinList(CouponOrderCountResponseVO.class,
                new MPJLambdaWrapper<OrderMarketingPO>()
                        .leftJoin(OrderPO.class, OrderPO::getOrderNumber, OrderMarketingPO::getOrderNumber)
                        .selectAs(OrderMarketingPO::getPromotionId, CouponOrderCountResponseVO::getPromotionId)
                        .selectAs(OrderPO::getOrderNumber, CouponOrderCountResponseVO::getOrderNumber)
                        .selectAs(OrderPO::getCompanyCode, CouponOrderCountResponseVO::getCompanyCode)
                        .selectAs(OrderPO::getUserCode, CouponOrderCountResponseVO::getUserCode)
                        .selectAs(OrderPO::getPayablePrice, CouponOrderCountResponseVO::getTotalMoney)
                        .selectAs(OrderPO::getCreatedDate, CouponOrderCountResponseVO::getCreatedDate)
                        .in(OrderMarketingPO::getPromotionId, requestVO.getPromotionIds())
                        .ne(OrderPO::getOrderStatus, OrderStatusConstant.CANCEL)
        );

        if (CollUtil.isEmpty(couponOrderCountResponseVOS)) {
            return BusinessResponseCommon.ok(new ArrayList<>());
        }

        return BusinessResponseCommon.ok(couponOrderCountResponseVOS);

    }
}
