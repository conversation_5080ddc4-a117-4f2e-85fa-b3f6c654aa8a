package com.yhd.fa.marketing.cms.web.quotation;

import com.alibaba.nacos.common.http.param.MediaType;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.response.ExcelFileAnalysisResponseVO;
import com.yhd.fa.marketing.cms.service.controller.QuotationUploadFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: QuotationUploadFileController.java, v0.1 2023/1/3 17:43 yehuasheng Exp $
 */
@Tag(name = "上传文件接口", description = "上传附件接口")
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, consumes = MediaType.MULTIPART_FORM_DATA, produces = MediaType.APPLICATION_JSON)
@RestController
public class QuotationUploadFileController {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(QuotationUploadFileController.class.getName());

    /**
     * 报价单上传文件服务
     */
    @Resource
    private QuotationUploadFileService quotationUploadFileService;

    /**
     * 解析excel的内容并且进行询价
     *
     * @param excelFile excel文件流
     * @return BusinessResponse<List < ExcelFileAnalysisResponseVO>>
     */
    @Operation(summary = "解析excel的内容并且进行询价", parameters = {@Parameter(name = "excelFile", description = "文件流"), @Parameter(name = "userCode", description = "用户编码", example = "139779")})
    @PostMapping(value = UriConstant.EXCEL_FILE_ANALYSIS)
    public BusinessResponse<List<ExcelFileAnalysisResponseVO>> excelFileAnalysis(@RequestParam("excelFile") MultipartFile excelFile, @RequestParam String userCode) {
        logger.info("request excel file analysis api. parameter userCode:{} excelFile:{}", userCode, excelFile);

        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<List<ExcelFileAnalysisResponseVO>> businessResponse = quotationUploadFileService.excelFileAnalysis(excelFile, userCode);
        logger.info("excel file analysis api success result businessResponse:{}", businessResponse);
        logger.info("excel file analysis api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }
}
