package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.*;
import com.yhd.fa.marketing.cms.dao.OrderClickHouseDAO;
import com.yhd.fa.marketing.cms.dao.OrderDAO;
import com.yhd.fa.marketing.cms.dao.OrderListClickHouseDAO;
import com.yhd.fa.marketing.cms.dao.OrderListDAO;
import com.yhd.fa.marketing.cms.enums.OrderChannelEnum;
import com.yhd.fa.marketing.cms.enums.OrderSourceEnum;
import com.yhd.fa.marketing.cms.enums.OrderStatusEnum;
import com.yhd.fa.marketing.cms.enums.PaymentTypeStatusEnum;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderListClickHousePO;
import com.yhd.fa.marketing.cms.pojo.po.OrderListPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderListResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.SqlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: OrderListLogic.java, v0.1 2022/12/2 14:25 yehuasheng Exp $
 */
@Component
public class GetOrderListLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderListLogic.class.getName());

    @Resource
    private OrderClickHouseDAO orderClickHouseDAO;

    @Resource
    private OrderDAO orderDAO;

    /**
     * 用户中旬服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private OrderListClickHouseLogic orderListClickHouseLogic;

    // 不能取消的状态列表
    public static final List<String> canNotCancelStatusList = Arrays.asList(
            OrderStatusConstant.CANCEL,
            OrderStatusConstant.FINISH,
            OrderStatusConstant.STOCK_UP,
            OrderStatusConstant.CANCELING,
            OrderStatusConstant.TAKE_DELIVERED
    );

    @Resource
    private DataAuthLogic dataAuthLogic;
    @Resource
    private OrderListDAO orderListDAO;

    @Resource
    private OrderListClickHouseDAO orderListClickHouseDAO;

    /**
     * 获取订单列表
     *
     * @param orderListRequestVO 订单列表请求参数
     * @return BusinessResponse<PageInfo < OrderListResponseVO>>
     */
    public BusinessResponse<PageInfo<OrderListResponseVO>> exec(OrderListRequestVO orderListRequestVO) {
        logger.info("start exec get order list logic.");

        // 设置查询条件
        MPJLambdaWrapper<OrderPO> lambdaWrapper = setQueryWrapper(orderListRequestVO);

        // 查询报价单列表
        List<OrderListResponseVO> orderList = queryCrossData(orderListRequestVO, lambdaWrapper);

        // 设置分页
        PageInfo<OrderListResponseVO> pageInfo = new PageInfo<>(orderList);

        // 设置查询后的结果其余的值 并且 返回
        return setOrderOtherValue(pageInfo);
    }

    /**
     * 设置查询条件
     *
     * @param orderListRequestVO 订单列表查询参数
     * @return MPJLambdaWrapper<OrderPO>
     */
    private MPJLambdaWrapper<OrderPO> setQueryWrapper(OrderListRequestVO orderListRequestVO) {
        logger.info("set query wrapper.");

        // 设置查询的字段
        MPJLambdaWrapper<OrderPO> queryWrapper = OrderUtil.setQueryWrapperSelectAs();

        // 设置排序
        queryWrapper.orderByDesc(OrderPO::getCreatedDate);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 判断是否有传时间
        setTimeQueryWrapper(orderListRequestVO, queryWrapper);

        // 判断查询是否订单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderListRequestVO.getOrderNumber(), queryWrapper, OrderPO::getOrderNumber);
        // 企业名称
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderListRequestVO.getCompanyName(), queryWrapper, OrderPO::getCompanyName);
        // 订单状态
        setOrderStatus(orderListRequestVO, queryWrapper);

        // 支付类型
        setPaymentType(orderListRequestVO, queryWrapper);

        // 订单渠道
        setOrderChannel(orderListRequestVO, queryWrapper);

        // 订单来源
        setOrderSource(orderListRequestVO, queryWrapper);

        //订单所属平台
        setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE, queryWrapper);

        return queryWrapper;
    }

    private void setPlatformCode(String platformCode, MPJLambdaWrapper<OrderPO> queryWrapper) {

        if (StringUtils.isNotBlank(platformCode)) {
            queryWrapper.eq(OrderPO::getPlatformCode, platformCode);
        }

    }

    /**
     * 设置查询时间
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setTimeQueryWrapper(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderPO> queryWrapper) {
        logger.info("get order list set time query wrapper.");

        // 开始时间
        if (ObjectUtil.isNotNull(orderListRequestVO.getStartDateTime())) {
            queryWrapper.ge(OrderPO::getCreatedDate, orderListRequestVO.getStartDateTime());
        }

        // 结束时间
        if (ObjectUtil.isNotNull(orderListRequestVO.getEndDateTime())) {
            queryWrapper.le(OrderPO::getCreatedDate, orderListRequestVO.getEndDateTime());
        }
    }

    /**
     * 设置查询订单状态
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setOrderStatus(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderPO> queryWrapper) {
        logger.info("get order list set order status query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getOrderStatus())) {
            if(StringUtils.equals(orderListRequestVO.getOrderStatus(), PaymentStatusConstant.PAYMENT_FAILED)) {
                queryWrapper.eq(OrderPO::getPaymentStatus, PaymentStatusConstant.PAYMENT_FAILED)
                        .eq(OrderPO::getSettlementType, SettlementTypeConstant.ONLINE)
                        .eq(OrderPO::getOrderStatus, OrderStatusConstant.UNPAID);
            } else {
                queryWrapper.eq(OrderPO::getOrderStatus, orderListRequestVO.getOrderStatus());
            }
        }
    }

    /**
     * 设置查询订单支付类型
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setPaymentType(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderPO> queryWrapper) {
        logger.info("get order list set payment type query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getPaymentType())) {
            if (StringUtils.equals(orderListRequestVO.getPaymentType(), PaymentTypeConstant.OFFLINE_PAY)) {
                // 线下付款
                queryWrapper.eq(OrderPO::getSettlementType, SettlementTypeConstant.ONLINE);
                queryWrapper.eq(OrderPO::getPaymentType, PaymentTypeConstant.BANK_TRANSFER);
            } else if (StringUtils.equals(orderListRequestVO.getPaymentType(), PaymentTypeConstant.MONTHLY)) {
                // 月结
                queryWrapper.eq(OrderPO::getSettlementType, SettlementTypeConstant.OFFLINE);
            } else {
                queryWrapper.eq(OrderPO::getSettlementType, SettlementTypeConstant.ONLINE);
                queryWrapper.eq(OrderPO::getPaymentType, orderListRequestVO.getPaymentType());
            }
        }
    }

    /**
     * 设置查询订单渠道
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setOrderChannel(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderPO> queryWrapper) {
        logger.info("get order list set order channel query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getOrderChannel())) {
            queryWrapper.eq(OrderPO::getOrderChannel, orderListRequestVO.getOrderChannel());
        }
    }

    /**
     * 设置查询订单来源
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setOrderSource(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderPO> queryWrapper) {
        logger.info("get order list set order source query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getOrderSource())) {
            queryWrapper.eq(OrderPO::getOrderSource, orderListRequestVO.getOrderSource());
        }
    }

    /**
     * 设置订单明细中的其他值
     *
     * @param pageInfo 分页的详情
     * @return BusinessResponse<PageInfo < OrderListResponseVO>>
     */
    private BusinessResponse<PageInfo<OrderListResponseVO>> setOrderOtherValue(PageInfo<OrderListResponseVO> pageInfo) {
        logger.info("set order other value.");

        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            // 获取用户信息
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userBaseMap = getUserBaseInfo(pageInfo.getList());

            // 设置订单状态名称
            Map<String, String> orderStatusName = Arrays.stream(OrderStatusEnum.values()).collect(Collectors.toMap(OrderStatusEnum::getOrderStatus, OrderStatusEnum::getOrderStatusCn));

            // 设置订单支付状态名称
            Map<String, String> paymentTypeStatusName = Arrays.stream(PaymentTypeStatusEnum.values()).collect(Collectors.toMap(PaymentTypeStatusEnum::getPaymentTypeStatus, PaymentTypeStatusEnum::getPaymentTypeStatusCn));

            // 设置订单来源名称
            Map<String, String> orderSourceName = Arrays.stream(OrderSourceEnum.values()).collect(Collectors.toMap(OrderSourceEnum::getOrderSource, OrderSourceEnum::getOrderSourceCn));

            // 设置订单渠道名称
            Map<String, String> orderChannelName = Arrays.stream(OrderChannelEnum.values()).collect(Collectors.toMap(OrderChannelEnum::getOrderChannel, OrderChannelEnum::getOrderChannelCn));

            // 获取订单明细
            Map<String, List<OrderListPO>> orderListMap = getOrderListMap(pageInfo);

            pageInfo.getList().forEach(orderListResponseVO -> {
                // 设置用户信息
                Optional.ofNullable(userBaseMap.get(orderListResponseVO.getUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
                    // 设置业务员信息
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> {
                        // 设置业务员名称联系方式
                        orderListResponseVO.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
                        orderListResponseVO.setSalesmanContact(salesMan.getMobile());
                    });
                    // 设置跟单的信息
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserResponseVO -> {
                        orderListResponseVO.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
                        orderListResponseVO.setMerchandiserContact(merchandiserResponseVO.getMobile());
                    });

                    // 设置用户的名字
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getUserInfo()).ifPresent(baseUserInfoResponseVO -> orderListResponseVO.setUserName(baseUserInfoResponseVO.getUserName()));
                });

                // 设置订单状态
                orderListResponseVO.setOrderStatusName(StringUtils.equals(orderListResponseVO.getPaymentStatus(), PaymentStatusConstant.PAYMENT_FAILED) && StringUtils.equals(orderListResponseVO.getSettlementType(), SettlementTypeConstant.ONLINE) && StringUtils.equals(orderListResponseVO.getOrderStatus(), OrderStatusConstant.UNPAID) ? "支付异常" : orderStatusName.getOrDefault(orderListResponseVO.getOrderStatus(), null));
                // 设置订单支付状态名称
                orderListResponseVO.setPaymentTypeName(StringUtils.equals(orderListResponseVO.getPaymentStatus(), PaymentStatusConstant.PAYMENT_FAILED) && StringUtils.equals(orderListResponseVO.getSettlementType(), SettlementTypeConstant.ONLINE) && StringUtils.equals(orderListResponseVO.getOrderStatus(), OrderStatusConstant.UNPAID) ? "支付异常" : paymentTypeStatusName.get(orderListResponseVO.getPaymentType()));
                // 设置订单来源名称
                orderListResponseVO.setOrderSourceName(orderSourceName.get(orderListResponseVO.getOrderSource()));
                // 设置订单渠道名称
                orderListResponseVO.setOrderChannelName(orderChannelName.get(orderListResponseVO.getOrderChannel()));
                // 设置是否可以取消
                if (orderListMap.containsKey(orderListResponseVO.getOrderNumber())) {
                    orderListResponseVO.setCancelStatus(
                            orderListMap.get(orderListResponseVO.getOrderNumber()).stream()
                                    .allMatch(orderListPO -> canNotCancelStatusList.contains(orderListPO.getOrderDetailStatus()))
                                    ? CommonConstant.TRUE : CommonConstant.FALSE
                    );
                }
            });
        }

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 获取订单明细
     *
     * @param pageInfo 分页信息
     * @return Map
     */
    private Map<String, List<OrderListPO>> getOrderListMap(PageInfo<OrderListResponseVO> pageInfo) {

        List<String> orderNumberList = pageInfo.getList().stream().map(OrderListResponseVO::getOrderNumber).distinct().collect(Collectors.toList());

        List<OrderListPO> orderListPOS = orderListDAO.selectList(new LambdaQueryWrapper<OrderListPO>()
                .in(OrderListPO::getOrderNumber, orderNumberList)
        );


        if (CollUtil.isNotEmpty(orderListPOS)) {
            List<String> list = orderListPOS.stream().map(OrderListPO::getOrderNumber).distinct().collect(Collectors.toList());
            if (list.size() == pageInfo.getList().size()) {
                return orderListPOS.stream().collect(Collectors.groupingBy(OrderListPO::getOrderNumber));
            } else {
                orderNumberList.removeAll(list);
                List<OrderListClickHousePO> orderListClickHousePOS = orderListClickHouseDAO.selectList(new LambdaQueryWrapper<OrderListClickHousePO>()
                        .in(OrderListClickHousePO::getOrderNumber, orderNumberList)
                );
                orderListPOS.addAll(BeanUtil.copyToList(orderListClickHousePOS, OrderListPO.class));
            }
        }

        return orderListPOS.stream().collect(Collectors.groupingBy(OrderListPO::getOrderNumber));

    }

    /**
     * 获取用户信息
     *
     * @param orderList 订单列表
     * @return Map<String, BaseUserInfoResponseVO>
     */
    private Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserBaseInfo(List<OrderListResponseVO> orderList) {
        logger.info("get order list user info.");

        // 提取订单列表中的用户编码
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = orderList
                .stream()
                .map(orderListResponseVO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .companyCode(orderListResponseVO.getCompanyCode())
                        .userCode(orderListResponseVO.getUserCode())
                        .build()).collect(Collectors.toList());

        // 获取用户信息map
        return userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);
    }

    /**
     * 获取完整的SQL语句(带参数值)
     * @param wrapper MPJLambdaWrapper对象
     * @return 完整SQL
     */
    private String getSelectOrderListFullSql(MPJLambdaWrapper<OrderPO> wrapper) {
        String sqlSelect = wrapper.getSqlSelect();
        String sqlFrom = wrapper.getFrom();
        String sqlComment = wrapper.getCustomSqlSegment();

        // 获取参数映射
        Map<String, Object> paramNameValuePairs = wrapper.getParamNameValuePairs();

        // 组装基础SQL
        String sql = "SELECT DISTINCT " + sqlSelect + " " +
                "FROM fa_order t" + sqlFrom + " " +
                sqlComment;

        // 替换参数值
        for (Map.Entry<String, Object> entry : paramNameValuePairs.entrySet()) {
            String paramName = "#{ew.paramNameValuePairs." + entry.getKey() + "}";
            Object paramValue = entry.getValue();

            // 根据参数类型处理值
            String replaceValue;
            if (paramValue instanceof String) {
                replaceValue = "'" + paramValue + "'";
            } else if (paramValue instanceof LocalDateTime) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BaseConstant.DATE_FORMATTER);
                replaceValue = "'" + ((LocalDateTime) paramValue).format(formatter) + "'";
            } else {
                replaceValue = String.valueOf(paramValue);
            }

            sql = sql.replace(paramName, replaceValue);
        }

        return sql;
    }

    /**
     * 查询跨冷热数据
     * @param requestVO 请求参数
     * @return List<OrderListResponseVO>
     */
    private List<OrderListResponseVO> queryCrossData(OrderListRequestVO requestVO, MPJLambdaWrapper<OrderPO> queryWrapper) {

        logger.info("start query cross data.");

        try (Page<OrderListResponseVO> page = new Page<>(requestVO.getPageNum(), requestVO.getPageSize())){

            // 获取查询总条数的sql
            String fullSql = getSelectOrderListFullSql(queryWrapper);
            // 查询MySql和ClickHouse的总条数
            Integer mysqlTotal = orderDAO.countOrderList(fullSql);
            Integer clickHouseTotal = orderClickHouseDAO.countOrderList(fullSql);

            // 计算总页数
            int total = clickHouseTotal + mysqlTotal;
            int residueP = total % requestVO.getPageSize();
            int totalPage = total / requestVO.getPageSize();
            if (residueP > CommonConstant.ZERO) {
                totalPage = totalPage + CommonConstant.ONE;
            }

            int startMysql = (requestVO.getPageNum() - CommonConstant.ONE) * requestVO.getPageSize();
            String lastSql = FaDocMarketingCmsConstant.LIMIT_STR + startMysql + CommonConstant.SYMBOL_COMMA + requestVO.getPageSize();

            int mysqlPageTotal = requestVO.getPageNum() * requestVO.getPageSize();
            //总条数小于mysql总条数，就走查询mysql
            if (mysqlPageTotal <= mysqlTotal) {

                queryWrapper.last(lastSql);
                List<OrderListResponseVO> mySqlData = orderDAO.selectJoinList(OrderListResponseVO.class,queryWrapper);

                // 设置分页数据
                page.setTotal(total);
                page.setPages(totalPage);
                page.setPageSize(mySqlData.size());
                page.addAll(mySqlData);

                return page;
            }

            // 剩余数量小于每页显示的条数，查两个库
            int residue = mysqlPageTotal - mysqlTotal;
            if (residue < requestVO.getPageSize()) {
                queryWrapper.last(lastSql);
                List<OrderListResponseVO> mySqlData = orderDAO.selectJoinList(OrderListResponseVO.class, queryWrapper);

                List<OrderListResponseVO> clickHouseData = orderListClickHouseLogic.selectListDeepForClickHouse(requestVO,FaDocMarketingCmsConstant.LIMIT_0_STR + residue);

                // 合并数据
                mySqlData.addAll(clickHouseData);

                // 按创建时间排序
                mySqlData.sort((o1, o2) -> o2.getCreatedDate().compareTo(o1.getCreatedDate()));

                // 设置分页数据
                page.setTotal(total);
                page.setPages(totalPage);
                page.setPageSize(mySqlData.size());
                page.addAll(mySqlData);

                return page;
            }

            // 计算查询click house数据的页数
            int mysqlPage = Math.toIntExact(mysqlTotal % requestVO.getPageSize());
            int mysqlTotalPage = Math.toIntExact(mysqlTotal / requestVO.getPageSize()) + CommonConstant.ONE;
            int clickHouseStartPage = requestVO.getPageNum() - mysqlTotalPage - CommonConstant.ONE;
            int clickHouseStart = clickHouseStartPage * requestVO.getPageSize() + (requestVO.getPageSize() - mysqlPage);

            // 查询数据
            List<OrderListResponseVO> clickHouseData = orderListClickHouseLogic.selectListDeepForClickHouse(requestVO,FaDocMarketingCmsConstant.LIMIT_STR + clickHouseStart + CommonConstant.SYMBOL_COMMA + requestVO.getPageSize());

            // 设置分页数据
            page.setTotal(total);
            page.setPages(totalPage);
            page.setPageSize(clickHouseData.size());
            page.addAll(clickHouseData);

            return page;

        } catch (Exception e) {
            logger.error("query mysql and click house order list fail. errorMsg:{}", e.toString());
            return new Page<>();
        }
    }
}
