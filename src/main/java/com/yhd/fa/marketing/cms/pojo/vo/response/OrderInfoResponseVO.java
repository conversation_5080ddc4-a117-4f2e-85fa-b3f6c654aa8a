package com.yhd.fa.marketing.cms.pojo.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderInfoResponseVO.java, v0.1 2023/2/20 9:42 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderInfoResponseVO extends OrderListResponseVO {
    /**
     * 配送方式 inBatches分批发货、merge合并一起
     */
    @Schema(description = "配送方式 inBatches分批发货、merge合并一起", example = "inBatches")
    private String shipping;

    /**
     * 首批发货商品的发货日
     */
    @Schema(description = "首批发货商品的发货日", example = "1")
    private Integer firstShippingDay;

    /**
     * 客户内部单号
     */
    @Schema(description = "客户内部单号", example = "A123456456")
    private String customerNumber;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间", example = "2022-01-01 10:10:10")
    private LocalDateTime payDate;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "给我点钱")
    private String remark;

    /**
     * 收件人
     */
    @Schema(description = "收件人", example = "你猜")
    private String consignee;

    /**
     * 收件人联系方式
     */
    @Schema(description = "收件人联系方式", example = "13111111111")
    private String contactNumber;

    /**
     * 省
     */
    @Schema(description = "省", example = "广东省")
    private String province;

    /**
     * 市区
     */
    @Schema(description = "市区", example = "东莞市")
    private String city;

    /**
     * 镇/区
     */
    @Schema(description = "镇/区", example = "横沥镇")
    private String town;

    /**
     * 收货地址
     */
    @Schema(description = "收货地址", example = "桃园区二号")
    private String address;

    /**
     * 订单明细列表
     */
    @Schema(description = "订单明细列表")
    private List<OrderDetailsResponseVO> orderDetails;

    /**
     * 订单包裹信息
     */
    @Schema(description = "订单包裹信息")
    private List<OrderLogisticsInformationResponseVO> orderLogisticsInformation;
}
