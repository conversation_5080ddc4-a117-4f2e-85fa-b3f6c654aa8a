package com.yhd.fa.marketing.cms.enums;


import com.yhd.common.util.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: ShareSystemdCompanyLevelEnum.java, v 0.1 2025/5/15 10:16 <PERSON><PERSON>uHong Exp $
 */
@Getter
@AllArgsConstructor
public enum ShareSystemdCompanyLevelEnum {

    // 资料客户1, 潜在客户2,意向客户3,成交客户4,长期客户5,FANS客户6,沉睡客户7,沉睡可转移8
    DATA_CUSTOMER(1, "资料客户"),
    POTENTIAL_CUSTOMER(2, "潜在客户"),
    INTENTIONAL_CUSTOMER(3, "意向客户"),
    TRANSACTION_CUSTOMER(4, "成交客户"),
    LONG_TERM_CUSTOMER(5, "长期客户"),
    FANS_CUSTOMER(6, "FANS客户"),
    SLEEP_CUSTOMER(7, "沉睡客户"),
    SLEEP_TRANSFER_CUSTOMER(8, "沉睡可转移客户"),
    ;
    private final Integer code;
    private final String name;

    public static String getName(Integer code) {

        for (ShareSystemdCompanyLevelEnum value : ShareSystemdCompanyLevelEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }

        return CommonConstant.DASH;
    }
}
