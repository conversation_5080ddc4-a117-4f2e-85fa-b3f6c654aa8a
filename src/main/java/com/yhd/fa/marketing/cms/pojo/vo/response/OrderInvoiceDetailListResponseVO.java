package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceDetailListResponseVO.java, v0.1 2023/2/23 14:46 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderInvoiceDetailListResponseVO extends BaseVO {
    /**
     * 订单发票明细id
     */
    @Schema(description = "订单发票明细id", example = "037ca7edce70404ba83671736af5152f")
    private String orderInvoiceDetailId;

    /**
     * 订单id
     */
    @Schema(description = "订单id", example = "1cbe348719ec4882ade85a3e1f53f18c")
    private String orderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "YI2022111615065597799D0M")
    private String orderNumber;

    /**
     * 发票索取金额
     */
    @Schema(description = "发票索取金额", example = "24.02")
    private BigDecimal payablePrice;

    /**
     * 订单创建时间
     */
    @Schema(description = "订单创建时间", example = "2022-01-01 10:10:10")
    private LocalDateTime orderCreatedDate;
}
