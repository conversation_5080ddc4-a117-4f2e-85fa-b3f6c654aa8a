package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndMerchandiserAndSalesmanInfoResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.ShippingAddressResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.BusinessTypeConstant;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.OrderInvoiceNatureConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.SynchronizeStatusEnum;
import com.yhd.fa.marketing.cms.mapper.OrderInvoiceListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderInvoiceMapper;
import com.yhd.fa.marketing.cms.mq.producer.SynchronizationProducer;
import com.yhd.fa.marketing.cms.pojo.dto.FaOrderInvoiceDetailSynchronizationDTO;
import com.yhd.fa.marketing.cms.pojo.dto.FaOrderInvoiceSynchronizationDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderInvoiceListPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderInvoicePO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SynchronizationOrderInvoiceLogic.java, v0.1 2023/3/2 12:11 yehuasheng Exp $
 */
@Component
public class SynchronizationOrderInvoiceLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SynchronizationOrderInvoiceLogic.class.getName());

    /**
     * 订单发票mapper
     */
    @Resource
    private OrderInvoiceMapper orderInvoiceMapper;

    /**
     * 订单发票列表mapper
     */
    @Resource
    private OrderInvoiceListMapper orderInvoiceListMapper;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 同步生产者
     */
    @Resource
    private SynchronizationProducer synchronizationProducer;

    /**
     * 执行订单发票同步
     *
     * @param orderInvoiceId 订单发票id
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<Void> exec(String orderInvoiceId) {
        logger.info("start exec synchronization order invoice logic.");

        // 查询订单发票详情
        OrderInvoicePO orderInvoiceInfo = orderInvoiceMapper.getOne(new MPJLambdaWrapper<OrderInvoicePO>().selectAll(OrderInvoicePO.class).eq(OrderInvoicePO::getId, orderInvoiceId).last(FaDocMarketingCmsConstant.LIMIT));
        if (ObjectUtil.isNull(orderInvoiceInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_INVOICE_IS_NOT_EXISTS);
        }

        // 查询订单发票详情
        List<OrderInvoiceListPO> orderInvoiceList = orderInvoiceListMapper.list(new MPJLambdaWrapper<OrderInvoiceListPO>().selectAll(OrderInvoiceListPO.class).eq(OrderInvoiceListPO::getOrderInvoiceId, orderInvoiceId));
        if (CollUtil.isEmpty(orderInvoiceList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_INVOICE_DETAIL_IS_NOT_EXISTS);
        }

        // 执行同步
        synchronization(orderInvoiceInfo, orderInvoiceList);

        return BusinessResponse.ok(null);
    }

    /**
     * 执行同步
     *
     * @param orderInvoiceInfo 订单发票详情
     * @param orderInvoiceList 订单发票明细
     */
    @Async("faSynchronizationOrderInvoice")
    public void synchronization(OrderInvoicePO orderInvoiceInfo, List<OrderInvoiceListPO> orderInvoiceList) {
        logger.info("exec synchronization order invoice to mq");

        // 数据转换同步数据
        List<FaOrderInvoiceDetailSynchronizationDTO> faOrderInvoiceDetailSynchronization = setSynchronizationOrderInvoiceDetailData(orderInvoiceList);

        // 设置同步的数据
        FaOrderInvoiceSynchronizationDTO faOrderInvoiceSynchronizationDTO = setSynchronizationOrderInvoiceData(orderInvoiceInfo, faOrderInvoiceDetailSynchronization);

        //如果是纸质发票把省份和市区加上
        if (StringUtils.equals(OrderInvoiceNatureConstant.PAPER, orderInvoiceInfo.getInvoiceNature()) && StringUtils.isBlank(orderInvoiceInfo.getEmail())) {
            //请求用户中心获取收货地址信息
            ShippingAddressResponseVO addressResponseVO = userBucCmsService.getUserAddressByAddressId(orderInvoiceInfo.getUserAddressId(), "invoice", orderInvoiceInfo.getUserCode());
            //设置省份,市区
            Optional.ofNullable(addressResponseVO.getProvince()).ifPresent(faOrderInvoiceSynchronizationDTO::setProvince);
            Optional.ofNullable(addressResponseVO.getCity()).ifPresent(faOrderInvoiceSynchronizationDTO::setCity);
        }

        // 执行同步
        synchronizationProducer.synchronizationData(JSON.toJSONString(faOrderInvoiceSynchronizationDTO), BusinessTypeConstant.ORDER_INVOICE, orderInvoiceInfo.getInvoiceNumber());

        // 更新同步状态
        orderInvoiceMapper.update(null,
                new LambdaUpdateWrapper<OrderInvoicePO>()
                        .set(OrderInvoicePO::getSendErpStatus, SynchronizeStatusEnum.SYNCHRONIZING.getStatus())
                        .set(OrderInvoicePO::getUpdatedDate, LocalDateTime.now())
                        .eq(OrderInvoicePO::getId, orderInvoiceInfo.getId())
        );
    }

    /**
     * 设置同步订单发票的明细数据
     *
     * @param orderInvoiceList 订单发票明细
     * @return List<FaOrderInvoiceDetailSynchronizationDTO>
     */
    private List<FaOrderInvoiceDetailSynchronizationDTO> setSynchronizationOrderInvoiceDetailData(List<OrderInvoiceListPO> orderInvoiceList) {
        logger.info("set synchronization order invoice detail data.");

        return orderInvoiceList.stream().map(orderInvoiceListPO ->
                FaOrderInvoiceDetailSynchronizationDTO
                        .builder()
                        .id(orderInvoiceListPO.getId())
                        .orderInvoiceId(orderInvoiceListPO.getOrderInvoiceId())
                        .payablePrice(orderInvoiceListPO.getPayablePrice())
                        .orderCreatedDate(orderInvoiceListPO.getCreatedDate())
                        .orderNumber(orderInvoiceListPO.getOrderNumber())
                        .build()
        ).collect(Collectors.toList());
    }

    /**
     * 设置同步订单发票详情数据
     *
     * @param orderInvoiceInfo                    发票详情
     * @param faOrderInvoiceDetailSynchronization 同步数据
     * @return FaOrderInvoiceSynchronizationDTO
     */
    private FaOrderInvoiceSynchronizationDTO setSynchronizationOrderInvoiceData(OrderInvoicePO orderInvoiceInfo, List<FaOrderInvoiceDetailSynchronizationDTO> faOrderInvoiceDetailSynchronization) {
        logger.info("set synchronization order invoice data.");

        // 获取用户信息
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(orderInvoiceInfo.getUserCode(), orderInvoiceInfo.getCompanyCode());

        //获取企业信息
        Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyAndMerchandiserAndSalesmanInfo = userBucCmsService.getCompanyAndMerchandiserAndSalesmanInfo(Collections.singletonList(orderInvoiceInfo.getCompanyCode()));
        CompanyAndMerchandiserAndSalesmanInfoResponseVO companyInfo = null;
        if (companyAndMerchandiserAndSalesmanInfo.containsKey(orderInvoiceInfo.getCompanyCode())) {
            companyInfo = companyAndMerchandiserAndSalesmanInfo.get(orderInvoiceInfo.getCompanyCode());
        }

        return FaOrderInvoiceSynchronizationDTO.builder()
                .id(orderInvoiceInfo.getId())
                .invoiceNumber(orderInvoiceInfo.getInvoiceNumber())
                .invoiceCode(orderInvoiceInfo.getInvoiceCode())
                .invoiceType(orderInvoiceInfo.getInvoiceType())
                .invoiceNature(orderInvoiceInfo.getInvoiceNature())
                .userAddressId(orderInvoiceInfo.getUserAddressId())
                .userAddress(orderInvoiceInfo.getUserAddress())
                .consignee(orderInvoiceInfo.getConsignee())
                .linkPhone(orderInvoiceInfo.getLinkPhone())
                .email(orderInvoiceInfo.getEmail())
                .companyCode(ObjectUtil.isNotNull(companyInfo) ? companyInfo.getCompanyInfo().getErpCompanyCode() : orderInvoiceInfo.getCompanyCode())
                .userCode(orderInvoiceInfo.getUserCode())
                .userName(ObjectUtil.isNotNull(userInfo.getCompanyInfo()) ? userInfo.getCompanyInfo().getErpCompanyCode() : null)
                .invCustomerName(orderInvoiceInfo.getInvCustomerName())
                .invTaxRegNumber(orderInvoiceInfo.getInvTaxRegNumber())
                .invAddress(orderInvoiceInfo.getInvAddress())
                .invLinkPhone(orderInvoiceInfo.getInvLinkPhone())
                .invManuBank(orderInvoiceInfo.getInvManuBank())
                .invAccount(orderInvoiceInfo.getInvAccount())
                .invoiceStatus(orderInvoiceInfo.getInvoiceStatus())
                .logisticsCode(orderInvoiceInfo.getLogisticsCode())
                .logisticsName(orderInvoiceInfo.getLogisticsName())
                .remarks(orderInvoiceInfo.getRemarks())
                .price(orderInvoiceInfo.getPrice())
                .deliveryStatus(orderInvoiceInfo.getDeliveryStatus())
                .territory(orderInvoiceInfo.getTerritory())
                .createdDate(orderInvoiceInfo.getCreatedDate())
                .orderInvoiceDetails(faOrderInvoiceDetailSynchronization)
                .build();
    }
}
