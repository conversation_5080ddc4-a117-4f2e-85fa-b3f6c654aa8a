package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.GoodsStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: GoodsStatusEnum.java, v0.1 2022/9/23 10:22 yehuasheng Exp $
 */
@Getter
public enum GoodsStatusEnum {
    NORMAL(GoodsStatusConstant.NORMAL, "正常"),
    OFF_SHELF(GoodsStatusConstant.OFF_SHELF, "下架"),
    DISCONTINUED(GoodsStatusConstant.DISCONTINUED, "停售"),
    NO_PRICE(GoodsStatusConstant.NO_PRICE, "无价格"),
    QUANTITY_EXCESS(GoodsStatusConstant.QUANTITY_EXCESS, "数量超出"),
    WRONG_MODEL(GoodsStatusConstant.WRONG_MODEL, "型号错误"),
    ;

    private final String status;
    private final String desc;

    GoodsStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
