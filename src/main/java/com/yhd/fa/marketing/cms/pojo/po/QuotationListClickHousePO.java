package com.yhd.fa.marketing.cms.pojo.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: QuotationListClickHousePO.java, v 0.1 2025/4/25 15:20 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("fa_quotation_list")
@EqualsAndHashCode(callSuper = true)
public class QuotationListClickHousePO extends BaseEntity {

    /**
     * 报价单id
     */
    private String quotationId;

    /**
     * 报价单号
     */
    private String quotationNumber;

    /**
     * 序号
     */
    private int sort;

    /**
     * 型号
     */
    private String model;

    /**
     * 客户型号
     */
    private String customerModel;

    /**
     * 代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 数量
     */
    private long quantity;

    /**
     * 旧数量
     */
    private Long oldQuantity;

    /**
     * 原单价
     */
    private BigDecimal originalPrice;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 数量折扣
     */
    private BigDecimal quantityDiscountRate;

    /**
     * 总折扣
     */
    private BigDecimal totalDiscountRate;

    /**
     * 交期
     */
    private Integer delivery;

    /**
     * 备注
     */
    private String remark;

    /**
     * 客户物料编码
     */
    private String customerMaterialCode;

    /**
     * 客户产品名称
     */
    private String customerProductName;

    /**
     * 长度
     */
    private BigDecimal modelLong;

    /**
     * 出图id
     */
    private Integer plotId;

    /**
     * 价格id
     */
    private Integer priceId;

    /**
     * 附加价格id
     */
    private Integer additionalPriceId;

    /**
     * 材质id
     */
    private Integer materialQualityId;

    /**
     * 供应商价格1
     */
    private BigDecimal supplierPriceOne;

    /**
     * 供应商价格2
     */
    private BigDecimal supplierPriceTwo;

    /**
     * 供应商价格3
     */
    private BigDecimal supplierPriceThree;

    /**
     * 供应商型号1
     */
    private String supplierModelOne;

    /**
     * 供应商型号2
     */
    private String supplierModelTwo;

    /**
     * 供应商型号3
     */
    private String supplierModelThree;

    /**
     * 计算要求
     */
    private String technicalSpecifications;

    /**
     * 单位
     */
    private String unit;

    /**
     * 计算价格状态，untreated未处理，calculationError计算错误，calculated已计算，notCalculated不计算，errorModel型号错误，confirmed已确认，quantityExcess数量超出，noPrice无价格，noDelivery无交期
     */
    private String calculationStatus;

    /**
     * 确认状态，confirm确认，disagree不同意
     */
    private String confirmationStatus;

    /**
     * 审核的备注
     */
    private String examineRemark;

    /**
     * 商品状态，normal正常，off_shelf下架，discontinued停售，no_price无价格，quantity_excess，wrong_model型号错误
     */
    private String goodsStatus;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 报价状态，pending待处理，finish报价完成，fail报价失败
     */
    private String quotationStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 怡合达附件地址
     */
    private String insideFileUrl;

    /**
     * 是否标准，standard标准，nonstandard非标准
     */
    private String standardStatus;

    /**
     * 是否推荐，yes是，no不
     */
    private String recommend;

    /**
     * 报价修改人
     */
    private String quotationUpdatedBy;

    /**
     * 类别<非标新增>
     */
    @TableField(exist = false)
    private String categoryCode;

    /**
     * 产品图片url<非标新增>
     */
    @TableField(exist = false)
    private String imageUrl;

    /**
     * 非标报价数据<非标新增>
     */
    private String fbQuoteData;
}
