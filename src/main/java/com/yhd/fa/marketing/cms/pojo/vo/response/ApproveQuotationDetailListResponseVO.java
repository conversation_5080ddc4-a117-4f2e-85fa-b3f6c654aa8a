package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationDetailListResponseVO.java, v0.1 2022/12/15 8:06 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApproveQuotationDetailListResponseVO extends BaseVO {
    /**
     * 报价单详情id
     */
    @Schema(description = "报价单详情id", example = "fcdscfesdwqadwasdwadawdwa")
    private String quotationDetailId;

    /**
     * 报价单明细状态
     */
    @Schema(description = "报价单明细状态 quotation报价中，finish报价完成，close已终止", example = "quotation")
    private String quotationDetailStatus;

    /**
     * 商品型号状态
     */
    @Schema(description = "商品型号状态，normal正常，off_shelf下架，discontinued停售，no_price无价格，quantity_excess，wrong_model型号错误", example = "normal")
    private String goodsStatus;

    /**
     * 是否标准
     */
    @Schema(description = "是否标准，yes标准，no非标准", example = "true")
    private String standardStatus;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息", example = "超过购买数量")
    private String errorMessage;

    /**
     * 客户型号
     */
    @Schema(description = "客户型号", example = "MISIMI123456")
    private String customerModel;

    /**
     * 怡合达型号
     */
    @Schema(description = "怡和达型号 不是客户填写的型号", example = "SAD01-D3-L100")
    private String model;

    /**
     * 产品代码 当型号需要人工报价单时并且匹配到代码时该参数有值
     */
    @Schema(description = "产品代码", example = "SAD01")
    private String code;

    /**
     * 一级分类编码 当型号需要人工报价单时并且匹配到代码时该参数有值
     */
    @Schema(description = "一级分类编码 当型号需要人工报价单时并且匹配到代码时该参数有值", example = "A")
    private String typeCode;

    /**
     * 一级分类名称
     */
    @Schema(description = "一级分类名称 当型号需要人工报价单时并且匹配到代码时该参数有值", example = "工业")
    private String typeName;

    /**
     * 二级分类编码 当型号需要人工报价单时并且匹配到代码时该参数有值
     */
    @Schema(description = "二级分类编码 当型号需要人工报价单时并且匹配到代码时该参数有值", example = "A01")
    private String catCode;

    /**
     * 二级分类名称 当型号需要人工报价单时并且匹配到代码时该参数有值
     */
    @Schema(description = "二级分类名称 当型号需要人工报价单时并且匹配到代码时该参数有值", example = "导向轴")
    private String catName;

    /**
     * 系列编码 当型号需要人工报价单时并且匹配到代码时该参数有值
     */
    @Schema(description = "系列编码 当型号需要人工报价单时并且匹配到代码时该参数有值", example = "SAD01-22")
    private String goodsCode;

    /**
     * 系列名称 当型号需要人工报价单时并且匹配到代码时该参数有值
     */
    @Schema(description = "系列名称 当型号需要人工报价单时并且匹配到代码时该参数有值", example = "导向轴支座")
    private String goodsName;

    /**
     * 类别编码 当型号需要人工报价单时并且匹配到代码时该参数有值
     */
    @Schema(description = "类别编码", example = "A01.01.01")
    private String categoryCode;

    /**
     * 类别名称
     */
    @Schema(description = "类别名称", example = "导向轴")
    private String categoryName;

    /**
     * 数量
     */
    @Schema(description = "数量", example = "1")
    private long quantity;

    /**
     * 客户产品物料编码
     */
    @Schema(description = "客户产品编码", example = "1234567899")
    private String customerMaterialCode;

    /**
     * 客户产品名称
     */
    @Schema(description = "客户产品名称", example = "你猜猜")
    private String customerProductName;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "你猜")
    private String remark;

    /**
     * 附件url地址
     */
    @Schema(description = "附件url地址", example = "https://image.yhdfa.com/sss.png")
    private String fileUrl;

    /**
     * 原价
     */
    @Schema(description = "ERP原价")
    private BigDecimal price;

    /**
     * 折扣单价
     */
    @Schema(description = "未税折扣单价")
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价")
    private BigDecimal taxDiscountPrice;

    /**
     * 总价小计
     */
    @Schema(description = "总价小计")
    private BigDecimal totalPrice;

    /**
     * 交期
     */
    @Schema(description = "交期")
    private Integer delivery;

    /**
     * 数量折扣
     */
    @Schema(description = "数量折扣")
    private BigDecimal quantityDiscountRate;

    /**
     * 总折扣
     */
    @Schema(description = "总折扣")
    private BigDecimal totalDiscountRate;
}
