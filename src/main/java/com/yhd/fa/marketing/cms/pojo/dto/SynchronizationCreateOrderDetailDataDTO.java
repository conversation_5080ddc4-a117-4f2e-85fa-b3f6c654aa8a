package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: SynchronizationCreateOrderDetailDataDTO.java, v0.1 2023/1/27 14:46 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SynchronizationCreateOrderDetailDataDTO extends BaseDTO {
    /**
     * 订单明细id
     */
    private Integer sortId;

    /**
     * 报价单号
     */
    private String quotationNumber;

    /**
     * 报价单明细id
     */
    private Integer quotationSortId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 客户型号
     */
    private String customerModel;

    /**
     * 客户物料号
     */
    private String customerMaterialCode;

    /**
     * 客户产品名称
     */
    private String customerProductName;

    /**
     * 长度
     */
    private BigDecimal modelLong;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 是否标准 true 标准 false 非标准
     */
    private String isStandard;

    /**
     * 原单价
     */
    private BigDecimal price;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣后含税单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总金额
     */
    private BigDecimal totalPrice;

    /**
     * 实付总金额
     */
    private BigDecimal payablePrice;

    /**
     * 数量折扣
     */
    private BigDecimal quantityDiscount;

    /**
     * 总折扣
     */
    private BigDecimal totalDiscount;

    /**
     * 交期
     */
    private int delivery;

    /**
     * 发货日期
     */
    private LocalDateTime shipDate;

    /**
     * 产品单位
     */
    private String unit;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 计算要求
     */
    private String technicalSpecifications;

    /**
     * 明细状态  unpaid 待支付、paid 已支付、untreated 待确认、pendingDelivery 待发货、cancelling 取消中、stockUp 备货中、takeDelivered 待收货、finish 已完成、cancel 已取消、closed已关闭
     */
    private String orderDetailStatus;

    /**
     * 怡合达备注
     */
    private String examineRemark;
}
