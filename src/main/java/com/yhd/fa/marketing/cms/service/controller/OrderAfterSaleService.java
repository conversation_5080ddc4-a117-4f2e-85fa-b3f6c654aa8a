package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderAfterSaleRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleResponseVO;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleService.java, v0.1 2023/2/24 14:51 yehuasheng Exp $
 */
public interface OrderAfterSaleService {
    /**
     * 获取订单售后列表
     *
     * @param orderAfterSaleRequestVO 订单售后列表请求参数
     * @return BusinessResponse<PageInfo < OrderAfterSaleResponseVO>>
     */
    BusinessResponse<PageInfo<OrderAfterSaleResponseVO>> getOrderAfterSaleList(OrderAfterSaleRequestVO orderAfterSaleRequestVO);

    /**
     * 获取订单售后详情页
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<OrderAfterSaleDetailResponseVO>
     */
    BusinessResponse<OrderAfterSaleDetailResponseVO> getOrderAfterSaleInfo(String orderAfterSaleId);

    /**
     * 同步订单售后
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<Void>
     */
    BusinessResponse<Void> synchronizationOrderAfterSale(String orderAfterSaleId);
}
