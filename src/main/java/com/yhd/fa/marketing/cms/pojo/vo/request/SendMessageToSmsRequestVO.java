package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: SendMessageToSmsRequestVO.java, v0.1 2023/3/20 16:00 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendMessageToSmsRequestVO extends BaseVO {
    /**
     * 模板id
     */
    private String templateId;

    /**
     * 数据
     */
    private transient Map<String, Object> map = new HashMap<>();

    /**
     * 手机号码
     */
    private List<String> phoneNumber;

    /**
     * 短信签名(含有短信发送的传入，不然默认签名为：怡合达)；
     */
    private String sign;
}
