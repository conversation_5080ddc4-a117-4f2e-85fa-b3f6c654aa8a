package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.fa.marketing.cms.constant.ExamineStatusConstant;
import com.yhd.fa.marketing.cms.constant.GoodsStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotedPriceStatusConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: QuotationGoodsDetailResponseVO.java, v0.1 2022/12/6 10:13 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuotationGoodsDetailResponseVO extends BaseVO {
    /**
     * id
     */
    @Schema(description = "报价单明细id",
            required = true,
            example = "8106a3f0ff004137b0954c827dae3318")
    private String quotationDetailId;

    /**
     * 序号
     */
    @Schema(description = "序号",
            required = true)
    private Integer productSort;

    /**
     * 报价单号
     */
    @Schema(description = "报价单号",
            required = true)
    private String quotationNumber;

    /**
     * 二级分类
     */
    @Schema(description = "二级分类")
    private String catCode;

    /**
     * 商品状态
     */
    @Schema(description = "商品状态",
            allowableValues = {GoodsStatusConstant.DISCONTINUED + "|停售", GoodsStatusConstant.NORMAL + "|正常", GoodsStatusConstant.NO_PRICE + "|无价格", GoodsStatusConstant.OFF_SHELF + "|下架", GoodsStatusConstant.QUANTITY_EXCESS + "|数量超出", GoodsStatusConstant.WRONG_MODEL + "|型号错误"})
    private String goodsStatus;

    /**
     * 系列代码
     */
    @Schema(description = "系列代码",
            example = "A01-01")
    private String goodsCode;

    /**
     * 图片
     */
    @Schema(description = "图片",
            example = "http://xxxxx.jpg")
    private String imageUrl;

    /**
     * 计算价格状态
     */
    @Schema(description = "计算价格状态",
            example = QuotedPriceStatusConstant.CALCULATED,
            allowableValues = {QuotedPriceStatusConstant.CALCULATED + "|已计算",
                    QuotedPriceStatusConstant.CALCULATION_ERROR + "|计算错误",
                    QuotedPriceStatusConstant.NOT_CALCULATED + "|不计算",
                    QuotedPriceStatusConstant.NO_DELIVERY + "|无交期",
                    QuotedPriceStatusConstant.CONFIRMED + "|已确认",
                    QuotedPriceStatusConstant.NO_PRICE + "|无价格",
                    QuotedPriceStatusConstant.QUANTITY_EXCESS + "|数量超出",
                    QuotedPriceStatusConstant.UNTREATED + "|未处理",
                    QuotedPriceStatusConstant.ERROR_MODEL + "|型号错误"})
    private String calculationStatus;

    /**
     * 客户型号
     */
    @Schema(description = "客户型号",
            example = "ABCD")
    private String customerModel;

    /**
     * 产品代码
     */
    @Schema(description = "产品代码",
            example = "SAD01")
    private String productCode;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private long quantity;

    /**
     * 报价单状态
     */
    @Schema(description = "报价单状态",
            example = QuotationStatusConstant.FINISH,
            allowableValues = {QuotationStatusConstant.QUOTATION + "|快速报价中",
                    QuotationStatusConstant.FINISH + "|报价完成",
                    QuotationStatusConstant.CLOSE + "|已终止"})
    private String quotationStatus;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态",
            example = ExamineStatusConstant.FINISH,
            allowableValues = {ExamineStatusConstant.FINISH + "|已完成",
                    ExamineStatusConstant.UN_FINISH + "|未完成"})
    private String examineStatus;

    /**
     * 状态描述
     */
    @Schema(description = "状态描述", example = "报价完成")
    private String statusCn;

    /**
     * 一级分类代码
     */
    @Schema(description = "一级分类代码", example = "A")
    private String typeCode;

    /**
     * 代码状态
     */
    @Schema(description = "代码状态 normal正常, off下架, stopped停售, online网售, someOnline部分规格网售, someOff部分规格下架, offlineFullAccess仅系统销售，目录、网站不销售, onlineSearchQuotationOfflineOnlyQuotation仅系统、网站型号销售，目录不印刷、网站产品信息屏蔽, onlineInfoSelectionSearchOfflineOnlyBook人工报价, onlineFullAccessOfflineOnlyQuotation网售，网站延用品牌资料")
    private String codeStatus;

    /**
     * 型号名称
     */
    @Schema(description = "型号名称", example = "导向轴")
    private String productName;

    /**
     * 怡合达型号
     */
    @Schema(description = "怡合达型号", example = "SAD01-D3-L100")
    private String model;

    /**
     * 工程师备注
     */
    @Schema(description = "工程师备注")
    private String engineerRemark;

    /**
     * 客户物料编码
     */
    @Schema(description = "客户物料编码", example = "AB123456")
    private String customerMaterialCode;

    /**
     * 客户物料名称
     */
    @Schema(description = "客户物料名称", example = "你猜")
    private String customerProductName;

    /**
     * 明细报价错误信息
     */
    @Schema(description = "明细报价错误信息")
    private String errorMsg;

    /**
     * 附件地址
     */
    @Schema(description = "附件地址")
    private String fileUrl;

    /**
     * 怡合达附件地址
     */
    @Schema(description = "怡合达附件地址")
    private String insideFileUrl;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 型号推荐文字显示判断条件
     */
    @Schema(description = "型号推荐文字显示判断条件")
    private String recommend;

    /**
     * 原价
     */
    @Schema(description = "ERP原价")
    private BigDecimal price;

    /**
     * 折扣单价
     */
    @Schema(description = "未税折扣单价")
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价")
    private BigDecimal taxDiscountPrice;

    /**
     * 总价小计
     */
    @Schema(description = "总价小计")
    private BigDecimal totalPrice;

    /**
     * 交期
     */
    @Schema(description = "交期")
    private Integer delivery;

    /**
     * 数量折扣
     */
    @Schema(description = "数量折扣")
    private BigDecimal quantityDiscountRate;

    /**
     * 总折扣
     */
    @Schema(description = "总折扣")
    private BigDecimal totalDiscountRate;
}
