package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

/**
 * 订单营销(优惠券/促销活动)关联信息表
 *
 * <AUTHOR>
 * @version Id: OrderMarketingPO.java, v 0.1 2024/8/8 9:16 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("fa_order_marketing")
@EqualsAndHashCode(callSuper = true)
public class OrderMarketingPO extends BaseEntity {

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 优惠券id
     */
    private String couponId;

    /**
     * 用户优惠券id
     */
    private String userCouponId;

    /**
     * 促销活动id
     */
    private String promotionId;

    /**
     * 用户促销活动id
     */
    private String userPromotionId;

}