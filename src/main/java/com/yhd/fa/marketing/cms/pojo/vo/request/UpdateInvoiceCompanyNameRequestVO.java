package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id: UpdateInvoiceCompanyNameRequestVO.java, v 0.1 2024/5/14 上午10:23 JiangYuHong Exp $
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UpdateInvoiceCompanyNameRequestVO extends BaseVO {

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "20220101", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "企业编码不能为空")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "上海测试公司", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "企业名称不能为空")
    private String companyName;

}
