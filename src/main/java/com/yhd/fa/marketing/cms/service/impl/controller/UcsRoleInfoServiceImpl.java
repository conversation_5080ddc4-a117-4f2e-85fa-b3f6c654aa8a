package com.yhd.fa.marketing.cms.service.impl.controller;


import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.service.controller.UcsRoleInfoService;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.ucs.foreign.pojo.vo.request.RoleInfoRequest;
import com.yhd.ucs.foreign.service.ForeignYhducsRoleInfoService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: UcsRoleInfoServiceImpl.java, v 0.1 2025/8/4 08:57 JiangYuHong Exp $
 */
@Service
public class UcsRoleInfoServiceImpl implements UcsRoleInfoService {

    private static final Logger logger = LogUtils.getLogger(UcsRoleInfoServiceImpl.class.getName());

    @Resource
    private ForeignYhducsRoleInfoService foreignClient;

    /**
     * 获取用户所有角色编码
     *
     * @return List
     */
    @Override
    public List<String> getUserAllRoleCodeList() {
        List<String> allRoleCodeList = new ArrayList<>();

        try {
            RoleInfoRequest roleInfoRequest = new RoleInfoRequest();
            roleInfoRequest.setEmplCode(SecurityUtil.getAccountNo());
            BusinessResponse businessResponse = foreignClient.getAllRolesByEmplAndAppId(roleInfoRequest);

            if (businessResponse.success()) {
                Object data = businessResponse.getData();
                if (data instanceof List) {
                    allRoleCodeList = ((List<?>) data).stream()
                            .filter(Objects::nonNull)  // 过滤null值
                            .map(item -> {
                                try {
                                    return item instanceof String ? (String) item : String.valueOf(item);
                                } catch (Exception e) {
                                    logger.warn("Failed to convert role item to string: {}", item, e);
                                    return null;
                                }
                            })
                            .filter(Objects::nonNull)  // 再次过滤转换失败的项
                            .filter(roleCode -> !roleCode.trim().isEmpty())  // 过滤空字符串
                            .collect(Collectors.toList());
                } else {
                    logger.warn("Response data is not a List, actual type: {}",
                            data != null ? data.getClass().getSimpleName() : "null");
                }
            } else {
                logger.warn("Failed to get roles, response: {}", businessResponse.getRt_msg());
            }

        } catch (Exception e) {
            logger.error("Error occurred while getting user roles for employee: {}",
                    SecurityUtil.getAccountNo(), e);
        }

        return allRoleCodeList;
    }
}
