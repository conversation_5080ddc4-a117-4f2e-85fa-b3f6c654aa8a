package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.dao.OrderAfterSaleDAO;
import com.yhd.fa.marketing.cms.enums.OrderAfterSaleStatusEnum;
import com.yhd.fa.marketing.cms.enums.OrderAfterSaleTypeEnum;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderAfterSalePO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderAfterSaleRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.SqlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderAfterSaleListLogic.java, v0.1 2023/2/24 15:08 yehuasheng Exp $
 */
@Component
public class GetOrderAfterSaleListLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderAfterSaleListLogic.class.getName());

    @Resource
    private OrderAfterSaleDAO orderAfterSaleDAO;

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 执行获取订单售后列表
     *
     * @param orderAfterSaleRequestVO 订单售后请求参数
     * @return BusinessResponse<PageInfo < OrderAfterSaleResponseVO>>
     */
    public BusinessResponse<PageInfo<OrderAfterSaleResponseVO>> exec(OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("start exec get order after sale list logic.");

        // 设置分页参数
        PageMethod.startPage(orderAfterSaleRequestVO.getPageNum(), orderAfterSaleRequestVO.getPageSize());

        // 设置查询字段以及条件
        MPJLambdaWrapper<OrderAfterSalePO> queryWrapper = setOrderAfterSaleQueryWrapper(orderAfterSaleRequestVO);

        // 查询订单售后列表
        List<OrderAfterSaleResponseVO> orderAfterSaleList = orderAfterSaleDAO.selectJoinList(OrderAfterSaleResponseVO.class, queryWrapper);

        // 设置分页
        PageInfo<OrderAfterSaleResponseVO> pageInfo = new PageInfo<>(orderAfterSaleList);

        // 设置订单售后其他值
        setOrderAfterSaleOtherValue(pageInfo.getList());

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 设置查询字段以及条件
     *
     * @param orderAfterSaleRequestVO 请求参数
     * @return MPJLambdaWrapper<OrderAfterSalePO>
     */
    private MPJLambdaWrapper<OrderAfterSalePO> setOrderAfterSaleQueryWrapper(OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("start set order after sale quotation wrapper.");

        // 获取订单售后查询条件以及字段
        MPJLambdaWrapper<OrderAfterSalePO> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(OrderAfterSalePO.class);
        queryWrapper.selectAs(OrderAfterSalePO::getId, OrderAfterSaleResponseVO::getOrderAfterSaleId);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderAfterSalePO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 判断是否有设置时间
        setTimeQueryWrapper(queryWrapper, orderAfterSaleRequestVO);

        Optional.ofNullable(orderAfterSaleRequestVO.getReplyStatus())
                .filter(StringUtils::isNotBlank)
                .ifPresent(replyStatus -> queryWrapper.eq(OrderAfterSalePO::getReplyStatus, replyStatus));

        // 查询订单售后单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderAfterSaleRequestVO.getOrderAfterSaleNumber(), queryWrapper, OrderAfterSalePO::getAfterSaleNumber);
        // 查询订单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderAfterSaleRequestVO.getOrderNumber(), queryWrapper, OrderAfterSalePO::getOrderNumber);
        // 查询企业名称
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderAfterSaleRequestVO.getCompanyName(), queryWrapper, OrderAfterSalePO::getCompanyName);
        // 查询订单售后状态
        setOrderAfterSaleStatusQueryWrapper(queryWrapper, orderAfterSaleRequestVO);

        // 查询订单售后类型
        setOrderAfterSaleTypeQueryWrapper(queryWrapper, orderAfterSaleRequestVO);

        // 查询订单同步状态
        setOrderAfterSaleSynchronizationStatusQueryWrapper(queryWrapper, orderAfterSaleRequestVO);

        queryWrapper.orderByDesc(OrderAfterSalePO::getCreatedDate);
        return queryWrapper;
    }

    /**
     * 设置时间
     *
     * @param queryWrapper            查询条件
     * @param orderAfterSaleRequestVO 请求参数
     */
    private void setTimeQueryWrapper(MPJLambdaWrapper<OrderAfterSalePO> queryWrapper, OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("set time query wrapper.");

        // 开始时间
        if (ObjectUtil.isNotNull(orderAfterSaleRequestVO.getStartDateTime())) {
            queryWrapper.ge(OrderAfterSalePO::getCreatedDate, orderAfterSaleRequestVO.getStartDateTime());
        }

        // 结束时间
        if (ObjectUtil.isNotNull(orderAfterSaleRequestVO.getEndDateTime())) {
            queryWrapper.le(OrderAfterSalePO::getCreatedDate, orderAfterSaleRequestVO.getEndDateTime());
        }
    }

    /**
     * 设置查询订单售后状态
     *
     * @param queryWrapper            查询条件
     * @param orderAfterSaleRequestVO 请求参数
     */
    private void setOrderAfterSaleStatusQueryWrapper(MPJLambdaWrapper<OrderAfterSalePO> queryWrapper, OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("set order after sale status query wrapper.");

        if (StringUtils.isNotBlank(orderAfterSaleRequestVO.getOrderAfterSaleStatus())) {
            queryWrapper.eq(OrderAfterSalePO::getAfterSaleStatus, orderAfterSaleRequestVO.getOrderAfterSaleStatus());
        }
    }

    /**
     * 设置查询订单售后类型
     *
     * @param queryWrapper            查询条件
     * @param orderAfterSaleRequestVO 请求参数
     */
    private void setOrderAfterSaleTypeQueryWrapper(MPJLambdaWrapper<OrderAfterSalePO> queryWrapper, OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("set order after sale type query wrapper.");

        if (StringUtils.isNotBlank(orderAfterSaleRequestVO.getAfterSaleType())) {
            queryWrapper.eq(OrderAfterSalePO::getAfterSaleType, orderAfterSaleRequestVO.getAfterSaleType());
        }
    }

    /**
     * 设置查询订单售后同步状态
     *
     * @param queryWrapper            查询条件
     * @param orderAfterSaleRequestVO 请求参数
     */
    private void setOrderAfterSaleSynchronizationStatusQueryWrapper(MPJLambdaWrapper<OrderAfterSalePO> queryWrapper, OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("set order after sale synchronization status query wrapper.");

        if (StringUtils.isNotBlank(orderAfterSaleRequestVO.getSynchronizationStatus())) {
            if (StringUtils.equals(orderAfterSaleRequestVO.getSynchronizationStatus(), CommonConstant.TRUE)) {
                queryWrapper.eq(OrderAfterSalePO::getSynchronizationStatus, FaDocMarketingCmsConstant.SUCCESS_STRING);
            } else {
                queryWrapper.ne(OrderAfterSalePO::getSynchronizationStatus, FaDocMarketingCmsConstant.SUCCESS_STRING);
            }
        }
    }

    /**
     * 设置订单售后其他值
     *
     * @param orderAfterSaleList 订单售后列表
     */
    private void setOrderAfterSaleOtherValue(List<OrderAfterSaleResponseVO> orderAfterSaleList) {
        logger.info("set order after sale other value.");

        if (CollUtil.isNotEmpty(orderAfterSaleList)) {
            // 获取用户信息
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userBaseInfo = getUserBaseInfo(orderAfterSaleList);

            // 订单售后状态
            Map<String, String> orderAfterSaleStatusMap = Arrays.stream(OrderAfterSaleStatusEnum.values()).collect(Collectors.toMap(OrderAfterSaleStatusEnum::getOrderAfterSaleStatus, OrderAfterSaleStatusEnum::getOrderAfterSaleStatusName));

            // 订单售后类型
            Map<String, String> orderAfterSaleTypeMap = Arrays.stream(OrderAfterSaleTypeEnum.values()).collect(Collectors.toMap(OrderAfterSaleTypeEnum::getOrderAfterSaleType, OrderAfterSaleTypeEnum::getOrderAfterSaleTypeName));

            orderAfterSaleList.forEach(orderAfterSaleResponseVO -> {
                Optional.ofNullable(userBaseInfo.get(orderAfterSaleResponseVO.getUserCode())).ifPresent(userAndMerchandiserAndResourcesResponseVO -> {
                    // 设置业务员的信息
                    Optional.ofNullable(userAndMerchandiserAndResourcesResponseVO.getSalesManInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(salesMan -> {
                        orderAfterSaleResponseVO.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
                        orderAfterSaleResponseVO.setSalesmanContact(salesMan.getMobile());
                    });
                    // 设置跟单和业务员
                    Optional.ofNullable(userAndMerchandiserAndResourcesResponseVO.getMerchandiserInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(merchandiserResponseVO -> {
                        orderAfterSaleResponseVO.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
                        orderAfterSaleResponseVO.setMerchandiserContact(merchandiserResponseVO.getMobile());
                    });
                });

                // 设置订单售后状态名称
                orderAfterSaleResponseVO.setAfterSaleStatusName(orderAfterSaleStatusMap.get(orderAfterSaleResponseVO.getAfterSaleStatus()));

                // 设置订单售后类型名称
                orderAfterSaleResponseVO.setAfterSaleTypeName(orderAfterSaleTypeMap.get(orderAfterSaleResponseVO.getAfterSaleType()));
            });
        }
    }

    /**
     * 获取用户信息
     *
     * @param orderAfterSaleList 订单售后列表
     * @return Map<String, BaseUserInfoResponseVO>
     */
    private Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserBaseInfo(List<OrderAfterSaleResponseVO> orderAfterSaleList) {
        logger.info("get order list user info.");

        // 提取订单列表中的用户编码
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = orderAfterSaleList
                .stream()
                .map(orderAfterSaleResponseVO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .companyCode(orderAfterSaleResponseVO.getCompanyCode())
                        .userCode(orderAfterSaleResponseVO.getUserCode())
                        .build()).collect(Collectors.toList());

        // 获取用户信息map
        return userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);
    }
}
