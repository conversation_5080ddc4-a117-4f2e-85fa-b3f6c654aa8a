package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_comments")
public class OrderCommentsPO extends BaseEntity {
    /**
     * 订单UUID
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 评价用户编码
     */
    private String userCode;

    /**
     * 评价用户手机号
     */
    private String phone;

    /**
     * 评价用户邮箱
     */
    private String email;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司名称首字母
     */
    private String companyNameInitial;

    /**
     * 产品描述相符度
     */
    private Integer productDescRating;

    /**
     * 人员服务态度
     */
    private Integer personnelServiceRating;

    /**
     * 产品交付时效
     */
    private Integer productDeliveryRating;

    /**
     * 文字评论
     */
    private String comment;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 赠送积分
     */
    private Integer bonusPoints;

    /**
     * 回复
     */
    private String reply;

    /**
     * 是否已回复
     */
    private String replied;

    /**
     * 回复时间
     */
    private LocalDateTime repliedDate;

    /**
     * 回复人
     */
    private String replyPeople;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "已生成工单")
    private String remark;

    /**
     * 回访记录
     */
    private String visitRecords;
}

