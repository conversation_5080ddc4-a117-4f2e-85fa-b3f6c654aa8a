package com.yhd.fa.marketing.cms.service.impl.logic.order;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.dao.OrderCommentsProblemCategoriesDAO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsProblemCategoriesPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProblemCategoriesListResponseVO;
import com.yhd.redis.service.RedisService;
import jodd.util.StringUtil;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsProblemCategoriesLogic.java, v 0.1 2025/5/16 11:58 JiangYuHong Exp $
 */
@Component
public class OrderCommentsProblemCategoriesLogic {

    public static final String PROBLEM_KEY_NAME = "ORDER_COMMENTS_PROBLEM_CATEGORIES_LIST";
    private static final Logger logger = LogUtils.getLogger(OrderCommentsProblemCategoriesLogic.class.getName());
    @Resource
    private OrderCommentsProblemCategoriesDAO orderCommentsProblemCategoriesDAO;

    @Resource
    private RedisService redisService;

    /**
     * 获取订单评论问题分类列表
     *
     * @return BusinessResponse
     */
    public BusinessResponse<List<ProblemCategoriesListResponseVO>> orderCommentsProblemCategoriesList() {

        logger.info("start get order comments problem categories list logic.");

        // 从Redis获取
        String s = redisService.get(PROBLEM_KEY_NAME);
        if (StringUtil.isNotBlank(s)) {
            List<ProblemCategoriesListResponseVO> categoriesListResponseVOS = JSON.parseArray(s, ProblemCategoriesListResponseVO.class);
            return BusinessResponse.ok(categoriesListResponseVOS);
        }

        List<OrderCommentsProblemCategoriesPO> categoriesPOList = orderCommentsProblemCategoriesDAO.selectList(new LambdaQueryWrapper<>());

        List<ProblemCategoriesListResponseVO> problemCategoriesListResponseVO = BeanUtil.copyToList(categoriesPOList, ProblemCategoriesListResponseVO.class);

        // 构建一个 map，方便后面查找子节点
        Map<Integer, ProblemCategoriesListResponseVO> categoryMap = problemCategoriesListResponseVO.stream().collect(Collectors.toMap(ProblemCategoriesListResponseVO::getId, Function.identity()));

        List<ProblemCategoriesListResponseVO> rootCategories = new ArrayList<>();

        for (ProblemCategoriesListResponseVO category : problemCategoriesListResponseVO) {
            if (category.getParentId() == null || category.getParentId() == 0) {
                // 一级分类
                rootCategories.add(category);
            } else {
                // 二级分类，找到父级并添加到 children 列表中
                ProblemCategoriesListResponseVO parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(category);
                }
            }
        }

        // 设置缓存
        redisService.setExpire(PROBLEM_KEY_NAME, JSON.toJSONString(rootCategories), CommonConstant.ONE, TimeUnit.DAYS);

        return BusinessResponse.ok(rootCategories);
    }
}
