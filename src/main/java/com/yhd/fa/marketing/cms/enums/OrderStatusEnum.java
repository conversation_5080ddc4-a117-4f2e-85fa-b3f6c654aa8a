package com.yhd.fa.marketing.cms.enums;

import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.constant.OrderStatusColorConstant;
import com.yhd.fa.marketing.cms.constant.OrderStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderStatusEnum.java, v0.1 2023/2/20 8:25 yehuasheng Exp $
 */
@Getter
public enum OrderStatusEnum {
    ALL(CommonConstant.EMPTY, "全部", ""),
    UNPAID(OrderStatusConstant.UNPAID, "待支付", OrderStatusColorConstant.ORANGE),
    CLOSED(OrderStatusConstant.CLOSED, "已终止", OrderStatusColorConstant.GRAY),
    CANCEL(OrderStatusConstant.CANCEL, "已取消", OrderStatusColorConstant.GRAY),
    FINISH(OrderStatusConstant.FINISH, "已完成", OrderStatusColorConstant.GREEN),
    ON_WAY(OrderStatusConstant.ON_WAY, "进行中", OrderStatusColorConstant.ORANGE),
    ;

    private final String orderStatus;
    private final String orderStatusCn;
    private final String orderStatusColor;

    OrderStatusEnum(String orderStatus, String orderStatusCn, String orderStatusColor) {
        this.orderStatus = orderStatus;
        this.orderStatusCn = orderStatusCn;
        this.orderStatusColor = orderStatusColor;
    }
}
