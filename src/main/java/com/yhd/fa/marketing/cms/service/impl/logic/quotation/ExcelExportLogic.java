package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.google.common.base.Stopwatch;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryListExportResponseVO;
import com.yhd.fa.marketing.cms.util.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryExportResponseVO;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

@Component
public class ExcelExportLogic {

    /**
     * 字符长度
     */
    public static final int STRING_LENGTH = 51;
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(ExcelExportLogic.class.getName());


    /**
     * 导出Excel
     *
     * @param enquiryExportResponseVO 导出的价单数据
     * @param response              返回的内容
     */
    public void exportExcel(EnquiryExportResponseVO enquiryExportResponseVO, HttpServletResponse response) {
        logger.info("start export excel logic.");

        ExportParams exportParams = new ExportParams();
        exportParams.setTitleHeight((short) 20);
        exportParams.setStyle(ExcelExportImpl.class);
        exportParams.setColor(IndexedColors.BLACK.index);
        try (Workbook workbook = ExcelExportUtil.exportExcel(exportParams, EnquiryListExportResponseVO.class,enquiryExportResponseVO.getList())) {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();

            // 设置响应输出的头类型及下载文件的默认名称
            setExportExcelResponseHeader(response, "enquiryLog");


            workbook.write(response.getOutputStream());
            logger.info("export excel success. cots:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            logger.error("export excel fail quotation detail data errorMsg:{}", e.getMessage());
        }
    }

    /**
     * 设置响应输出的头类型及下载文件的默认名称
     * @param response 响应
     * @param fileName 文件名
     */
    private void setExportExcelResponseHeader(HttpServletResponse response, String fileName) {
        try {
            // 清空输出流
            response.reset();
            response.setCharacterEncoding(CommonConstant.UTF8);
            response.setContentType("application/vnd.ms-excel;charset=utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        } catch (Exception ex) {
            logger.error("set response header error:{}", ex.getMessage());
        }
    }
}
