package com.yhd.fa.marketing.cms.service.impl.sao;

import com.alibaba.fastjson2.JSON;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import com.yhd.fa.marketing.cms.sao.UnifiedPushMessageSAO;
import com.yhd.fa.marketing.cms.service.sao.UnifiedPushMessageService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: UnifiedPushMessageServiceImpl.java, v0.1 2023/3/7 9:03 yehuasheng Exp $
 */
@Service
public class UnifiedPushMessageServiceImpl implements UnifiedPushMessageService {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(UnifiedPushMessageServiceImpl.class.getName());

    /**
     * 推送消息sao
     */
    @Resource
    private UnifiedPushMessageSAO unifiedPushMessageSAO;

    /**
     * 发送钉钉告警
     *
     * @param templateId     模板id
     * @param receivers      钉钉发送的人
     * @param replaceContent 替换的内容
     * @param atAll          是否@所有人 true是 false否
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<String> sendDingDingRobot(String templateId, List<String> receivers, Map<String, Object> replaceContent, String atAll) {
        logger.info("start send ding ding robot for warn service.");

        try {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 设置请求参数
            SendMessageToDingDingRobotRequestVO sendMessageToDingDingRobotRequestVO = SendMessageToDingDingRobotRequestVO
                    .builder()
                    .templateId(templateId)
                    .receivers(receivers)
                    .map(replaceContent)
                    .atAll(atAll)
                    .build();
            logger.info("send ding ding robot message for pushMessage service parameter sendMessageToDingDingRobotRequestVO:{}", sendMessageToDingDingRobotRequestVO);
            // 请求接口
            BusinessResponse<String> businessResponse = unifiedPushMessageSAO.sendDingDingWarn(sendMessageToDingDingRobotRequestVO);
            logger.info("send ding ding robot message result businessResponse:{}", businessResponse);
            logger.info("send ding ding robot message cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("send ding ding robot message fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.SEND_DING_DING_MESSAGE_FAIL);
        }
    }

    /**
     * 发送钉钉多对多
     *
     * @param templateId     模板id
     * @param receivers      钉钉发送的人
     * @param replaceContent 替换的内容
     * @return BusinessResponse<String>
     */
    @Override
    public BusinessResponse<String> sendDingDingManyToMany(String templateId, List<String> receivers, List<Map<String, Object>> replaceContent) {
        logger.info("start send ding ding many to many service.");

        try {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 设置请求参数
            SendMessageToDingDingManyRequestVO sendMessageToDingDingManyRequestVO = SendMessageToDingDingManyRequestVO
                    .builder()
                    .templateId(templateId)
                    .receivers(receivers)
                    .map(replaceContent.stream().map(JSON::toJSONString).collect(Collectors.toList()))
                    .build();
            logger.info("send ding ding to many message for pushMessage service parameter sendMessageToDingDingManyRequestVO:{}", sendMessageToDingDingManyRequestVO);
            // 请求接口
            BusinessResponse<String> businessResponse = unifiedPushMessageSAO.sendDingDingMany(sendMessageToDingDingManyRequestVO);
            logger.info("send ding ding to many message result businessResponse:{}", businessResponse);
            logger.info("send ding ding to many message cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("send ding ding to many message fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.SEND_DING_DING_MESSAGE_FAIL);
        }
    }

    /**
     * 发送钉钉一对多
     *
     * @param templateId     模板id
     * @param receivers      钉钉发送的人
     * @param replaceContent 替换的内容
     * @return BusinessResponse<String>
     */
    @Override
    public BusinessResponse<String> sendDingDingOneToMany(String templateId, List<String> receivers, Map<String, Object> replaceContent) {
        logger.info("start send ding ding one to many service.");

        try {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 设置请求参数
            SendMessageToDingDingRequestVO sendMessageToDingDingRequestVO = SendMessageToDingDingRequestVO
                    .builder()
                    .templateId(templateId)
                    .receivers(receivers)
                    .map(replaceContent)
                    .build();
            logger.info("send ding ding one to many message for pushMessage service parameter sendMessageToDingDingRequestVO:{}", sendMessageToDingDingRequestVO);
            // 请求接口
            BusinessResponse<String> businessResponse = unifiedPushMessageSAO.sendDingDingOneToMany(sendMessageToDingDingRequestVO);
            logger.info("send ding ding one to many message result businessResponse:{}", businessResponse);
            logger.info("send ding ding one to many message cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("send ding ding one to many message fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.SEND_DING_DING_MESSAGE_FAIL);
        }
    }

    /**
     * 发送短信多对多
     *
     * @param templateId     模板id
     * @param phoneNumber    手机号码
     * @param replaceContent 短信多对多消息推送传入
     * @param sign           短信签名，当短信为多对多发送时，有多少个手机号，传多少个签名；
     * @return BusinessResponse<String>
     */
    @Override
    public BusinessResponse<String> sendSmsToMany(String templateId, List<String> phoneNumber, List<Map<String, Object>> replaceContent, List<String> sign) {
        logger.info("start send sms to many service.");

        try {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 设置请求参数
            SendMessageToSmsManyRequestVO sendMessageToSmsManyRequestVO = SendMessageToSmsManyRequestVO
                    .builder()
                    .templateId(templateId)
                    .phoneNumber(phoneNumber)
                    .map(replaceContent.stream().map(JSON::toJSONString).collect(Collectors.toList()))
                    .sign(sign)
                    .build();
            logger.info("send sms to many message for pushMessage service parameter sendMessageToSmsManyRequestVO:{}", sendMessageToSmsManyRequestVO);
            // 请求接口
            BusinessResponse<String> businessResponse = unifiedPushMessageSAO.sendSmsToMany(sendMessageToSmsManyRequestVO);
            logger.info("send sms to many message result businessResponse:{}", businessResponse);
            logger.info("send sms to many message cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("send sms to many message fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.SEND_SMS_MESSAGE_FAIL);
        }
    }

    /**
     * 发送短信
     *
     * @param templateId     模板id
     * @param phoneNumber    手机号码
     * @param replaceContent 短信多对多消息推送传入
     * @param sign           短信签名
     * @return BusinessResponse<String>
     */
    @Override
    public BusinessResponse<String> sendSms(String templateId, List<String> phoneNumber, Map<String, Object> replaceContent, String sign) {
        logger.info("start send sms service.");

        try {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 设置请求参数
            SendMessageToSmsRequestVO sendMessageToSmsRequestVO = SendMessageToSmsRequestVO
                    .builder()
                    .templateId(templateId)
                    .phoneNumber(phoneNumber)
                    .map(replaceContent)
                    .sign(sign)
                    .build();
            logger.info("send sms message for pushMessage service parameter sendMessageToSmsRequestVO:{}", sendMessageToSmsRequestVO);
            // 请求接口
            BusinessResponse<String> businessResponse = unifiedPushMessageSAO.sendSms(sendMessageToSmsRequestVO);
            logger.info("send sms message result businessResponse:{}", businessResponse);
            logger.info("send sms message cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("send sms message fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.SEND_SMS_MESSAGE_FAIL);
        }
    }

    /**
     * 发送邮件 多对多
     *
     * @param templateId     模板id
     * @param emailAddress   邮箱
     * @param replaceContent 内容
     * @param type           邮件类型：order订单，quotation报价单，other其他；订单报价单邮件包含明细项时传入order/quotation，不包含明细项直接传other
     * @return BusinessResponse<String>
     */
    @Override
    public BusinessResponse<String> sendEmailToMany(String templateId, List<String> emailAddress, List<Map<String, Object>> replaceContent, String type) {
        logger.info("start send many to email service.");

        try {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 设置请求参数
            SendMessageToEmailManyRequestVO sendMessageToEmailManyRequestVO = SendMessageToEmailManyRequestVO
                    .builder()
                    .templateId(templateId)
                    .emailAddress(emailAddress)
                    .map(replaceContent)
                    .type(type)
                    .build();
            logger.info("send email to many message for pushMessage service parameter sendMessageToEmailManyRequestVO:{}", sendMessageToEmailManyRequestVO);
            // 请求接口
            BusinessResponse<String> businessResponse = unifiedPushMessageSAO.sendEmailToMany(sendMessageToEmailManyRequestVO);
            logger.info("send email to many message result businessResponse:{}", businessResponse);
            logger.info("send email to many message cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("send email to many message fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.SEND_EMAIL_MESSAGE_FAIL);
        }
    }

    /**
     * 发送邮件
     *
     * @param templateId     模板id
     * @param emailAddress   邮箱
     * @param replaceContent 数据
     * @param type           邮件类型：order订单，quotation报价单，other其他；订单报价单邮件包含明细项时传入order/quotation，不包含明细项直接传other
     * @return BusinessResponse<String>
     */
    @Override
    public BusinessResponse<String> sendEmail(String templateId, List<String> emailAddress, Map<String, Object> replaceContent, String type) {
        logger.info("start send email service.");

        try {
            // 设置时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 设置请求参数
            SendMessageToEmailRequestVO sendMessageToEmailRequestVO = SendMessageToEmailRequestVO
                    .builder()
                    .templateId(templateId)
                    .emailAddress(emailAddress)
                    .map(replaceContent)
                    .type(type)
                    .build();
            logger.info("send email message for pushMessage service parameter sendMessageToEmailRequestVO:{}", sendMessageToEmailRequestVO);
            // 请求接口
            BusinessResponse<String> businessResponse = unifiedPushMessageSAO.sendEmail(sendMessageToEmailRequestVO);
            logger.info("send email message result businessResponse:{}", businessResponse);
            logger.info("send email message cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("send email message fail. errorMsg:{}", e.getMessage());
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.SEND_EMAIL_MESSAGE_FAIL);
        }
    }
}
