package com.yhd.fa.marketing.cms.service.impl.verification.quotation;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationInfoRequestVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: GetQuotationListVerification.java, v0.1 2022/12/2 14:07 yehuasheng Exp $
 */
@Component
public class GetQuotationListVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetQuotationListVerification.class.getName());

    /**
     * 检查参数
     *
     * @param quotationInfoRequestVO 查下报价单列表的参数
     * @param <T>                    T
     * @return BusinessResponse
     */
    public <T> BusinessResponse<T> check(QuotationInfoRequestVO quotationInfoRequestVO) {
        logger.info("check get quotation list parameter.");

        // 设置默认当前页码以及每页显示20条
        if (quotationInfoRequestVO.getPageNum() <= CommonConstant.ZERO) {
            quotationInfoRequestVO.setPageNum(CommonConstant.ONE);
        }
        if (quotationInfoRequestVO.getPageSize() <= CommonConstant.ZERO) {
            quotationInfoRequestVO.setPageSize(20);
        }

        return BusinessResponseCommon.ok(null);
    }
}
