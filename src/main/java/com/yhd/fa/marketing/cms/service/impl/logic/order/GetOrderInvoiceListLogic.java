package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.OrderInvoiceNatureEnum;
import com.yhd.fa.marketing.cms.enums.OrderInvoiceStatusEnum;
import com.yhd.fa.marketing.cms.enums.OrderInvoiceTypeEnum;
import com.yhd.fa.marketing.cms.mapper.OrderInvoiceMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderInvoicePO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderInvoiceRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.SqlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderInvoiceListLogic.java, v0.1 2023/2/23 11:12 yehuasheng Exp $
 */
@Component
public class GetOrderInvoiceListLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderInvoiceListLogic.class.getName());

    /**
     * 订单发票mapper
     */
    @Resource
    private OrderInvoiceMapper orderInvoiceMapper;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 执行查询订单发票列表
     *
     * @param orderInvoiceRequestVO 订单发票请求参数
     * @return BusinessResponse
     */
    public BusinessResponse<PageInfo<OrderInvoiceResponseVO>> exec(OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("start exec get order invoice list.");

        // 设置分页参数
        PageMethod.startPage(orderInvoiceRequestVO.getPageNum(), orderInvoiceRequestVO.getPageSize());

        // 设置查询条件
        MPJLambdaWrapper<OrderInvoicePO> queryWrapper = setOrderInvoiceWrapper(orderInvoiceRequestVO);

        if (null == queryWrapper) {
            return BusinessResponse.ok(PageInfo.of(new ArrayList<>()));
        }

        // 查询列表
        List<OrderInvoiceResponseVO> orderInvoiceResponseList = orderInvoiceMapper.selectJoinList(OrderInvoiceResponseVO.class, queryWrapper);

        // 设置分页
        PageInfo<OrderInvoiceResponseVO> pageInfo = new PageInfo<>(orderInvoiceResponseList);

        // 设置订单发票其他值
        setOrderInvoiceOtherValue(pageInfo.getList());

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 设置查询字段以及条件
     *
     * @param orderInvoiceRequestVO 订单发票请求参数
     * @return MPJLambdaWrapper<OrderInvoicePO>
     */
    private MPJLambdaWrapper<OrderInvoicePO> setOrderInvoiceWrapper(OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("set order invoice wrapper.");

        // 设置查询的字段
        MPJLambdaWrapper<OrderInvoicePO> queryWrapper = OrderUtil.setOrderInvoiceQueryWrapperSelectAs()
                .orderByDesc(OrderInvoicePO::getCreatedDate);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderInvoicePO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 设置时间
        setTimeQueryWrapper(queryWrapper, orderInvoiceRequestVO);

        // 判断查询是否发票单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderInvoiceRequestVO.getOrderInvoiceNumber(), queryWrapper, OrderInvoicePO::getInvoiceNumber);

        // 企业名称
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderInvoiceRequestVO.getCompanyName(), queryWrapper, OrderInvoicePO::getInvCustomerName);

        // 设置发票状态
        setOrderInvoiceStatusQueryWrapper(queryWrapper, orderInvoiceRequestVO);

        // 设置同步状态
        setOrderInvoiceSynchronizationStatusQueryWrapper(queryWrapper, orderInvoiceRequestVO);

        // 返回
        return queryWrapper;
    }

    /**
     * 设置查询时间
     *
     * @param queryWrapper          查询条件
     * @param orderInvoiceRequestVO 订单发票参数
     */
    private void setTimeQueryWrapper(MPJLambdaWrapper<OrderInvoicePO> queryWrapper, OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("get order list set time query wrapper.");

        // 开始时间
        if (ObjectUtil.isNotNull(orderInvoiceRequestVO.getStartDateTime())) {
            queryWrapper.ge(OrderInvoicePO::getCreatedDate, orderInvoiceRequestVO.getStartDateTime());
        }

        // 结束时间
        if (ObjectUtil.isNotNull(orderInvoiceRequestVO.getEndDateTime())) {
            queryWrapper.le(OrderInvoicePO::getCreatedDate, orderInvoiceRequestVO.getEndDateTime());
        }
    }

    /**
     * 设置发票状态
     *
     * @param queryWrapper          查询条件
     * @param orderInvoiceRequestVO 订单发票参数
     */
    private void setOrderInvoiceStatusQueryWrapper(MPJLambdaWrapper<OrderInvoicePO> queryWrapper, OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("set order invoice status query wrapper.");

        if (StringUtils.isNotBlank(orderInvoiceRequestVO.getOrderInvoiceStatus())) {
            queryWrapper.eq(OrderInvoicePO::getInvoiceStatus, orderInvoiceRequestVO.getOrderInvoiceStatus());
        }
    }

    /**
     * 设置同步状态查询
     *
     * @param queryWrapper          查询条件
     * @param orderInvoiceRequestVO 订单发票参数
     */
    private void setOrderInvoiceSynchronizationStatusQueryWrapper(MPJLambdaWrapper<OrderInvoicePO> queryWrapper, OrderInvoiceRequestVO orderInvoiceRequestVO) {
        logger.info("set order invoice synchronization status query wrapper.");

        if (StringUtils.isNotBlank(orderInvoiceRequestVO.getSynchronizationStatus())) {
            if (StringUtils.equals(CommonConstant.TRUE, orderInvoiceRequestVO.getSynchronizationStatus())) {
                queryWrapper.eq(OrderInvoicePO::getSendErpStatus, FaDocMarketingCmsConstant.SUCCESS_STRING);
            } else {
                queryWrapper.ne(OrderInvoicePO::getSendErpStatus, FaDocMarketingCmsConstant.SUCCESS_STRING);
            }
        }
    }

    /**
     * 设置发票其他值
     *
     * @param orderInvoiceList 发票集合
     */
    private void setOrderInvoiceOtherValue(List<OrderInvoiceResponseVO> orderInvoiceList) {
        logger.info("set order invoice other value");

        if (CollUtil.isNotEmpty(orderInvoiceList)) {
            // 获取用户
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userBaseInfo = getUserBaseInfo(orderInvoiceList);

            // 设置发票状态
            Map<String, String> orderInvoiceStatusMap = Arrays.stream(OrderInvoiceStatusEnum.values()).collect(Collectors.toMap(OrderInvoiceStatusEnum::getOrderInvoiceStatus, OrderInvoiceStatusEnum::getOrderInvoiceStatusName));

            // 设置发票类型
            Map<String, String> orderInvoiceTypeMap = Arrays.stream(OrderInvoiceTypeEnum.values()).collect(Collectors.toMap(OrderInvoiceTypeEnum::getOrderInvoiceType, OrderInvoiceTypeEnum::getOrderInvoiceTypeName));

            // 设置发票性质
            Map<String, String> orderInvoiceNatureMap = Arrays.stream(OrderInvoiceNatureEnum.values()).collect(Collectors.toMap(OrderInvoiceNatureEnum::getOrderInvoiceNature, OrderInvoiceNatureEnum::getOrderInvoiceNatureName));

            orderInvoiceList.forEach(orderInvoiceResponseVO -> {

                // 设置用户信息
                Optional.ofNullable(userBaseInfo.get(orderInvoiceResponseVO.getUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
                    // 设置跟单的信息
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserResponseVO -> {
                        orderInvoiceResponseVO.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
                        orderInvoiceResponseVO.setMerchandiserContact(merchandiserResponseVO.getMobile());
                    });

                    // 设置用户的名字
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getUserInfo()).ifPresent(baseUserInfoResponseVO -> orderInvoiceResponseVO.setUserName(baseUserInfoResponseVO.getUserName()));

                    // 设置业务员的信息
                    Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> {
                        orderInvoiceResponseVO.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
                        orderInvoiceResponseVO.setSalesmanContact(salesMan.getMobile());
                    });
                });

                // 设置状态名
                orderInvoiceResponseVO.setOrderInvoiceStatusName(orderInvoiceStatusMap.get(orderInvoiceResponseVO.getOrderInvoiceStatus()));
                // 设置发票状态名
                orderInvoiceResponseVO.setOrderInvoiceTypeName(orderInvoiceTypeMap.get(orderInvoiceResponseVO.getOrderInvoiceType()));
                // 设置发票性质名
                orderInvoiceResponseVO.setOrderInvoiceNatureName(orderInvoiceNatureMap.get(orderInvoiceResponseVO.getOrderInvoiceNature()));
            });
        }
    }

    /**
     * 获取用户信息
     *
     * @param orderInvoiceList 订单发票列表
     * @return Map<String, BaseUserInfoResponseVO>
     */
    private Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserBaseInfo(List<OrderInvoiceResponseVO> orderInvoiceList) {
        logger.info("get order list user info.");

        // 提取订单列表中的用户编码
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = orderInvoiceList
                .stream()
                .map(orderInvoiceResponseVO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .companyCode(orderInvoiceResponseVO.getCompanyCode())
                        .userCode(orderInvoiceResponseVO.getUserCode())
                        .build()).collect(Collectors.toList());

        // 获取用户信息map
        return userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);
    }
}
