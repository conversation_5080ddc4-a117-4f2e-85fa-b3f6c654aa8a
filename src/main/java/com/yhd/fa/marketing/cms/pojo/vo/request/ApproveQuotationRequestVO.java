package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationRequestVO.java, v0.1 2022/12/8 9:27 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveQuotationRequestVO extends PageRequestVO {
    /**
     * 报价单号
     */
    @Schema(description = "报价单号", example = "YB202211161438419779SXBQ")
    private String quotationNumber;
}
