package com.yhd.fa.marketing.cms.mapper.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.EnquiryLogDAO;
import com.yhd.fa.marketing.cms.mapper.EnquiryLogMapper;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogMapperImpl.java, v0.1 2022/12/6 10:28 yehuasheng Exp $
 */
@Service
@DS("ch")
public class EnquiryLogMapperImpl extends MPJBaseServiceImpl<EnquiryLogDAO, EnquiryLogPO> implements EnquiryLogMapper {
}
