package com.yhd.fa.marketing.cms.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogDAO.java, v0.1 2022/12/6 10:26 yehuasheng Exp $
 */
@Repository("enquiryLog")
@DS("ch")
public interface EnquiryLogDAO extends MPJBaseMapper<EnquiryLogPO> {
    void insertBatch(List<EnquiryLogPO> enquiryLogPOList);
}
