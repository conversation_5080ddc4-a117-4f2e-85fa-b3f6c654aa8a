package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: UploadFileResponseVO.java, v0.1 2022/8/26 11:52 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadFileResponseVO extends BaseVO {
    /**
     * 文件名
     */
    @Schema(description = "文件名")
    private String fileName;

    /**
     * 文件链接
     */
    @Schema(description = "文件链接")
    private String local;

    /**
     * code
     */
    @Schema(description = "code")
    private Integer code;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;
}
