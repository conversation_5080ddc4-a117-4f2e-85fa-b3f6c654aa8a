package com.yhd.fa.marketing.cms.mapper.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.SaleCompanyRelationDAO;
import com.yhd.fa.marketing.cms.dao.SaleMerchandiserRelationDAO;
import com.yhd.fa.marketing.cms.mapper.SaleCompanyRelationMapper;
import com.yhd.fa.marketing.cms.mapper.SaleMerchandiserRelationMapper;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleMerchandiserRelationPO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogMapperImpl.java, v0.1 2022/12/6 10:28 yehuasheng Exp $
 */
@Service
@DS("ch")
public class SaleMerchandiserRelationMapperImpl extends MPJBaseServiceImpl<SaleMerchandiserRelationDAO, SaleMerchandiserRelationPO> implements SaleMerchandiserRelationMapper {
}
