package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.HelpUserCancelOrderApplyRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCancelRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.HelpUserCancelOrderBasicsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelResponseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCancelService.java, v0.1 2023/2/22 9:55 yehuasheng Exp $
 */
public interface OrderCancelService {
    /**
     * 获取订单取消列表
     *
     * @param orderCancelRequestVO 订单取消列表参数
     * @return BusinessResponse<PageInfo < OrderCancelResponseVO>>
     */
    BusinessResponse<PageInfo<OrderCancelResponseVO>> getOrderCancelList(OrderCancelRequestVO orderCancelRequestVO);

    /**
     * 获取订单取消详情
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<OrderCancelDetailResponseVO>
     */
    BusinessResponse<OrderCancelDetailResponseVO> getOrderCancelInfo(String orderCancelId);

    /**
     * 同步订单取消
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<Void>
     */
    BusinessResponse<Void> synchronizationOrderCancel(String orderCancelId);

    /**
     * 取消订单初始化接口
     *
     * @param orderId 订单id
     * @return BusinessResponse
     */
    BusinessResponse<List<HelpUserCancelOrderBasicsResponseVO>> helpUserCancelOrderBasics(String orderId);

    /**
     * 申请取消订单
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    BusinessResponse<Object> helpUserCancelOrderApply(HelpUserCancelOrderApplyRequestVO requestVO);
}
