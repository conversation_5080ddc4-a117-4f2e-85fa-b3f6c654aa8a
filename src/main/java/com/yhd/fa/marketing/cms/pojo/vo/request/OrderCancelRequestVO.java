package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.OrderCancelStatusConstant;
import com.yhd.fa.marketing.cms.constant.OrderCancelTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderCancelRequestVO.java, v0.1 2023/2/22 9:23 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderCancelRequestVO extends PageRequestVO {
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-01-01 10:10:10")
    private LocalDateTime startDateTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-01-01 10:10:10")
    private LocalDateTime endDateTime;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "MYI202211221106059833DEVT")
    @Size(min = 2, message = "订单号至少输入2个字符")
    private String orderNumber;

    /**
     * 订单取消单号
     */
    @Schema(description = "订单取消单号", example = "SQ20221124102914QDJN")
    @Size(min = 2, message = "订单取消单号至少输入2个字符")
    private String orderCancelNumber;

    /**
     * 订单取消状态
     */
    @Schema(description = "订单取消状态 ''全部 canceling 取消中 processed 已处理 turnDown 驳回 refunded 已退款", example = "canceling")
    @ValueInEnum(matchTarget = {CommonConstant.EMPTY, OrderCancelStatusConstant.CANCELING, OrderCancelStatusConstant.PROCESSED, OrderCancelStatusConstant.REFUNDED, OrderCancelStatusConstant.TURN_DOWN}, message = "订单取消状态错误")
    private String orderCancelStatus;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    @Size(min = 2, message = "企业名称至少输入2个字符")
    private String companyName;

    /**
     * 内部人员工号
     */
    @Schema(description = "内部人员工号", example = "yhd575")
    private String insideEmployeeCode;

    /**
     * 订单取消类型
     */
    @Schema(description = "订单取消类型 part部分取消 all整单取消", example = "all")
    @ValueInEnum(matchTarget = {OrderCancelTypeConstant.ALL, OrderCancelTypeConstant.PART, CommonConstant.EMPTY}, message = "订单取消类型错误")
    private String orderCancelType;
}
