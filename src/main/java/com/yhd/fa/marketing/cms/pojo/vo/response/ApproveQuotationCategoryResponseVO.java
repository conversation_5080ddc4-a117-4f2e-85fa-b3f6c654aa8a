package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationCategoryResponseVO.java, v0.1 2022/12/19 10:13 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveQuotationCategoryResponseVO extends BaseVO {
    /**
     * 类别编码
     */
    @Schema(description = "类别编码", example = "A01.01.01")
    private String categoryCode;

    /**
     * 类别编码名称
     */
    @Schema(description = "类别名称", example = "导向轴")
    private String productName;
}
