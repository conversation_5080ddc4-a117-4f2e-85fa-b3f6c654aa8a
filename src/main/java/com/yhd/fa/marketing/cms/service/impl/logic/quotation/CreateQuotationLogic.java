package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.cms.api.sdk.enums.FaRoleEnum;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.common.util.UUIDUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.*;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.GoodsStatusEnum;
import com.yhd.fa.marketing.cms.enums.QuotationPriceStatusEnum;
import com.yhd.fa.marketing.cms.enums.QuotationStatusEnum;
import com.yhd.fa.marketing.cms.mapper.QuotationListMapper;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.mq.producer.ProductAddNebulaGraphProducer;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CreateQuotationDetailRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CreateQuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationModelInfoRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;
import com.yhd.fa.marketing.cms.service.sao.ProductBusinessInfoService;
import com.yhd.fa.marketing.cms.service.sao.UnifiedPushMessageService;
import com.yhd.fa.marketing.cms.util.QuotationUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.UserUtil;
import com.yhd.united.file.service.OssService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: CreateQuotationLogic.java, v0.1 2023/1/5 14:19 yehuasheng Exp $
 */
@Component
@RefreshScope
public class CreateQuotationLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(CreateQuotationLogic.class.getName());

    /**
     * 询价服务
     */
    @Resource
    private ProductBusinessInfoService productBusinessInfoService;

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    /**
     * 报价单明细mapper
     */
    @Resource
    private QuotationListMapper quotationListMapper;

    /**
     * 推送服务
     */
    @Resource
    private UnifiedPushMessageService unifiedPushMessageService;

    /**
     * 移除图片标签钉钉告警的工号人员
     */
    @Value("${removeImageTag.dingding.mobileNumber}")
    private String removeImageTagDingDingMobileNumber;

    /**
     * 移除图片标签的钉钉告警
     */
    @Value("${removeImageTag.dingding.templateId}")
    private String removeImageTagTemplateId;

    /**
     * oss服务
     */
    @Resource
    private OssService ossService;

    /**
     * 执行创建报价单
     *
     * @param userInfo                 用户信息
     * @param createQuotationRequestVO 创建报价单的参数
     * @return BusinessResponse<String>
     */
    public BusinessResponse<String> exec(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo, CreateQuotationRequestVO createQuotationRequestVO) {
        logger.info("start exec create quotation logic.");

        // 获取报价信息
        List<QuotationModelInfoRequestVO> quotationModelInfoRequestVOList = createQuotationRequestVO.getProducts()
                .stream()
                .map(createQuotationDetailRequestVO ->
                        QuotationModelInfoRequestVO.builder()
                                .model(createQuotationDetailRequestVO.getCustomerModel())
                                .quantity(createQuotationDetailRequestVO.getQuantity())
                                .build())
                .collect(Collectors.toList());
        QuotationRequestVO quotationRequestVO = QuotationRequestVO.builder()
                .source(QuotationSourceConstant.ONLINE)
                .companyCode(userInfo.getCompanyInfo().getErpCompanyCode())
                .userRole(UserUtil.getUserEnquiryRole(userInfo))
                .list(quotationModelInfoRequestVOList).build();
        BusinessResponse<List<QuotationResponseVO>> modelPrice = productBusinessInfoService.getModelPrice(quotationRequestVO);
        if (!modelPrice.success()) {
            return BusinessResponseCommon.fail(modelPrice.getRt_code(), modelPrice.getRt_msg());
        }

        // 生成报价单号
        String quotationNumber = null;
        boolean haveQuotationNumber = true;
        while (haveQuotationNumber) {
            quotationNumber = QuotationUtil.generateQuotationNumber(userInfo);
            // 查询数据库是否存在报价单号
            QuotationPO quotation = quotationMapper.getOne(new LambdaQueryWrapper<QuotationPO>().eq(QuotationPO::getQuotationNumber, quotationNumber).last(FaDocMarketingCmsConstant.LIMIT));
            if (ObjectUtil.isEmpty(quotation)) {
                haveQuotationNumber = false;
            }
        }

        // 插入数据
        return insertQuotationData(userInfo, quotationNumber, modelPrice.getData(), createQuotationRequestVO);
    }

    /**
     * 插入报价单数据
     *
     * @param userInfo                 用户信息
     * @param quotationNumber          报价单号
     * @param quotationPrice           报价单价格和交期集合
     * @param createQuotationRequestVO 请求参数
     * @return BusinessResponse<String>
     */
    private BusinessResponse<String> insertQuotationData(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo, String quotationNumber, List<QuotationResponseVO> quotationPrice, CreateQuotationRequestVO createQuotationRequestVO) {
        logger.info("insert quotation data.");

        // 设置报价单id
        String quotationId = UUIDUtils.getStringUUID();

        // 设置报价的明细
        List<QuotationListPO> quotationDetailList = setQuotationDetail(quotationId, quotationNumber, quotationPrice, createQuotationRequestVO);

        // 设置报价单信息
        QuotationPO quotationInfo = setQuotation(userInfo, quotationId, quotationNumber, quotationDetailList, createQuotationRequestVO);

        // 插入数据
        if (quotationMapper.save(quotationInfo) && quotationListMapper.saveBatch(quotationDetailList)) {
            // 同步给erp
            SpringUtil.getBean(SynchronizationQuotationLogic.class).synchronization(quotationInfo, quotationDetailList);

            // 图片标签删除
            List<String> fileKey = createQuotationRequestVO.getProducts().stream().map(CreateQuotationDetailRequestVO::getFileKey).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(fileKey)) {
                SpringUtil.getBean(CreateQuotationLogic.class).removeFileTag(fileKey, quotationNumber);
            }

            // 同步图数据库
            SpringUtil.getBean(ProductAddNebulaGraphProducer.class).sendMqMessage(quotationInfo, quotationDetailList, userInfo);

            return BusinessResponseCommon.ok(quotationId);
        } else {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.INSERT_QUOTATION_FAIL);
        }

    }

    /**
     * 设置报价明细集合
     *
     * @param quotationId              报价单的id
     * @param quotationNumber          报价单号
     * @param quotationPrice           报价的价格交期集合
     * @param createQuotationRequestVO 请求参数
     * @return List<QuotationListPO>
     */
    private List<QuotationListPO> setQuotationDetail(String quotationId, String quotationNumber, List<QuotationResponseVO> quotationPrice, CreateQuotationRequestVO createQuotationRequestVO) {
        logger.info("set quotation detail list data.");

        // 定义用户名
        String userName = SecurityUtil.getAccountNoAndUserName();

        // 设置型号对应序号的价格
        Map<String, Map<String, QuotationResponseVO>> quotationPriceMap = quotationPrice.stream()
                .collect(Collectors.groupingBy(QuotationResponseVO::getModel,
                        Collectors.toMap(QuotationResponseVO::getSort, Function.identity())));

        // 报价状态转map
        Map<Integer, String> quotationPriceStatusMap = Arrays.stream(QuotationPriceStatusEnum.values())
                .collect(Collectors.toMap(QuotationPriceStatusEnum::getStatus, QuotationPriceStatusEnum::getCode));

        // 商品状态映射
        Map<String, String> goodsStatusMap = QuotationUtil.setGoodsStatusMap();

        List<QuotationListPO> quotationList = new ArrayList<>();
        for (int i = 0; i < createQuotationRequestVO.getProducts().size(); i++) {
            // 获取请求报价的型号信息
            CreateQuotationDetailRequestVO createQuotationDetailRequestVO = createQuotationRequestVO.getProducts().get(i);

            // 获取报价完后的信息 型号 -> 数量-序号
            QuotationResponseVO quotationPriceResponseVO = quotationPriceMap
                    .get(createQuotationDetailRequestVO.getCustomerModel())
                    .get(createQuotationDetailRequestVO.getQuantity() + CommonConstant.DASH + i);

            // 设置型号、交期、价格的信息
            QuotationListPO quotationListPO = QuotationListPO.builder()
                    .quotationId(quotationId)
                    .quotationNumber(quotationNumber)
                    .sort(i + CommonConstant.ONE)
                    .model(quotationPriceResponseVO.isStandard() ? quotationPriceResponseVO.getModel() : null)
                    .customerModel(createQuotationDetailRequestVO.getCustomerModel())
                    .productCode(quotationPriceResponseVO.getCode())
                    .productName(quotationPriceResponseVO.getProductName())
                    .quantity(createQuotationDetailRequestVO.getQuantity())
                    .remark(createQuotationDetailRequestVO.getRemark())
                    .customerMaterialCode(createQuotationDetailRequestVO.getMaterialCode())
                    .customerProductName(createQuotationDetailRequestVO.getCustomerProductName())
                    .modelLong(quotationPriceResponseVO.getAluminumProfileValueOfFieldLong())
                    .plotId(quotationPriceResponseVO.getModelValueId())
                    .priceId(quotationPriceResponseVO.getSourcePriceAutoId())
                    .additionalPriceId(quotationPriceResponseVO.getSourcePriceAutoId())
                    .materialQualityId(quotationPriceResponseVO.getModelValueId())
                    .supplierPriceOne(quotationPriceResponseVO.getG1Price())
                    .supplierPriceTwo(quotationPriceResponseVO.getG2Price())
                    .supplierPriceThree(quotationPriceResponseVO.getG3Price())
                    .technicalSpecifications(quotationPriceResponseVO.getTechnicalSpecifications())
                    .unit(quotationPriceResponseVO.getUnit())
                    .calculationStatus(quotationPriceStatusMap.get(quotationPriceResponseVO.getQuotedPriceStatus()))
                    .confirmationStatus(null)
                    .goodsStatus(quotationPriceResponseVO.isStandard() && quotationPriceResponseVO.isHaveDelivery()
                            ? GoodsStatusEnum.NORMAL.getStatus()
                            : goodsStatusMap.getOrDefault(quotationPriceResponseVO.getErrorType(), GoodsStatusEnum.WRONG_MODEL.getStatus()))
                    .materialCode(quotationPriceResponseVO.getMaterialCode())
                    .quotationStatus(QuotationUtil.setQuotationDetailStatus(quotationPriceResponseVO, createQuotationDetailRequestVO))
                    .errorMessage(quotationPriceResponseVO.getMsg())
                    .fileUrl(createQuotationDetailRequestVO.getFileUrl())
                    .standardStatus(quotationPriceResponseVO.isStandard() && quotationPriceResponseVO.isHaveDelivery() ? CommonConstant.YES : CommonConstant.NO)
                    .build();

            // 设置价格和交期
            if (StringUtils.isBlank(createQuotationDetailRequestVO.getFileUrl()) && StringUtils.isBlank(createQuotationDetailRequestVO.getRemark())) {
                QuotationUtil.setQuotationPOPriceAndDelivery(quotationListPO, quotationPriceResponseVO);
            }

            // 设置id、创建时间、创建人
            quotationListPO.setId(UUIDUtils.getStringUUID());
            quotationListPO.setCreatedDate(LocalDateTime.now());
            quotationListPO.setCreatedBy(userName);

            quotationList.add(quotationListPO);
        }

        return quotationList;
    }

    /**
     * 设置报价单信息
     *
     * @param userInfo                 用户信息
     * @param quotationId              报价单id
     * @param quotationNumber          报价单号
     * @param quotationDetailList      报价单明细集合
     * @param createQuotationRequestVO 请求参数
     * @return QuotationPO
     */
    private QuotationPO setQuotation(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo, String quotationId, String quotationNumber, List<QuotationListPO> quotationDetailList, CreateQuotationRequestVO createQuotationRequestVO) {
        logger.info("set quotation po data.");

        // 设置报价状态
        String quotationStatus = quotationDetailList.stream().allMatch(quotationListPO -> StringUtils.equals(quotationListPO.getQuotationStatus(), QuotationStatusEnum.FINISH.getStatus())) ? QuotationStatusEnum.FINISH.getStatus() : QuotationStatusEnum.QUOTATION.getStatus();

        boolean isQuotation = StringUtils.equals(quotationStatus, QuotationStatusEnum.FINISH.getStatus());

        // 计算总价
        BigDecimal totalPrice = null;
        if (isQuotation) {
            totalPrice = quotationDetailList.stream().map(QuotationListPO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 设置采购人
        String purchaseUserCode = createQuotationRequestVO.getPurchaseUserCode();
        if (StringUtils.isBlank(purchaseUserCode) || UserUtil.haveCreateOrderAuthority(userInfo)) {
            purchaseUserCode = userInfo.getUserCode();
        }

        // 设置报价单po
        QuotationPO quotationPO = QuotationPO.builder()
                .quotationNumber(quotationNumber)
                .quotationLevel(StringUtils.equals(userInfo.getCompanyRole().getRoleCode(), FaRoleEnum.YHD_FA_COMPANY_PU_TONG_QI_YE.getCode()) ? QuotationLevelConstant.ORD_CO : QuotationLevelConstant.CERT_CO)
                .customerQuotationNumber(createQuotationRequestVO.getCustomerQuotationCode())
                .userCode(userInfo.getUserCode())
                .companyCode(userInfo.getCompanyInfo().getCompanyCode())
                .companyName(userInfo.getCompanyInfo().getCompanyName())
                .purchaseUserCode(purchaseUserCode)
                .quotationStatus(quotationStatus)
                .totalPrice(totalPrice)
                .payablePrice(totalPrice)
                .synchronizationStatus(SynchronizationStatusConstant.NOT_SYNCHRONIZED)
                .isExamine(CommonConstant.FALSE)
                .examineDate(LocalDateTime.now())
                .examineId(SecurityUtil.getAccountNo())
                .examineStatus(ExamineStatusConstant.AGREE)
                .quotationCompletionDate(isQuotation ? LocalDateTime.now() : null)
                .quotationUserCode(isQuotation ? FaDocMarketingCmsConstant.ADMIN_STRING : null)
                .channelType(StringUtils.isNotBlank(createQuotationRequestVO.getChannelType()) ? createQuotationRequestVO.getChannelType() : ChannelTypeConstant.PC)
                .isInsideCreated(CommonConstant.TRUE)
                .insideEmployeeCode(SecurityUtil.getAccountNo())
                .employeeName(SecurityUtil.getUserName())
                .erpCompanyCode(userInfo.getCompanyInfo().getErpCompanyCode())
                .build();
        quotationPO.setId(quotationId);
        quotationPO.setCreatedBy(SecurityUtil.getAccountNoAndUserName());
        quotationPO.setCreatedDate(LocalDateTime.now());
        return quotationPO;
    }

    /**
     * 移除生成报价单的附件标签
     *
     * @param fileKey 附件标签
     */
    @Async
    public void removeFileTag(List<String> fileKey, String quotationNumber) {
        logger.info("remove minio file tag. fileKey:{}", fileKey);

        // 移除 标签
        fileKey.forEach(key -> {
            if (!ossService.removeTemporaryLabel(key)) {
                logger.error("remove temporary label fail. key:{}", key);
                // 设置推送的人员
                List<String> receiverRequestVOList = Arrays.asList(removeImageTagDingDingMobileNumber.split(CommonConstant.SYMBOL_COMMA));
                // 设置推送的内容
                Map<String, Object> replaceContent = new HashMap<>();
                replaceContent.put("quotationNumber", quotationNumber);
                replaceContent.put("key", key);
                replaceContent.put("exceptionTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
                unifiedPushMessageService.sendDingDingRobot(removeImageTagTemplateId, receiverRequestVOList, replaceContent, CommonConstant.FALSE);
            }
        });
    }
}
