package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogPO.java, v0.1 2022/8/27 10:55 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("enquiry_log")
public class EnquiryLogPO extends BaseEntity {
    /**
     * 型号
     */
    private String model;

    /**
     * 代码
     */
    private String code;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 原价
     */
    private BigDecimal costPrice;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总价
     */
    private BigDecimal taxTotalPrice;

    /**
     * 总折扣
     */
    private BigDecimal totalDiscount;

    /**
     * 交期
     */
    private Integer delivery;

    /**
     * 用户id
     */
    private String userCode;

    /**
     * 用户等级
     */
    private Integer userLevel;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业等级
     */
    private Integer companyLevel;

    /**
     * 询价状态
     */
    private String enquiryStatus;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 是否标准型号
     */
    private String isStandard;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 用户类型 0 Personal 个人用户,  1 Enterprise 普通企业用户, 2 PendingEnterprise 待认证企业用户,  3 VerifiedEnterprise 认证企业用户
     */
    private Integer userType;

    /**
     * 用户职位
     */
    private String occupation;

    /**
     * IP地址区域
     */
    private String ipRegion;

    /**
     * 分区的时间
     */
    private LocalDate createdDay;

    /**
     * 客户注册时间
     */
    private LocalDateTime registerDate;

    //状态数据加字段
    /**
     * 用户名称
     */
    private String userName;

    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 用户手机号码
     */
    private String userPhone;
    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 跟单员
     */
    private String merchandiser;

    /**
     * 业务员
     */
    private String salesman;

    /**
     * 客户等级
     */
    private String customerGrade;
}
