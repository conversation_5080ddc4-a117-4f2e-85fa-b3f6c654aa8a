package com.yhd.fa.marketing.cms.util;

import com.yhd.buc.cms.api.sdk.pojo.vo.request.CompanyInfoListByJobNumberRequestVO;
import com.yhd.buc.cms.api.sdk.utils.UserApiUtil;
import com.yhd.common.exception.BizException;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.configure.DatePermissionsProperties;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.ucs.po.LoginUserAllInfo;
import com.yhd.ucs.po.response.YhducsEmployeeInfo;
import com.yhd.ucs.po.response.YhducsRoleInfo;
import com.yhd.ucs.util.UcsLoginUtil;
import org.slf4j.Logger;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SecurityUtil.java, v0.1 2022/12/2 14:36 yehuasheng Exp $
 */
public class SecurityUtil {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SecurityUtil.class.getName());

    private SecurityUtil() {
    }

    /**
     * 获取用户明细
     *
     * @return LoginUserAllInfo
     */
    public static LoginUserAllInfo getUserDetails() {
        try {
            return UcsLoginUtil.getNewLoginUserInfo();
        } catch (Exception var7) {
            throw new BizException(1009, "凭证无效或已过期");
        }
    }

    /**
     * 获取员工信息
     *
     * @return YhducsEmployeeInfo
     */
    public static YhducsEmployeeInfo getEmployeeInfo() {
        LoginUserAllInfo userDetails = getUserDetails();
        if (Objects.isNull(userDetails)) {
            return null;
        }
        return userDetails.getEmployeeInfo();
    }

    /**
     * 获取用户姓名
     *
     * @return String
     */
    public static String getUserName() {
        YhducsEmployeeInfo employeeInfo = getEmployeeInfo();
        if (Objects.isNull(employeeInfo)) {
            return null;
        }
        return employeeInfo.getEmplName();
    }

    /**
     * 获取用户工号
     *
     * @return String
     */
    public static String getAccountNo() {
        YhducsEmployeeInfo employeeInfo = getEmployeeInfo();
        if (Objects.isNull(employeeInfo)) {
            return null;
        }
        return employeeInfo.getEmplCode();
    }

    /**
     * 获取用户姓名(工号)
     *
     * @return String
     */
    public static String getAccountNoAndUserName() {
        YhducsEmployeeInfo employeeInfo = getEmployeeInfo();
        if (Objects.isNull(employeeInfo)) {
            return "admin";
        }
        return getUserName() + "(" + getAccountNo() + ")";
    }

    /**
     * 获取业务对应的企业编码
     *
     * @return List<String>
     */
    public static List<String> getOperatorCompanyCode() {
        logger.info("get operator company code");

        // 请求业务接口
        return UserApiUtil.getCompanyCodeListByJobNumberMethod(CompanyInfoListByJobNumberRequestVO
                .builder()
                .platformCode(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .jobNumber(getAccountNo())
                .build());
    }

    /**
     * 获取当前用户所有角色
     */
    public static List<String> getRoleCodeList() {
        try {
            return getUserDetails().getRoleInfos().stream().map(YhducsRoleInfo::getRoleCode).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BizException(1009, "凭证无效或已过期");
        }

    }

    /**
     * 数据权限判断是否查看全部
     */
    public static boolean ifShowAllData() {
        try {
            return getRoleCodeList().stream().anyMatch(DatePermissionsProperties.getInstance().getAllDataRoleCodeList()::contains);
        } catch (Exception e) {
            logger.error("if show all date error {}", e.getMessage());
            return false;
        }

    }

    public static boolean ifCustomizeRole() {
        try {
            return getRoleCodeList().stream().anyMatch(DatePermissionsProperties.getInstance().getCustomizeCodeList()::contains);
        } catch (Exception e) {
            logger.error("if show customize date error {}", e.getMessage());
            return false;
        }

    }

    public static List<String> getDataRoleBindEmployee() {
        logger.info("get bind employee code");

        // 请求业务接口
        return UserApiUtil.getDataRoleBindEmployee(getAccountNo());
    }
}
