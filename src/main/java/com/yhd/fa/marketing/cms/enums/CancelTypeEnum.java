package com.yhd.fa.marketing.cms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: CancelTypeEnum.java, v 0.1 2022/11/23 16:39 JiangYuHong Exp $
 */
@Getter
@AllArgsConstructor
public enum CancelTypeEnum {

    /**
     * 部分取消
     */
    PART("part", "部分取消"),

    /**
     * 整单取消
     */
    ALL("all", "整单取消");

    private final String code;
    private final String desc;
}
