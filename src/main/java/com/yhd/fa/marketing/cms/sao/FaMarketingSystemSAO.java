package com.yhd.fa.marketing.cms.sao;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.ReturnCouponRequestVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 营销活动系统SAO
 *
 * <AUTHOR>
 * @version Id: FaMarketingSystemSAO.java, v 0.1 2024/8/8 16:22 JiangYuHong Exp $
 */
@FeignClient(name = "yhd-service-fa-marketing-system-cms")
public interface FaMarketingSystemSAO {

    /**
     * 退还优惠券
     *
     * @param requestVO 请求参数
     * @return businessResponse
     */
    @PostMapping(value = "/fa/marketing/system/cms/v1/0/common/return", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<Object> returnCoupon(@RequestBody ReturnCouponRequestVO requestVO);

}
