package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationDetailResponseVO.java, v0.1 2022/12/12 17:24 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApproveQuotationDetailResponseVO extends BaseVO {
    /**
     * 报价单id
     */
    @Schema(description = "报价单id", example = "2bdb24c30f6e49ce8eb23d66672c39a1")
    private String quotationId;

    /**
     * 报价单状态
     */
    @Schema(description = "报价单状态 quotation报价中，finish报价完成，close已终止，outTime超报价有效期", example = "quotation")
    private String quotationStatus;

    /**
     * 报价单号
     */
    @Schema(description = "报价单号", example = "YB202211161438419779SXBQ")
    private String quotationNumber;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "A001")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "测试企业")
    private String companyName;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "123456")
    private String userCode;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称", example = "测试test")
    private String userName;

    /**
     * 采购人员编码
     */
    @Schema(description = "采购人员编码")
    private String purchaseUserCode;

    /**
     * 采购人员名称
     */
    @Schema(description = "采购人员名称", example = "测试哈哈")
    private String purchaseUserName;

    /**
     * 跟单名称
     */
    @Schema(description = "跟单员名称", example = "林杏金")
    private String merchandiser;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "13111111111")
    private String merchandiserContact;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-12-14 17:58:00", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    /**
     * 报价单列表
     */
    @Schema(description = "报价单列表")
    private List<ApproveQuotationDetailListResponseVO> quotationDetailList;
}
