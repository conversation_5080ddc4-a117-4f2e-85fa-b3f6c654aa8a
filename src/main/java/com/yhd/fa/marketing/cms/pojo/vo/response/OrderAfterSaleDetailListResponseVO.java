package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleDetailListResponseVO.java, v0.1 2023/2/24 16:22 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderAfterSaleDetailListResponseVO extends BaseVO {

    /**
     * 售后id
     */
    @Schema(description = "售后id", example = "44b07af2969d49d4bc8c02baf77f1d39")
    private String afterSaleId;

    /**
     * 订单明细序号
     */
    @Schema(description = "订单明细序号", example = "1")
    private Integer orderSortId;

    /**
     * 产品代码
     */
    @Schema(description = "产品代码", example = "SAD01")
    private String productCode;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称", example = "导向轴")
    private String productName;

    /**
     * 产品型号
     */
    @Schema(description = "产品型号", example = "SAD01-D4-L10")
    private String productModel;

    /**
     * 数量
     */
    @Schema(description = "数量", example = "1")
    private Long quantity;

    /**
     * 价格
     */
    @Schema(description = "价格", example = "100.00")
    private BigDecimal price;

    /**
     * 折扣单价
     */
    @Schema(description = "折扣单价", example = "100.00")
    private BigDecimal discountPrice;

    /**
     * 折扣后含税单价
     */
    @Schema(description = "折扣后含税单价", example = "100.00")
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总金额
     */
    @Schema(description = "含税总金额", example = "100.00")
    private BigDecimal totalPrice;

    /**
     * 产品单位
     */
    @Schema(description = "产品单位", example = "PCS")
    private String unit;
}
