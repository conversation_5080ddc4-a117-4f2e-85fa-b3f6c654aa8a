package com.yhd.fa.marketing.cms.sao;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.ModelSplitCodeRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ModelSplitCodeResponseVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ProductBasisSAO.java, v0.1 2023/5/5 14:21 yehuasheng Exp $
 */
@FeignClient(name = "yhd-service-product-basis")
public interface ProductBasisSAO {
    /**
     * 型号拆分代码信息
     *
     * @param modelSplitCodeRequestVO 型号集合
     * @return BusinessResponse<ModelSplitCodeResponseVO>
     */
    @PostMapping(value = "/product/v2/0/split/code", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    BusinessResponse<List<ModelSplitCodeResponseVO>> modelSplitCode(@RequestBody ModelSplitCodeRequestVO modelSplitCodeRequestVO);
}
