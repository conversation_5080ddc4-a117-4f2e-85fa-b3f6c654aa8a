package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/4/10 9:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "获取最后下单日期返参")
public class LastOrderDateResponseVO extends BaseVO {
    @Schema(description = "用户标识")
    private String userCode;

    @Schema(description = "最后下单日期")
    private String lastOrderDate;
}
