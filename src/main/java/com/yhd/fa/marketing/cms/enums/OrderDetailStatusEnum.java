package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderStatusColorConstant;
import com.yhd.fa.marketing.cms.constant.OrderStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderDetailStatusEnum.java, v0.1 2023/2/20 15:37 yehuasheng Exp $
 */
@Getter
public enum OrderDetailStatusEnum {
    UNPAID(OrderStatusConstant.UNPAID, "待支付", OrderStatusColorConstant.ORANGE),
    UNTREATED(OrderStatusConstant.UNTREATED, "待确认", OrderStatusColorConstant.ORANGE),
    CONFIRM(OrderStatusConstant.CONFIRM, "已确认", OrderStatusColorConstant.BLUE),
    STOCK_UP(OrderStatusConstant.STOCK_UP, "备货中", OrderStatusColorConstant.ORANGE),
    TAKE_DELIVERED(OrderStatusConstant.TAKE_DELIVERED, "待收货", OrderStatusColorConstant.GREEN),
    FINISH(OrderStatusConstant.FINISH, "已完成", OrderStatusColorConstant.GREEN),
    CANCELING(OrderStatusConstant.CANCELING, "取消中", OrderStatusColorConstant.RED),
    CANCEL(OrderStatusConstant.CANCEL, "已取消", OrderStatusColorConstant.GRAY),
    CLOSED(OrderStatusConstant.CLOSED, "已关闭", OrderStatusColorConstant.GRAY),
    ;

    private final String orderDetailStatus;
    private final String orderDetailStatusCn;
    private final String orderDetailStatusColor;

    OrderDetailStatusEnum(String orderDetailStatus, String orderDetailStatusCn, String orderDetailStatusColor) {
        this.orderDetailStatus = orderDetailStatus;
        this.orderDetailStatusCn = orderDetailStatusCn;
        this.orderDetailStatusColor = orderDetailStatusColor;
    }
}
