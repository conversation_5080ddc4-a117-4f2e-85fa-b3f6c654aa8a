package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/8/2 16:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FbOrderTotalPriceCountResponseVO extends BaseVO {

    @Schema(description = "订单总金额")
    private BigDecimal totalPriceAll;

    @Schema(description = "部分订单总金额")
    private BigDecimal totalPricePart;

}
