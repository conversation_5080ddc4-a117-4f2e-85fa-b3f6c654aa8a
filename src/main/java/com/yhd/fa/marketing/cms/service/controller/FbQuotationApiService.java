package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationAndOrderDeleteRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationPageRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/18 16:08
 */
public interface FbQuotationApiService {
    /**
     * 获取非标报价分页列表
     *
     * @param fbQuotationPageRequestVO
     * @return
     */
    BusinessResponse<PageInfo<FbQuotationListResponseVO>> getFbQuotationPage(FbQuotationPageRequestVO fbQuotationPageRequestVO);

    /**
     * Fb报价单详情数据获取
     *
     * @param id
     * @param quotationNumber
     * @return
     */
    BusinessResponse<FbQuotationDetailResponseVO> getFbQuotationDetail(String id, String quotationNumber);

    BusinessResponse<FbQuotationDetailResponseVO> fbQuotationInit();

    /**
     * Fb报价单单据统计金额
     *
     * @param fbQuotationPageRequestVO
     * @return
     */
    BusinessResponse<FbQuotationTotalPriceCountResponseVO> getFbQuotationCount(FbQuotationPageRequestVO fbQuotationPageRequestVO);

    /**
     * Fb报价单删除
     *
     * @param fbQuotationAndOrderDeleteRequestVO
     * @return
     */
    BusinessResponse<Object> deleteFbQuotation(FbQuotationAndOrderDeleteRequestVO fbQuotationAndOrderDeleteRequestVO);

    /**
     * 获取非标报价列表导出数据
     *
     * @param fbQuotationPageRequestVO
     * @return
     */
    BusinessResponse<List<FbQuotationaExportResponseVO>> getFbQuotationData(FbQuotationPageRequestVO fbQuotationPageRequestVO);
}
