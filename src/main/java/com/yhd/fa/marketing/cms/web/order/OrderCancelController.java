package com.yhd.fa.marketing.cms.web.order;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.HelpUserCancelOrderApplyRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCancelRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.HelpUserCancelOrderBasicsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderCancelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: OrderCancelController.java, v0.1 2023/2/21 8:44 yehuasheng Exp $
 */
@Tag(name = "订单取消接口", description = "订单取消包含 订单取消列表、详情、帮客户申请取消订单")
@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
public class OrderCancelController {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderCancelController.class.getName());

    /**
     * 订单取消服务
     */
    @Resource
    private OrderCancelService orderCancelService;

    /**
     * 订单取消列表
     *
     * @param orderCancelRequestVO 订单取消列表请求参数
     * @return BusinessResponse<PageInfo < OrderCancelResponseVO>>
     */
    @Operation(summary = "订单取消列表")
    @PostMapping(value = UriConstant.ORDER_CANCEL_LIST, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<PageInfo<OrderCancelResponseVO>> orderCancelList(@RequestBody @Validated OrderCancelRequestVO orderCancelRequestVO) {
        logger.info("request order cancel list api. parameter orderCancelRequestVO:{}", orderCancelRequestVO);

        // 执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<PageInfo<OrderCancelResponseVO>> businessResponse = orderCancelService.getOrderCancelList(orderCancelRequestVO);
        logger.info("request order cancel list success response businessResponse:{}", businessResponse);
        logger.info("request order cancel list cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 获取订单取消详情
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<OrderCancelDetailResponseVO>
     */
    @Operation(summary = "订单取消详情页", parameters = {@Parameter(name = "orderCancelId", description = "订单取消id", example = "5012551d99714d3e93c6f649aa87dd1d", required = true)})
    @GetMapping(value = UriConstant.ORDER_CANCEL_DETAIL)
    public BusinessResponse<OrderCancelDetailResponseVO> orderCancelDetail(@RequestParam String orderCancelId) {
        logger.info("request order cancel detail api. parameter orderCancelId:{}", orderCancelId);

        // 执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<OrderCancelDetailResponseVO> businessResponse = orderCancelService.getOrderCancelInfo(orderCancelId);
        logger.info("request order cancel detail api success response businessResponse:{}", businessResponse);
        logger.info("request order cancel detail api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 同步订单取消
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<Void>
     */
    @Operation(summary = "订单取消同步", parameters = {@Parameter(name = "orderCancelId", description = "订单取消id", example = "5012551d99714d3e93c6f649aa87dd1d")})
    @GetMapping(value = UriConstant.SYNCHRONIZATION_CANCEL_ORDER)
    public BusinessResponse<Void> synchronizationOrderCancel(@RequestParam String orderCancelId) {
        logger.info("request synchronization order cancel api parameter orderCancelId:{}", orderCancelId);

        // 执行时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<Void> businessResponse = orderCancelService.synchronizationOrderCancel(orderCancelId);
        logger.info("request synchronization order cancel api success response businessResponse:{}", businessResponse);
        logger.info("request synchronization order cancel api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 取消订单初始化接口
     *
     * @param orderId 订单id
     * @return BusinessResponse
     */
    @Operation(summary = "取消订单初始化接口", parameters = {@Parameter(name = "orderId", description = "订单id", example = "5012551d99714d3e93c6f649aa87dd1d", required = true)})
    @GetMapping(value = UriConstant.ORDER_CANCEL_BASICS)
    public BusinessResponse<List<HelpUserCancelOrderBasicsResponseVO>> cancelBasics(@RequestParam String orderId) {
        logger.info("request cancel basics api. parameter:{}", orderId);
        Stopwatch stopwatch = Stopwatch.createStarted();

        BusinessResponse<List<HelpUserCancelOrderBasicsResponseVO>> businessResponse = orderCancelService.helpUserCancelOrderBasics(orderId);

        logger.info("request cancel basics api businessResponse:{}", businessResponse);
        logger.info("request cancel basics api end. cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }

    /**
     * 申请取消订单
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @Operation(summary = "申请取消订单")
    @PostMapping(value = UriConstant.APPLY_ORDER_CANCEL)
    public BusinessResponse<Object> applyOrderCancel(@RequestBody HelpUserCancelOrderApplyRequestVO requestVO) {

        logger.info("request apply order cancel api. parameter:{}", requestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();

        BusinessResponse<Object> businessResponse = orderCancelService.helpUserCancelOrderApply(requestVO);

        logger.info("request apply order cancel api businessResponse:{}", businessResponse);
        logger.info("request apply order cancel api end. cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }
}
