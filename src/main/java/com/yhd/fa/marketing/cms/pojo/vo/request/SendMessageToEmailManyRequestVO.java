package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: SendMessageToEmailManyRequestVO.java, v0.1 2023/3/20 19:48 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendMessageToEmailManyRequestVO extends BaseVO {
    /**
     * 模板id
     */
    private String templateId;

    /**
     * 内容
     */
    private transient List<Map<String, Object>> map = new ArrayList<>();

    /**
     * 邮箱
     */
    private List<String> emailAddress;

    /**
     * 邮件类型：order订单，quotation报价单，other其他；订单报价单邮件包含明细项时传入order/quotation，不包含明细项直接传other
     */
    private String type;
}
