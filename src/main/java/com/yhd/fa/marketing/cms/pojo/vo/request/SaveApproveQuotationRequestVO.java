package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.ApproveQuotationResultTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: SaveApproveQuotationRequestVO.java, v0.1 2022/12/23 14:43 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveApproveQuotationRequestVO extends BaseVO {
    /**
     * 处理结果的类型 approved审核通过 refuse拒绝
     */
    @Schema(description = "处理结果的类型 approved审核通过 refuse拒绝",
            example = "approved",
            allowableValues = {ApproveQuotationResultTypeConstant.APPROVED,
                    ApproveQuotationResultTypeConstant.REFUSE})
    @ValueInEnum(matchTarget = {ApproveQuotationResultTypeConstant.APPROVED,
            ApproveQuotationResultTypeConstant.REFUSE}, message = "处理结果类型不正确")
    @NotEmpty(message = "处理结果类型不能为空")
    private String resultType;

    /**
     * 拒绝理由
     */
    @Schema(description = "拒绝理由 当处理结果类型resultType是拒绝时refuse 必填")
    private String reasonsRefusal;

    /**
     * 报价单id
     */
    @Schema(description = "报价单id 当处理结果类型resultType是审核通过时approved 必填")
    private String quotationId;

    /**
     * 是否有改动报价单
     */
    @Schema(description = "是否有改动报价单 true有 false没有", example = "true")
    private boolean changeToQuotation = true;

    /**
     * 报价单详情
     */
    @Schema(description = "报价单详情 当处理结果类型resultType是审核通过时approved 必填")
    @Valid
    private List<CreateQuotationDetailRequestVO> quotationDetailList;
}
