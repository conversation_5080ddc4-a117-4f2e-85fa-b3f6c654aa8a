package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.QuotationDeleteStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotationSynchronizationStatusConstant;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetApproveQuotationListLogic.java, v0.1 2022/12/8 9:35 yehuasheng Exp $
 */
@Component
public class GetApproveQuotationListLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetApproveQuotationListLogic.class.getName());

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 执行查询审核报价单列表
     *
     * @param approveQuotationRequestVO 审核报价单列表请求参数
     * @return BusinessResponse<PageInfo < ApproveQuotationResponseVO>>
     */
    public BusinessResponse<PageInfo<ApproveQuotationResponseVO>> exec(ApproveQuotationRequestVO approveQuotationRequestVO) {
        logger.info("start get approve quotation logic.");

        // 设置分页
        PageMethod.startPage(approveQuotationRequestVO.getPageNum(), approveQuotationRequestVO.getPageSize());

        // 设置查询的条件
        MPJLambdaWrapper<QuotationPO> queryWrapper = setQueryWrapper(approveQuotationRequestVO);

        if (null == queryWrapper) {
            return BusinessResponse.ok(PageInfo.of(new ArrayList<>()));
        }

        // 查询报价单审核列表
        List<ApproveQuotationResponseVO> approveQuotationList = quotationMapper.selectJoinList(ApproveQuotationResponseVO.class, queryWrapper);

        // 设置分页
        PageInfo<ApproveQuotationResponseVO> pageInfo = new PageInfo<>(approveQuotationList);

        // 获取用户名称以及跟单信息
        setApproveQuotationUserInfo(pageInfo.getList());

        // 设置分页并返回
        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 设置审核报价单列表查询条件
     *
     * @param approveQuotationRequestVO 审核报价单请求参数
     * @return MPJLambdaWrapper<QuotationPO>
     */
    private MPJLambdaWrapper<QuotationPO> setQueryWrapper(ApproveQuotationRequestVO approveQuotationRequestVO) {
        logger.info("set approve quotation query wrapper.");

        // 设置基础的条件
        MPJLambdaWrapper<QuotationPO> queryWrapper = new MPJLambdaWrapper<QuotationPO>()
                .ge(QuotationPO::getCreatedDate, LocalDateTime.now().minusMonths(CommonConstant.THREE))
                .eq(QuotationPO::getDeleteStatus, QuotationDeleteStatusConstant.NORMAL)
                .eq(QuotationPO::getSynchronizationStatus, QuotationSynchronizationStatusConstant.NOT_SYNCHRONIZED)
                .eq(QuotationPO::getQuotationStatus, QuotationStatusConstant.QUOTATION)
                .eq(QuotationPO::getIsExamine, CommonConstant.TRUE)
                .selectAs(QuotationPO::getId, ApproveQuotationResponseVO::getQuotationId)
                .selectAs(QuotationPO::getQuotationNumber, ApproveQuotationResponseVO::getQuotationNumber)
                .selectAs(QuotationPO::getUserCode, ApproveQuotationResponseVO::getUserCode)
                .selectAs(QuotationPO::getPurchaseUserCode, ApproveQuotationResponseVO::getPurchaseUserCode)
                .selectAs(QuotationPO::getCompanyCode, ApproveQuotationResponseVO::getCompanyCode)
                .selectAs(QuotationPO::getCompanyName, ApproveQuotationResponseVO::getCompanyName)
                .selectAs(QuotationPO::getCreatedDate, ApproveQuotationResponseVO::getCreatedDate)
                .orderByDesc(QuotationPO::getCreatedDate);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, QuotationPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 判断是否有报价单号
        if (StringUtils.isNotBlank(approveQuotationRequestVO.getQuotationNumber())) {
            queryWrapper.like(QuotationPO::getQuotationNumber, approveQuotationRequestVO.getQuotationNumber().trim());
        }

        return queryWrapper;
    }

    /**
     * 设置审核报价单的用户名以及跟单信息
     *
     * @param approveQuotationList 审核报价单列表
     */
    private void setApproveQuotationUserInfo(List<ApproveQuotationResponseVO> approveQuotationList) {
        logger.info("set approve quotation list user info.");

        if (CollUtil.isEmpty(approveQuotationList)) {
            return;
        }

        // 获取用户编码集合
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = approveQuotationList
                .stream()
                .map(approveQuotationResponseVO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .companyCode(approveQuotationResponseVO.getCompanyCode())
                        .purchaseUserCode(approveQuotationResponseVO.getPurchaseUserCode())
                        .userCode(approveQuotationResponseVO.getUserCode())
                        .build())
                .collect(Collectors.toList());
        Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userInfoMap = userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);

        approveQuotationList.forEach(approveQuotationResponseVO -> {
            // 设置用户
            Optional.ofNullable(userInfoMap.get(approveQuotationResponseVO.getUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
                // 设置跟单和联系方式
                Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).ifPresent(merchandiserResponseVO -> {
                    approveQuotationResponseVO.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
                    approveQuotationResponseVO.setMerchandiserContact(merchandiserResponseVO.getMobile());
                });
                // 设置用户名称
                if (null != userAndCompanyAndMerchandiserResponseVO.getUserInfo()) {
                    approveQuotationResponseVO.setUserName(userAndCompanyAndMerchandiserResponseVO.getUserInfo().getUserName());
                }
            });

            // 设置采购人
            Optional.ofNullable(userInfoMap.get(approveQuotationResponseVO.getPurchaseUserCode())).ifPresent(merchandiserResponseVO -> approveQuotationResponseVO.setPurchaseUserName(merchandiserResponseVO.getUserInfo().getUserName()));
        });
    }
}
