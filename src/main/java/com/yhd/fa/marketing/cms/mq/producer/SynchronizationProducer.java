package com.yhd.fa.marketing.cms.mq.producer;

import com.alibaba.fastjson2.JSON;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.MqConstant;
import com.yhd.fa.marketing.cms.pojo.dto.OnlineSynchronizationDTO;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: SynchronizationProducer.java, v0.1 2023/1/27 8:56 yehuasheng Exp $
 */
@Component
public class SynchronizationProducer {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SynchronizationProducer.class.getName());

    /**
     * 日志
     */
    private static final Logger rocketMqLogger = LogUtils.getLogger(MqConstant.MQ_LOG_NAME);

    /**
     * mq
     */
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 同步创建报价单
     *
     * @param synchronizationData 同步的json数据
     * @param businessType        同步业务类型
     * @param businessOrderNo     业务单号
     */
    public void synchronizationData(String synchronizationData, String businessType, String businessOrderNo) {
        logger.info("start send synchronization producer to mq. topic:{} businessType:{} businessOrderNo:{}", MqConstant.FA_RESYNCHRONIZE_TOPIC, businessType, businessOrderNo);
        rocketMqLogger.info("start send synchronization producer to mq. topic:{} businessType:{} businessOrderNo:{}", MqConstant.FA_RESYNCHRONIZE_TOPIC, businessType, businessOrderNo);

        // 设置发送给mq的DTO
        OnlineSynchronizationDTO onlineSynchronizationDTO = OnlineSynchronizationDTO.builder()
                .source(FaDocMarketingCmsConstant.PLATFORM_CODE)
                .synchronizationData(synchronizationData)
                .businessType(businessType)
                .businessOrderNo(businessOrderNo)
                .build();

        // DTO对象转json
        String sendData = JSON.toJSONString(onlineSynchronizationDTO);

        // 发送给mq
        rocketMqLogger.info("send synchronization send msg:{}", sendData);
        rocketMQTemplate.convertAndSend(MqConstant.FA_RESYNCHRONIZE_TOPIC, sendData);
    }
}
