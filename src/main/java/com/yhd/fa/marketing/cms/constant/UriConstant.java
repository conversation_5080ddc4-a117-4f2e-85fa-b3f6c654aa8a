package com.yhd.fa.marketing.cms.constant;

/**
 * <AUTHOR>
 * @version Id: UriConstant.java, v0.1 2022/12/1 17:06 yehuasheng Exp $
 */
public class UriConstant {
    /************************** 前缀 ********************************

     /**
     * 版本
     */
    public static final String VERSION = "/v1/0";
    /**
     * 前缀
     */
    public static final String PREFIX = "/fadoc/marketing/cms";
    /************************** 报价单 *******************************

     /**
     * 报价单
     */
    public static final String QUOTATION_LIST = "/quotation/list";
    /**
     * 报价单详情
     */
    public static final String QUOTATION_DETAIL = "/quotation/detail";
    /**
     * 审核报价单列表
     */
    public static final String APPROVE_QUOTATION_LIST = "/approve/quotation/list";
    /**
     * 审核报价单详情
     */
    public static final String APPROVE_QUOTATION_DETAIL = "/approve/quotation/detail";
    /**
     * 待审核报价单分类选择分类
     */
    public static final String APPROVE_QUOTATION_SELECTION_TYPE = "/approve/quotation/selection/type";
    /**
     * 待审核报价单分类选择类别编号
     */
    public static final String APPROVE_QUOTATION_SELECTION_CATEGORY_NUMBER = "/approve/quotation/selection/category/number";
    /**
     * 查价单列表
     */
    public static final String ENQUIRY_LOG_LIST = "/enquiry/log/list";
    /**
     * 待审核报价单检查客户型号
     */
    public static final String CHECK_APPROVE_QUOTATION_CUSTOMER_MODEL = "/check/approve/quotation/customer/model";
    /**
     * 更新待审核报价单
     */
    public static final String SAVE_APPROVE_QUOTATION = "/save/approve/quotation";
    /**
     * 获取客户编码列表
     */
    public static final String CUSTOMER_LIST = "/customer/list";
    /**
     * 根据企业名称获取企业信息
     */
    public static final String CUSTOMER_COMPANY = "/customer/company";
    /**
     * 根据客户企业获取客户信息
     */
    public static final String CUSTOMER_USER = "/customer/user";
    /**
     * excel文件解析
     */
    public static final String EXCEL_FILE_ANALYSIS = "/excel/file/analysis";

    /**
     * excel文件导出价单
     */
    public static final String EXPORT_ENQUIRY_LOG_EXCEL = "/export/enquiry/excel";
    /**
     * 创建报价单
     */
    public static final String CREATE_QUOTATION = "/create/quotation";
    /**
     * 重新同步报价单
     */
    public static final String SYNCHRONIZATION_QUOTATION = "/synchronization/quotation";
    /************************** 订单 *******************************

     /**
     * 订单列表
     */
    public static final String ORDER_LIST = "/order/list";
    /**
     * 订单详情页
     */
    public static final String ORDER_DETAIL = "/order/detail";
    /**
     * 订单详情页备注
     */
    public static final String ORDER_COMMENTS_DETAIL_OTHER_EDIT = "/order/comments/detail/other/edit";
    /**
     * 订单取消列表
     */
    public static final String ORDER_CANCEL_LIST = "/order/cancel/list";
    /**
     * 订单取消详情页
     */
    public static final String ORDER_CANCEL_DETAIL = "/order/cancel/detail";
    /**
     * 取消初始化接口
     */
    public static final String ORDER_CANCEL_BASICS = "/order/cancel/basics";
    /**
     * 订单取消申请提交
     */
    public static final String APPLY_ORDER_CANCEL = "/order/cancel/apply";
    /**
     * 订单发票列表
     */
    public static final String ORDER_INVOICE_LIST = "/order/invoice/list";
    /**
     * 订单发票详情
     */
    public static final String ORDER_INVOICE_DETAIL = "/order/invoice/detail";
    /**
     * 订单售后列表
     */
    public static final String ORDER_AFTER_SALE_LIST = "/order/after/sale/list";
    /**
     * 订单售后详情
     */
    public static final String ORDER_AFTER_SALE_DETAIL = "/order/after/sale/detail";
    /**
     * 订单评价列表
     */
    public static final String ORDER_COMMENTS_LIST = "/order/comments/list";
    /**
     * 订单评价详情
     */
    public static final String ORDER_COMMENTS_DETAIL = "/order/comments/detail";
    /**
     * 订单评论回复
     */
    public static final String ORDER_COMMENTS_REPLY = "/order/comments/reply";
    /**
     * 订单评论问题分类列表
     */
    public static final String ORDER_COMMENTS_PROBLEM_CATEGORIES_LIST = "/order/comments/problem/categories/list";
    /**
     * 创建订单同步
     */
    public static final String SYNCHRONIZATION_CREATE_ORDER = "/synchronization/create/order";
    /**
     * 取消订单同步
     */
    public static final String SYNCHRONIZATION_CANCEL_ORDER = "/synchronization/cancel/order";
    /**
     * 同步订单发票
     */
    public static final String SYNCHRONIZATION_ORDER_INVOICE = "/synchronization/order/invoice";
    /**
     * 同步订单售后
     */
    public static final String SYNCHRONIZATION_ORDER_AFTER_SALE = "/synchronization/order/after/sale";

    /**
     * 同步订单收款单
     */
    public static final String SYNCHRONIZATION_ORDER_COLLECTION = "/synchronization/order/collection";

    /**
     * 修改订单支付流水号
     */
    public static final String UPDATE_ORDER_PAYMENT_SERIAL_NUMBER = "/update/order/payment/serial/number";

    /**
     * 检查昨天用户是否有下订单
     */
    public static final String CHECK_YESTERDAY_USER_ORDERS = "/order/check/yesterday/users";

    /**
     * 更新订单砸金蛋状态
     */
    public static final String UPDATE_ORDER_EGG_STATUS = "/update/order/egg/status";

    /**
     * 获取最后下单日期
     */
    public static final String LAST_ORDER_DATE = "/last-order-date";

    /**
     * 营销订单完成
     */
    public static final String MARKETING_COUPON_ORDER_FINISH = "/coupon/order/finish";

    /**
     * 营销订单统计
     */
    public static final String MARKETING_COUPON_ORDER_COUNT = "/coupon/order/count";

    public static final String UPDATE_INVOICE_INFO_COMPANY_NAME = "/update/invoice/company/name";

    private UriConstant() {
    }
}
