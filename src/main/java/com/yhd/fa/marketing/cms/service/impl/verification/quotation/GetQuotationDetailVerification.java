package com.yhd.fa.marketing.cms.service.impl.verification.quotation;

import cn.hutool.core.collection.CollUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.dao.QuotationClickHouseDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationClickHousePO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationDetailResponseVO;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version Id: GetQuotationDetailVerification.java, v0.1 2022/12/6 11:17 yehuasheng Exp $
 */
@Component
public class GetQuotationDetailVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetQuotationDetailVerification.class.getName());

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    @Resource
    private QuotationClickHouseDAO quotationClickHouseDAO;

    /**
     * 检查报价单的id并且查询报价单信息
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<QuotationDetailResponseVO>
     */
    public BusinessResponse<QuotationDetailResponseVO> check(String quotationId) {
        logger.info("check quotation detail quotation id");

        // 判断报价单id是否为空
        if (StringUtils.isBlank(quotationId)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_ID_IS_EMPTY);
        }

        // 获取查询条件
        MPJLambdaWrapper<QuotationPO> queryWrapper = setQueryWrapper(quotationId);

        // 获取报价单
        QuotationDetailResponseVO quotationDetailResponseVO = quotationMapper.selectJoinOne(QuotationDetailResponseVO.class, queryWrapper);

        // 判断报价单是否不存在
        if (!Optional.ofNullable(quotationDetailResponseVO).isPresent()) {

            QuotationDetailResponseVO quotationDetailCHResponseVO = quotationClickHouseDAO.selectJoinOne(QuotationDetailResponseVO.class, setClickHouseQueryWrapper(quotationId));
            if (!Optional.ofNullable(quotationDetailCHResponseVO).isPresent()) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_IS_EMPTY);
            }
            return BusinessResponseCommon.ok(quotationDetailCHResponseVO);
        }

        return BusinessResponseCommon.ok(quotationDetailResponseVO);
    }

    /**
     * 设置查询条件
     *
     * @param quotationId 报价单id
     * @return MPJLambdaWrapper<QuotationPO>
     */
    private MPJLambdaWrapper<QuotationPO> setQueryWrapper(String quotationId) {
        logger.info("set query quotation lambda wrapper.");

        MPJLambdaWrapper<QuotationPO> queryWrapper = new MPJLambdaWrapper<QuotationPO>()
                .eq(QuotationPO::getId, quotationId)
                .selectAs(QuotationPO::getId, QuotationDetailResponseVO::getQuotationId)
                .selectAs(QuotationPO::getQuotationNumber, QuotationDetailResponseVO::getQuotationNumber)
                .selectAs(QuotationPO::getCustomerQuotationNumber, QuotationDetailResponseVO::getCustomerQuotationNumber)
                .selectAs(QuotationPO::getUserCode, QuotationDetailResponseVO::getUserCode)
                .selectAs(QuotationPO::getPurchaseUserCode, QuotationDetailResponseVO::getPurchaseUserCode)
                .selectAs(QuotationPO::getCompanyCode, QuotationDetailResponseVO::getCompanyCode)
                .selectAs(QuotationPO::getCompanyName, QuotationDetailResponseVO::getCompanyName)
                .selectAs(QuotationPO::getPayablePrice, QuotationDetailResponseVO::getTotalPrice)
                .selectAs(QuotationPO::getCreatedBy, QuotationDetailResponseVO::getCreatedBy)
                .selectAs(QuotationPO::getDeleteStatus, QuotationDetailResponseVO::getDeleteStatus)
                .selectAs(QuotationPO::getQuotationStatus, QuotationDetailResponseVO::getQuotationStatus)
                .selectAs(QuotationPO::getIsInsideCreated, QuotationDetailResponseVO::getIsInsideCreated)
                .selectAs(QuotationPO::getInsideEmployeeCode, QuotationDetailResponseVO::getInsideEmployeeCode)
                .selectAs(QuotationPO::getEmployeeName, QuotationDetailResponseVO::getEmployeeName)
                .selectAs(QuotationPO::getSynchronizationStatus, QuotationDetailResponseVO::getSynchronizationStatus)
                .selectAs(QuotationPO::getSynchronizationDate, QuotationDetailResponseVO::getSynchronizeDate)
                .selectAs(QuotationPO::getCreatedDate, QuotationDetailResponseVO::getCreatedDate)
                .selectAs(QuotationPO::getExamineDate, QuotationDetailResponseVO::getExamineDate)
                .selectAs(QuotationPO::getQuotationCompletionDate, QuotationDetailResponseVO::getQuotationCompletionDate)
                .selectAs(QuotationPO::getTransferOrder, QuotationDetailResponseVO::getTransferOrder)
                .selectAs(QuotationPO::getIsExamine, QuotationDetailResponseVO::getIsExamine);

        // 判断是否业务员
        List<String> companyCodeList = SecurityUtil.getOperatorCompanyCode();
        if (CollUtil.isNotEmpty(companyCodeList)) {
            queryWrapper.in(QuotationPO::getCompanyCode, companyCodeList);
        }

        return queryWrapper;
    }

    /**
     * 设置查询条件
     *
     * @param quotationId 报价单id
     * @return MPJLambdaWrapper<QuotationPO>
     */
    private MPJLambdaWrapper<QuotationClickHousePO> setClickHouseQueryWrapper(String quotationId) {
        logger.info("set query quotation lambda wrapper.");

        MPJLambdaWrapper<QuotationClickHousePO> queryWrapper = new MPJLambdaWrapper<QuotationClickHousePO>()
                .eq(QuotationClickHousePO::getId, quotationId)
                .selectAs(QuotationClickHousePO::getId, QuotationDetailResponseVO::getQuotationId)
                .selectAs(QuotationClickHousePO::getQuotationNumber, QuotationDetailResponseVO::getQuotationNumber)
                .selectAs(QuotationClickHousePO::getCustomerQuotationNumber, QuotationDetailResponseVO::getCustomerQuotationNumber)
                .selectAs(QuotationClickHousePO::getUserCode, QuotationDetailResponseVO::getUserCode)
                .selectAs(QuotationClickHousePO::getPurchaseUserCode, QuotationDetailResponseVO::getPurchaseUserCode)
                .selectAs(QuotationClickHousePO::getCompanyCode, QuotationDetailResponseVO::getCompanyCode)
                .selectAs(QuotationClickHousePO::getCompanyName, QuotationDetailResponseVO::getCompanyName)
                .selectAs(QuotationClickHousePO::getPayablePrice, QuotationDetailResponseVO::getTotalPrice)
                .selectAs(QuotationClickHousePO::getCreatedBy, QuotationDetailResponseVO::getCreatedBy)
                .selectAs(QuotationClickHousePO::getDeleteStatus, QuotationDetailResponseVO::getDeleteStatus)
                .selectAs(QuotationClickHousePO::getQuotationStatus, QuotationDetailResponseVO::getQuotationStatus)
                .selectAs(QuotationClickHousePO::getIsInsideCreated, QuotationDetailResponseVO::getIsInsideCreated)
                .selectAs(QuotationClickHousePO::getInsideEmployeeCode, QuotationDetailResponseVO::getInsideEmployeeCode)
                .selectAs(QuotationClickHousePO::getEmployeeName, QuotationDetailResponseVO::getEmployeeName)
                .selectAs(QuotationClickHousePO::getSynchronizationStatus, QuotationDetailResponseVO::getSynchronizationStatus)
                .selectAs(QuotationClickHousePO::getSynchronizationDate, QuotationDetailResponseVO::getSynchronizeDate)
                .selectAs(QuotationClickHousePO::getCreatedDate, QuotationDetailResponseVO::getCreatedDate)
                .selectAs(QuotationClickHousePO::getExamineDate, QuotationDetailResponseVO::getExamineDate)
                .selectAs(QuotationClickHousePO::getQuotationCompletionDate, QuotationDetailResponseVO::getQuotationCompletionDate)
                .selectAs(QuotationClickHousePO::getTransferOrder, QuotationDetailResponseVO::getTransferOrder)
                .selectAs(QuotationClickHousePO::getIsExamine, QuotationDetailResponseVO::getIsExamine);

        // 判断是否业务员
        List<String> companyCodeList = SecurityUtil.getOperatorCompanyCode();
        if (CollUtil.isNotEmpty(companyCodeList)) {
            queryWrapper.in(QuotationClickHousePO::getCompanyCode, companyCodeList);
        }

        return queryWrapper;
    }
}
