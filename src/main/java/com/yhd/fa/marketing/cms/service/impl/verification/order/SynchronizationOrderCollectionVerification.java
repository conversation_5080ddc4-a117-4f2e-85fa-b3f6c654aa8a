package com.yhd.fa.marketing.cms.service.impl.verification.order;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCollectionLogMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderCollectionLogPO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: SynchronizationOrderCollectionVerification.java, v0.1 2023/3/29 8:28 yehuasheng Exp $
 */
@Component
public class SynchronizationOrderCollectionVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SynchronizationOrderCollectionVerification.class.getName());

    /**
     * 收款单mapper
     */
    @Resource
    private OrderCollectionLogMapper orderCollectionLogMapper;

    /**
     * 检查订单收款单参数
     *
     * @param orderNumber 订单号
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<OrderCollectionLogPO> check(String orderNumber) {
        logger.info("check synchronization order collection parameter.");

        // 查询收款单记录
        OrderCollectionLogPO orderCollectionLog = orderCollectionLogMapper
                .getOne(new LambdaQueryWrapper<OrderCollectionLogPO>()
                        .eq(OrderCollectionLogPO::getOrderNumber, orderNumber)
                        .orderByDesc(OrderCollectionLogPO::getOrderNumber)
                        .last(FaDocMarketingCmsConstant.LIMIT));

        return ObjectUtil.isNull(orderCollectionLog) ? BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_COLLECTION_LOG_IS_NOT_EXIST) : BusinessResponseCommon.ok(orderCollectionLog);
    }
}
