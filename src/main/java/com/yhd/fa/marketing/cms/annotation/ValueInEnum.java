package com.yhd.fa.marketing.cms.annotation;

import com.yhd.fa.marketing.cms.aop.ValueInEnumAOP;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version Id: ValueInEnum.java, v0.1 2022/8/24 11:14 yehuasheng Exp $
 */
@Documented
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValueInEnumAOP.class) //标明哪个类校验
public @interface ValueInEnum {
    /**
     * 输出的内容
     *
     * @return String
     */
    String message() default "枚举不匹配";

    /**
     * 匹配对象
     *
     * @return String[]
     */
    String[] matchTarget() default {};

    /**
     * 组
     *
     * @return Class[]
     */
    Class[] groups() default {};

    /**
     * Payload
     *
     * @return Class<? extends Payload>[]
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 是否必填
     *
     * @return boolean
     */
    boolean required() default true;
}
