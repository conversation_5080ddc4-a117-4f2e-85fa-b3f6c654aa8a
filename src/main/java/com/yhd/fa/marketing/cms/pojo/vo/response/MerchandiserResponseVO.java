package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: MerchandiserResponseVO.java, v0.1 2023/4/3 11:23 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchandiserResponseVO extends BaseVO {
    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 用户工号
     */
    private String employeeCode;

    /**
     * 用户名称
     */
    private String employeeName;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * qq
     */
    private String qq;
}
