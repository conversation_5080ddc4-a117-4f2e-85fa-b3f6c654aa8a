package com.yhd.fa.marketing.cms.service.impl.verification.quotation;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.QuotationDeleteStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: GetApproveQuotationDetailVerification.java, v0.1 2022/12/12 17:30 yehuasheng Exp $
 */
@Component
public class GetApproveQuotationDetailVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetApproveQuotationDetailVerification.class.getName());

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    /**
     * 检查审核报价单是否可以审核 并返回报价单信息
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<QuotationPO
     */
    public BusinessResponse<QuotationPO> check(String quotationId) {
        logger.info("check approve quotation detail verification.");

        // 获取报价单
        QuotationPO quotationInfo = quotationMapper.getById(quotationId);

        // 判断报价单是否存在
        if (ObjectUtil.isEmpty(quotationId)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_IS_EMPTY);
        }

        // 判断报价单是否需要审核
        if (StringUtils.equals(quotationInfo.getIsExamine(), CommonConstant.FALSE) ||
                !StringUtils.equals(quotationInfo.getQuotationStatus(), QuotationStatusConstant.QUOTATION)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_IS_NOT_NEED_REVIEWED);
        }

        // 判断报价单是否已删除或者加入回收站
        if (!StringUtils.equals(quotationInfo.getDeleteStatus(), QuotationDeleteStatusConstant.NORMAL)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_IS_DELETE_NOT_NEED_REVIEWED);
        }

        // 返回报价单信息
        return BusinessResponseCommon.ok(quotationInfo);
    }
}
