package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.BusinessTypeConstant;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.SynchronizeStatusEnum;
import com.yhd.fa.marketing.cms.mapper.OrderAfterSaleListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderAfterSaleMapper;
import com.yhd.fa.marketing.cms.mq.producer.SynchronizationProducer;
import com.yhd.fa.marketing.cms.pojo.dto.FaOrderAfterSaleDetailSynchronizationDTO;
import com.yhd.fa.marketing.cms.pojo.dto.FaOrderAfterSaleSynchronizationDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderAfterSaleListPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderAfterSalePO;
import com.yhd.fa.marketing.cms.util.BaseUtil;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SynchronizationOrderAfterSaleLogic.java, v0.1 2023/3/2 15:34 yehuasheng Exp $
 */
@Component
@RefreshScope
public class SynchronizationOrderAfterSaleLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(SynchronizationOrderAfterSaleLogic.class.getName());

    /**
     * 订单售后mapper
     */
    @Resource
    private OrderAfterSaleMapper orderAfterSaleMapper;

    /**
     * 订单售后明细mapper
     */
    @Resource
    private OrderAfterSaleListMapper orderAfterSaleListMapper;

    /**
     * 同步生产者
     */
    @Resource
    private SynchronizationProducer synchronizationProducer;

    /**
     * 图片域名
     */
    @Value("${image-config.domain}")
    String imageDomain;

    /**
     * 执行订单售后同步
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<Void> exec(String orderAfterSaleId) {
        logger.info("start exec synchronization order after sale logic.");

        // 获取订单售后详情
        OrderAfterSalePO orderAfterSaleInfo = orderAfterSaleMapper.getOne(new MPJLambdaWrapper<OrderAfterSalePO>().selectAll(OrderAfterSalePO.class).eq(OrderAfterSalePO::getId, orderAfterSaleId).last(FaDocMarketingCmsConstant.LIMIT));
        if (ObjectUtil.isNull(orderAfterSaleInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_AFTER_SALE_IS_NOT_EXISTS);
        }

        // 获取订单售后明细
        List<OrderAfterSaleListPO> orderAfterSaleList = orderAfterSaleListMapper.list(new MPJLambdaWrapper<OrderAfterSaleListPO>().selectAll(OrderAfterSaleListPO.class).eq(OrderAfterSaleListPO::getAfterSaleId, orderAfterSaleId));
        if (CollUtil.isEmpty(orderAfterSaleList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_AFTER_SALE_DETAIL_IS_NOT_EXISTS);
        }

        // 执行同步
        synchronization(orderAfterSaleInfo, orderAfterSaleList);

        return BusinessResponse.ok(null);
    }

    /**
     * 执行售后同步
     *
     * @param orderAfterSaleInfo 售后详情
     * @param orderAfterSaleList 售后明细
     */
    @Async("faSynchronizationOrderAfterSale")
    public void synchronization(OrderAfterSalePO orderAfterSaleInfo, List<OrderAfterSaleListPO> orderAfterSaleList) {
        logger.info("start exec synchronization order after sale to mq.");

        // 设置同步参数
        List<FaOrderAfterSaleDetailSynchronizationDTO> faOrderAfterSaleDetailSynchronizationDTO = setSynchronizationOrderAfterSaleDetailData(orderAfterSaleList);

        // 设置同步售后参数
        FaOrderAfterSaleSynchronizationDTO faOrderAfterSaleSynchronizationDTO = setSynchronizationOrderAfterSaleData(orderAfterSaleInfo, faOrderAfterSaleDetailSynchronizationDTO);

        // 设置同步
        synchronizationProducer.synchronizationData(JSONUtil.toJsonStr(faOrderAfterSaleSynchronizationDTO), BusinessTypeConstant.ORDER_AFTER_SALES, orderAfterSaleInfo.getAfterSaleNumber());

        // 更新同步状态
        orderAfterSaleMapper.update(null,
                new LambdaUpdateWrapper<OrderAfterSalePO>()
                        .set(OrderAfterSalePO::getSynchronizationStatus, SynchronizeStatusEnum.SYNCHRONIZING.getStatus())
                        .set(OrderAfterSalePO::getUpdatedDate, LocalDateTime.now())
                        .eq(OrderAfterSalePO::getId, orderAfterSaleInfo.getId())
        );
    }

    /**
     * 设置同步订单售后明细参数
     *
     * @param orderAfterSaleList 订单售后明细
     * @return List<FaOrderAfterSaleDetailSynchronizationDTO>
     */
    private List<FaOrderAfterSaleDetailSynchronizationDTO> setSynchronizationOrderAfterSaleDetailData(List<OrderAfterSaleListPO> orderAfterSaleList) {
        logger.info("set synchronization order after sale detail data.");

        return orderAfterSaleList.stream().map(orderAfterSaleListPO ->
                FaOrderAfterSaleDetailSynchronizationDTO
                        .builder()
                        .id(orderAfterSaleListPO.getId())
                        .afterSaleId(orderAfterSaleListPO.getAfterSaleId())
                        .afterSaleNumber(orderAfterSaleListPO.getAfterSaleNumber())
                        .orderSortId(orderAfterSaleListPO.getOrderSortId())
                        .productCode(orderAfterSaleListPO.getProductCode())
                        .productName(orderAfterSaleListPO.getProductName())
                        .productModel(orderAfterSaleListPO.getProductModel())
                        .quantity(orderAfterSaleListPO.getQuantity())
                        .price(orderAfterSaleListPO.getPrice())
                        .discountPrice(orderAfterSaleListPO.getDiscountPrice())
                        .taxDiscountPrice(orderAfterSaleListPO.getTaxDiscountPrice())
                        .totalPrice(orderAfterSaleListPO.getTotalPrice())
                        .unit(orderAfterSaleListPO.getUnit())
                        .build()
        ).collect(Collectors.toList());
    }

    /**
     * 设置同步订单售后数据
     *
     * @param orderAfterSaleInfo                       订单售后详情
     * @param faOrderAfterSaleDetailSynchronizationDTO 订单售后明细
     * @return FaOrderAfterSaleSynchronizationDTO
     */
    private FaOrderAfterSaleSynchronizationDTO setSynchronizationOrderAfterSaleData(OrderAfterSalePO orderAfterSaleInfo, List<FaOrderAfterSaleDetailSynchronizationDTO> faOrderAfterSaleDetailSynchronizationDTO) {
        logger.info("set synchronization order after sale data.");

        return FaOrderAfterSaleSynchronizationDTO.builder()
                .id(orderAfterSaleInfo.getId())
                .afterSaleNumber(orderAfterSaleInfo.getAfterSaleNumber())
                .orderId(orderAfterSaleInfo.getOrderId())
                .orderNumber(orderAfterSaleInfo.getOrderNumber())
                .orderPaymentType(orderAfterSaleInfo.getOrderPaymentType())
                .userCode(orderAfterSaleInfo.getUserCode())
                .companyCode(orderAfterSaleInfo.getCompanyCode())
                .companyName(orderAfterSaleInfo.getCompanyName())
                .erpCompanyCode(orderAfterSaleInfo.getErpCompanyCode())
                .shipmentReceiptStatus(orderAfterSaleInfo.getShipmentReceiptStatus())
                .afterSaleType(orderAfterSaleInfo.getAfterSaleType())
                .imagePath(BaseUtil.addDomainToImageUrls(imageDomain, orderAfterSaleInfo.getImagePath()))
                .suggestReason(orderAfterSaleInfo.getSuggestReason())
                .problemDescription(orderAfterSaleInfo.getProblemDescription())
                .afterSalePrice(orderAfterSaleInfo.getAfterSalePrice())
                .applicant(orderAfterSaleInfo.getApplicant())
                .applicantPhone(orderAfterSaleInfo.getApplicantPhone())
                .applicantEmail(orderAfterSaleInfo.getApplicantEmail())
                .territory(orderAfterSaleInfo.getTerritory())
                .userReturnCourier(orderAfterSaleInfo.getUserReturnCourier())
                .userReturnTrackingNumber(orderAfterSaleInfo.getUserReturnTrackingNumber())
                .createdDate(orderAfterSaleInfo.getCreatedDate())
                .orderAfterSaleDetails(faOrderAfterSaleDetailSynchronizationDTO)
                .build();
    }
}
