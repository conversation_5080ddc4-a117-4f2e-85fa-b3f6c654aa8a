package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderListResponseVO.java, v0.1 2023/2/16 19:51 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderListResponseVO extends BaseVO {
    /**
     * 订单id
     */
    @Schema(description = "订单id", example = "1348969b8947455995f0fbe5586cbef5")
    private String orderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "YI202212030829479779T5GL")
    private String orderNumber;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态 unpaid 待支付、onWay 在途单、finish 已完成、cancel 已取消、closed已关闭", example = "onWay")
    private String orderStatus;

    /**
     * 取消状态
     */
    @Schema(description = "取消状态 true 已取消, false未取消", example = "false")
    private String cancelStatus;

    /**
     * 删除状态
     */
    @Schema(description = "删除状态 recycleBin 回收站 false 未删除 true 彻底删除", example = "false")
    private String deleteStatus;

    /**
     * 订单状态名称
     */
    @Schema(description = "状态名称", example = "在途单")
    private String orderStatusName;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "HA5814973528")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "139779")
    private String userCode;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称", example = "测试大哥好")
    private String userName;

    /**
     * 跟单员
     */
    @Schema(description = "跟单员", example = "你猜")
    private String merchandiser;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "1311111111")
    private String merchandiserContact;

    /**
     * 业务员名称
     */
    @Schema(description = "业务员名称", example = "01111/林杏金")
    private String salesmanName;

    /**
     * 业务员联系方式
     */
    @Schema(description = "业务员联系方式", example = "13111111111")
    private String salesmanContact;

    /**
     * 计算方式
     */
    @Schema(description = "结算方式 online即时支付 offline线下支付", example = "online")
    private String settlementType;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态 paid已支付 unpaid未支付 paymentFailed支付失败", example = "paid")
    private String paymentStatus;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式 unknown未支付、aliPay支付宝支付、weChatPay微信支付、unionPay企业网银、offlinePay线下支付、monthly信用月结", example = "aliPay")
    private String paymentType;

    /**
     * 支付名称
     */
    @Schema(description = "支付名称", example = "支付宝")
    private String paymentTypeName;

    /**
     * 流水号
     */
    @Schema(description = "流水号", example = "ABC123456")
    private String tradeNo;

    /**
     * 总价
     */
    @Schema(description = "总价格", example = "38.44")
    private BigDecimal totalPrice;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额", example = "38.44")
    private BigDecimal payablePrice;

    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态 wait等待同步 success同步完成 fail同步失败 synchronizing同步中")
    private String synchronizeStatus;

    /**
     * 同步信息
     */
    @Schema(description = "同步信息")
    private String synchronizeMessage;

    /**
     * 订单渠道
     */
    @Schema(description = "下单渠道 pc端 wap手机端 weChat微信 app端 applet小程序", example = "pc")
    private String orderChannel;

    /**
     * 订单渠道名称
     */
    @Schema(description = "订单渠道名称", example = "pc端")
    private String orderChannelName;

    /**
     * 创建订单来源
     */
    @Schema(description = "下单来源 cart购物车 buy一键购买 quotation报价单 offlineQuotation线下报价单 mergeQuotation合并报价单 mergeOfflineQuotation线下合并报价单")
    private String orderSource;

    /**
     * 下单来源名称
     */
    @Schema(description = "下单来源名称", example = "一键购买")
    private String orderSourceName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2020-01-01 10:10:10")
    private LocalDateTime createdDate;
}
