package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.mapper.OrderMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.UserCodeListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CheckUserOrderResponseVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: CheckUserOrderLogic.java, v0.1 2023/5/3 14:27 yehuasheng Exp $
 */
@Component
public class CheckUserOrderLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(CheckUserOrderLogic.class.getName());

    /**
     * 订单mapper
     */
    @Resource
    private OrderMapper orderMapper;

    /**
     * 执行检查用户昨天是否有下单
     *
     * @param userCodeListRequestVO 用户集合参数
     * @return BusinessResponse<List < CheckUserOrderResponseVO>>
     */
    public BusinessResponse<List<CheckUserOrderResponseVO>> exec(UserCodeListRequestVO userCodeListRequestVO) {
        logger.info("start exec check user order logic.");

        // 设置日期
        LocalDate nowDate = LocalDate.now();
        LocalDate yesterday = nowDate.minusDays(CommonConstant.ONE);

        // 查询用户的订单
        List<OrderPO> orderList = orderMapper.list(new MPJLambdaWrapper<OrderPO>()
                .select(OrderPO::getId, OrderPO::getUserCode)
                .in(OrderPO::getUserCode, userCodeListRequestVO.getUserCodeList().stream().distinct().collect(Collectors.toList()))
                .ge(OrderPO::getCreatedDate, yesterday)
                .lt(OrderPO::getCreatedDate, nowDate));

        // 转换返回参数
        Map<String, List<String>> orderMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orderList)) {
            orderMap.putAll(orderList
                    .stream()
                    .collect(Collectors
                            .groupingBy(OrderPO::getUserCode, Collectors
                                    .mapping(OrderPO::getId, Collectors.toList()))));
        }
        List<CheckUserOrderResponseVO> checkUserOrderList = userCodeListRequestVO.getUserCodeList()
                .stream()
                .map(userCode
                        -> CheckUserOrderResponseVO
                        .builder()
                        .userCode(userCode)
                        .hadOrders(orderMap.containsKey(userCode))
                        .build())
                .collect(Collectors.toList());

        return BusinessResponse.ok(checkUserOrderList);
    }
}
