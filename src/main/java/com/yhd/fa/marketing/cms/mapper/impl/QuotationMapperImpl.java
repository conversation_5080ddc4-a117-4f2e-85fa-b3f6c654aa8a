package com.yhd.fa.marketing.cms.mapper.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.QuotationDAO;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version Id: QuotationMapperImpl.java, v0.1 2022/12/2 11:10 yehuasheng Exp $
 */
@Service
@DS("quotation")
public class QuotationMapperImpl extends MPJBaseServiceImpl<QuotationDAO, QuotationPO> implements QuotationMapper {

}
