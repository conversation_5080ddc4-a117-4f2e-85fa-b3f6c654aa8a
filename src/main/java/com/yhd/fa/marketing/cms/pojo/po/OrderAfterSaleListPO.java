package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_after_sale_list")
public class OrderAfterSaleListPO extends BaseEntity {

    /**
     * 售后id
     */
    private String afterSaleId;

    /**
     * 售后单号
     */
    private String afterSaleNumber;

    /**
     * 订单明细序号
     */
    private Integer orderSortId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣后含税单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总金额
     */
    private BigDecimal totalPrice;

    /**
     * 产品单位
     */
    private String unit;

}

