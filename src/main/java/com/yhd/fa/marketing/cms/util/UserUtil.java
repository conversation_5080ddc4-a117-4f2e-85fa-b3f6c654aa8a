package com.yhd.fa.marketing.cms.util;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.buc.cms.api.sdk.enums.FaAuthEnum;
import com.yhd.buc.cms.api.sdk.enums.FaRoleEnum;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.ResourcesInfo;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: UserUtil.java, v0.1 2022/12/23 11:22 yehuasheng Exp $
 */
public class UserUtil {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(UserUtil.class.getName());

    private UserUtil() {
    }

    /**
     * 是否有采购权限
     *
     * @param userInfo 用户信息
     * @return boolean
     */
    public static boolean haveCreateOrderAuthority(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("check user have create order authority.");

        // 判断是否个人用户，如果是则返回不能下单
        if (StringUtils.equals(userInfo.getUserRole().getRoleCode(), FaRoleEnum.YHD_FA_USER_GE_REN_YONG_HU.getCode())
                // 判断是否升级中的用户
                || StringUtils.equals(userInfo.getUserRole().getRoleCode(), FaRoleEnum.YHD_FA_USER_DAI_REN_ZHENG_YONG_HU.getCode())) {
            return false;
        }

        // 判断是否普通企业，如果是则返回可以下单
        if (ObjectUtil.isNotNull(userInfo.getCompanyInfo()) && StringUtils.equals(userInfo.getCompanyRole().getRoleCode(), FaRoleEnum.YHD_FA_COMPANY_PU_TONG_QI_YE.getCode())) {
            return true;
        }

        // 剩余就是认证企业

        // 提取用户禁止的操作权限
        List<String> prohibitPermissions = getProhibitPermissions(userInfo.getResourcesCodeClosedList());

        // 判断用户是否管理员
        return StringUtils.equals(userInfo.getUserInfo().getAdmin(), CommonConstant.TRUE) || !prohibitPermissions.contains(FaAuthEnum.YHD_FA_CHUANG_JIAN_DING_DAN.getCode());
    }

    /**
     * 是否没有创建报价单权限
     *
     * @param userInfo 用户信息
     * @return boolean
     */
    public static boolean haveNotCreateQuotationAuthority(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("check user have create quotation authority.");

        return getProhibitPermissions(userInfo.getResourcesCodeClosedList()).contains(FaAuthEnum.YHD_FA_CHUANG_JIAN_BAO_JIA.getCode());
    }

    /**
     * 获取用户询价的类型
     *
     * @param userInfo 用户信息
     * @return int
     */
    public static int getUserEnquiryRole(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("get quotation user role.");

        int enquiryRole = CommonConstant.ZERO;

        // 判断用户是否企业用户
        if (ObjectUtil.isNotNull(userInfo.getCompanyInfo()) && StringUtils.isNotBlank(userInfo.getCompanyInfo().getCompanyCode())) {
            enquiryRole = CommonConstant.ONE;
            // 判断用户如果是认证企业用户并且是超级管理员的那么就设置类型为2
            List<String> prohibitPermissions = getProhibitPermissions(userInfo.getResourcesCodeClosedList());
            if ((StringUtils.equals(userInfo.getUserRole().getRoleCode(), FaRoleEnum.YHD_FA_USER_REN_ZHENG_QI_YE_YONG_HU.getCode()) && !prohibitPermissions.contains(FaAuthEnum.YHD_FA_CHA_JIA.getCode()))
                    // 或者他是管理员的
                    || StringUtils.equals(userInfo.getUserInfo().getAdmin(), CommonConstant.TRUE)) {
                enquiryRole = CommonConstant.TWO;
            }
        }

        return enquiryRole;
    }

    /**
     * 是否没有询价权限
     *
     * @param userInfo 用户信息
     * @return boolean
     */
    public static boolean haveNotEnquiryPriceAuthority(CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("check user have enquiry price authority.");

        return getProhibitPermissions(userInfo.getResourcesCodeClosedList()).contains(FaAuthEnum.YHD_FA_XUN_JIA.getCode());
    }


    /**
     * 提取用户的禁止操作权限
     *
     * @return List<String>
     */
    private static List<String> getProhibitPermissions(List<ResourcesInfo> resourcesInfoList) {
        return Optional.ofNullable(resourcesInfoList)
                .map(interiorResources
                        -> interiorResources
                        .stream()
                        .map(ResourcesInfo::getResourcesCode)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }
}
