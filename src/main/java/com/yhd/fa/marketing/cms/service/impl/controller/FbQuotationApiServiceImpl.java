package com.yhd.fa.marketing.cms.service.impl.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.BaseUserInfoRequestVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.CompanyAndMerchandiserAndSalesmanInfoRequestVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.CompanyInfoListByJobNumberRequestVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.request.ErpSalesInfoRequestVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndMerchandiserAndSalesmanInfoResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.ErpSalesInfoResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.ErpCompanyInfo;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.MerchandiserInfo;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.UserInfo;
import com.yhd.buc.cms.api.sdk.utils.UserApiUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.BaseConstant;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.QuotationDeleteStatusConstant;
import com.yhd.fa.marketing.cms.dao.FbQuotationDAO;
import com.yhd.fa.marketing.cms.dao.FbQuotationListDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.FbQuotationMapper;
import com.yhd.fa.marketing.cms.pojo.po.FbQuotationListPO;
import com.yhd.fa.marketing.cms.pojo.po.FbQuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationAndOrderDeleteRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationPageRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.*;
import com.yhd.fa.marketing.cms.service.controller.FbQuotationApiService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/6/18 16:08
 */
@Service
@DS("quotation")
public class FbQuotationApiServiceImpl implements FbQuotationApiService {
    @Resource
    private FbQuotationDAO fbQuotationDAO;

    @Resource
    private FbQuotationListDAO fbQuotationListDAO;

    @Resource
    private FbQuotationMapper fbQuotationMapper;

    private static final org.slf4j.Logger logger = LogUtils.getLogger(FbQuotationApiServiceImpl.class.getName());


    @Override
    public BusinessResponse<PageInfo<FbQuotationListResponseVO>> getFbQuotationPage(FbQuotationPageRequestVO fbQuotationPageRequestVO) {

        //数据权限处理

        fbQuotationPageRequestVO.parseDate();

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getEmpNo())) {
            //获取当前员工下的企业编码
            CompanyInfoListByJobNumberRequestVO companyInfoListByJobNumberRequestVO = new CompanyInfoListByJobNumberRequestVO();

            companyInfoListByJobNumberRequestVO.setJobNumber(fbQuotationPageRequestVO.getEmpNo());

            companyInfoListByJobNumberRequestVO.setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE);

            List<String> companyCodeList = UserApiUtil.getCompanyCodeListByJobNumberMethod(companyInfoListByJobNumberRequestVO);

            if (CollUtil.isEmpty(companyCodeList)) {
                return BusinessResponse.ok(PageInfo.of(new ArrayList<>()));
            }

            fbQuotationPageRequestVO.setCompanyCodeList(companyCodeList);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getUnitName()) && fbQuotationPageRequestVO.getUnitName().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setUnitName(CommonConstant.PERCENT + fbQuotationPageRequestVO.getUnitName() + CommonConstant.PERCENT);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getCompanyName()) && fbQuotationPageRequestVO.getCompanyName().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setCompanyName(CommonConstant.PERCENT + fbQuotationPageRequestVO.getCompanyName() + CommonConstant.PERCENT);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getQuotationNumber()) && fbQuotationPageRequestVO.getQuotationNumber().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setQuotationNumber(fbQuotationPageRequestVO.getQuotationNumber() + CommonConstant.PERCENT);
        }

        if (fbQuotationPageRequestVO.getPageNum() != 0) {
            PageMethod.startPage(fbQuotationPageRequestVO.getPageNum(), fbQuotationPageRequestVO.getPageSize());
        }

        List<FbQuotationListResponseVO> fbOrderListResponseVOList =
                fbQuotationDAO.selectFbQuotationPage(fbQuotationPageRequestVO);

        if (CollUtil.isEmpty(fbOrderListResponseVOList)) {
            return BusinessResponse.ok(PageInfo.emptyPageInfo());
        }

        PageInfo<FbQuotationListResponseVO> pageInfo = PageInfo.of(fbOrderListResponseVOList);

        try {
            List<String> userCodeList = fbOrderListResponseVOList.stream().map(FbQuotationListResponseVO::getUserCode).collect(Collectors.toList());
            List<String> purchaseUserCodeList = fbOrderListResponseVOList.stream().map(FbQuotationListResponseVO::getPurchaseUserCode).collect(Collectors.toList());

            userCodeList.addAll(purchaseUserCodeList);
            HashSet<String> hashSet = new HashSet<>(userCodeList);
            BaseUserInfoRequestVO baseUserInfoRequestVO = new BaseUserInfoRequestVO();
            baseUserInfoRequestVO.setUserCodeList(hashSet);
            List<UserInfo> userInfoByUserCodesOrUserAccounts = UserApiUtil.getUserInfoByUserCodesOrUserAccounts(baseUserInfoRequestVO);
            Map<String, UserInfo> userInfoMap = userInfoByUserCodesOrUserAccounts.stream().collect(Collectors.toMap(UserInfo::getUserCode, v -> v, (k1, k2) -> k1));
            pageInfo.getList().forEach(e -> {
                String userCode = e.getUserCode();
                String purchaseUserCode = e.getPurchaseUserCode();
                UserInfo userInfo = userInfoMap.get(userCode);
                if (Objects.nonNull(userInfo)) {
                    e.setUserCode(StringUtils.join(StringUtils.EMPTY, userInfo.getUserName(), CommonConstant.LEFT_BRACKET, userInfo.getUserCode(), CommonConstant.RIGHT_BRACKET));
                }

                UserInfo purchaseUserInfo = userInfoMap.get(purchaseUserCode);
                if (Objects.nonNull(purchaseUserInfo)) {
                    e.setPurchaseUserCode(StringUtils.join(StringUtils.EMPTY, purchaseUserInfo.getUserName(), CommonConstant.LEFT_BRACKET, purchaseUserInfo.getUserCode(), CommonConstant.RIGHT_BRACKET));
                }
            });
        } catch (Exception e) {
            logger.error("get user name fail {} ", e.getMessage());
        }

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * Fb报价单详情数据获取
     *
     * @param id
     * @param quotationNumber
     * @return
     */
    @Override
    public BusinessResponse<FbQuotationDetailResponseVO> getFbQuotationDetail(String id, String quotationNumber) {
        FbQuotationPO fbQuotationPO = null;
        if (StringUtils.isNotBlank(id)) {
            fbQuotationPO = fbQuotationDAO.selectById(id);
        } else if (StringUtils.isNotBlank(quotationNumber)) {
            LambdaQueryWrapper<FbQuotationPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FbQuotationPO::getQuotationNumber, quotationNumber);
            fbQuotationPO = fbQuotationDAO.selectOne(queryWrapper);
        } else {
            return BusinessResponse.fail(FaDocMarketingResponseEnum.PARAM_ERROR.getCode(), FaDocMarketingResponseEnum.PARAM_ERROR.getDesc());
        }

        if (Objects.isNull(fbQuotationPO)) {
            return BusinessResponse.ok(null);
        }
        //查询报价单明细
        LambdaQueryWrapper<FbQuotationListPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FbQuotationListPO::getQuotationId, fbQuotationPO.getId()).orderByAsc(FbQuotationListPO::getSort);
        List<FbQuotationListPO> fbQuotationListPOS = fbQuotationListDAO.selectList(queryWrapper);
        List<FbQuotationDetailChildResponseVO> fbQuotationDetailChildResponseVOList = fbQuotationListPOS.stream().map(m -> {
            FbQuotationDetailChildResponseVO fbQuotationDetailChildResponseVO = new FbQuotationDetailChildResponseVO();
            BeanUtils.copyProperties(m, fbQuotationDetailChildResponseVO);
            if (StringUtils.isNotBlank(m.getTotalDiscountRate())) {
                fbQuotationDetailChildResponseVO.setTotalDiscountRate(new BigDecimal(m.getTotalDiscountRate()).multiply(new BigDecimal(100)).toString() + CommonConstant.PERCENT);
            }
            return fbQuotationDetailChildResponseVO;
        }).collect(Collectors.toList());


        FbQuotationDetailResponseVO fbQuotationDetailResponseVO = new FbQuotationDetailResponseVO();
        BeanUtils.copyProperties(fbQuotationPO, fbQuotationDetailResponseVO);
        fbQuotationDetailResponseVO.setCreatedDate(fbQuotationPO.getCreatedDate().format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
        fbQuotationDetailResponseVO.setFbQuotationDetailChildResponseVOList(fbQuotationDetailChildResponseVOList);


        try {
            BaseUserInfoRequestVO baseUserInfoRequestVO = new BaseUserInfoRequestVO();
            baseUserInfoRequestVO.setUserCodeList(new HashSet<>(Arrays.asList(fbQuotationDetailResponseVO.getUserCode(), fbQuotationDetailResponseVO.getPurchaseUserCode())));
            List<UserInfo> userInfoByUserCodesOrUserAccounts = UserApiUtil.getUserInfoByUserCodesOrUserAccounts(baseUserInfoRequestVO);
            Map<String, UserInfo> userInfoMap = userInfoByUserCodesOrUserAccounts.stream().collect(Collectors.toMap(UserInfo::getUserCode, v -> v, (k1, k2) -> k1));
            UserInfo createdUserInfo = userInfoMap.get(fbQuotationDetailResponseVO.getUserCode());
            if (Objects.nonNull(createdUserInfo)) {
                fbQuotationDetailResponseVO.setUserCode(StringUtils.join(StringUtils.EMPTY, createdUserInfo.getUserName(), CommonConstant.LEFT_BRACKET, createdUserInfo.getUserCode(), CommonConstant.RIGHT_BRACKET));
            }

            if (StringUtils.isNotBlank(fbQuotationDetailResponseVO.getPurchaseUserCode())) {
                UserInfo purchaseUseruserInfo = userInfoMap.get(fbQuotationDetailResponseVO.getPurchaseUserCode());
                if (Objects.nonNull(purchaseUseruserInfo)) {
                    fbQuotationDetailResponseVO.setPurchaseUserCode(StringUtils.join(StringUtils.EMPTY, purchaseUseruserInfo.getUserName(), CommonConstant.LEFT_BRACKET, createdUserInfo.getUserCode(), CommonConstant.RIGHT_BRACKET));
                }
            }


        } catch (Exception e) {
            logger.error("set user code info fail {}", e.getMessage());
        }


        return BusinessResponse.ok(fbQuotationDetailResponseVO);
    }

    /**
     * 报价单数据清洗
     *
     * @return
     */
    @Override
    public BusinessResponse<FbQuotationDetailResponseVO> fbQuotationInit() {

        int batchSize = 100;

        LambdaQueryWrapper<FbQuotationPO> queryWrapper = new LambdaQueryWrapper<>();
        Long count = fbQuotationDAO.selectCount(queryWrapper);

        long pageSize = (count / batchSize) + 1;

        for (int i = 1; i <= pageSize; i++) {
            LambdaQueryWrapper<FbQuotationPO> inner = new LambdaQueryWrapper<>();
            inner.select(FbQuotationPO::getId, FbQuotationPO::getCompanyCode).orderByAsc(FbQuotationPO::getCreatedDate).last("limit " + batchSize + " offset " + (i - 1) * batchSize);
            List<FbQuotationPO> fbQuotationPOS = fbQuotationDAO.selectList(inner);
            List<String> companyCodeList = fbQuotationPOS.stream().map(FbQuotationPO::getCompanyCode).distinct().collect(Collectors.toList());

            //1.获取专属跟单
            CompanyAndMerchandiserAndSalesmanInfoRequestVO companyAndMerchandiserAndSalesmanInfoRequestVO = new CompanyAndMerchandiserAndSalesmanInfoRequestVO();

            companyAndMerchandiserAndSalesmanInfoRequestVO.setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE);

            companyAndMerchandiserAndSalesmanInfoRequestVO.setCompanyCodeList(companyCodeList);

            List<CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyAndMerchandiserAndSalesmanInfoResponseVOS
                    = UserApiUtil.companyAndMerchandiserAndSalesmanInfo(companyAndMerchandiserAndSalesmanInfoRequestVO);

            Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> merchandiserAndSalesmanInfoResponseVOMap = companyAndMerchandiserAndSalesmanInfoResponseVOS.stream()
                    .collect(Collectors.toMap(CompanyAndMerchandiserAndSalesmanInfoResponseVO::getCompanyCode, m -> m, (k1, k2) -> k1));

            //2.获取企业归属地
            Map<String, ErpCompanyInfo> erpCompanyInfo = UserApiUtil.getErpCompanyInfo(companyAndMerchandiserAndSalesmanInfoRequestVO);


            //业务员工号集合
            List<String> salesCodeList = companyAndMerchandiserAndSalesmanInfoResponseVOS
                    .stream()
                    .map(m -> Objects.isNull(m.getSalesManInfo()) ? null : m.getSalesManInfo().getEmployeeCode())
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            //3.获取业务员所属部门
            ErpSalesInfoRequestVO erpSalesInfoRequestVO = new ErpSalesInfoRequestVO();
            erpSalesInfoRequestVO.setEmployeeCodeList(salesCodeList);
            Map<String, ErpSalesInfoResponseVO> erpSalesInfo = UserApiUtil.getErpSalesInfo(erpSalesInfoRequestVO);


            fbQuotationPOS.forEach(e -> {
                String companyCode = e.getCompanyCode();
                //1.业务员、专属跟单
                CompanyAndMerchandiserAndSalesmanInfoResponseVO companyAndMerchandiserAndSalesmanInfoResponseVO = merchandiserAndSalesmanInfoResponseVOMap.get(companyCode);
                MerchandiserInfo salesManInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getSalesManInfo();
                MerchandiserInfo merchandiserInfo = companyAndMerchandiserAndSalesmanInfoResponseVO.getMerchandiserInfo();

                if (Objects.nonNull(salesManInfo)) {
                    e.setOperator(StringUtils.join(StringUtils.EMPTY, salesManInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, salesManInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET));
                }
                if (Objects.nonNull(merchandiserInfo)) {
                    e.setMerchandiser(StringUtils.join(StringUtils.EMPTY, merchandiserInfo.getEmployeeName(), CommonConstant.LEFT_BRACKET, merchandiserInfo.getEmployeeCode(), CommonConstant.RIGHT_BRACKET));
                }

                //2.客户归属
                ErpCompanyInfo companyInfo = erpCompanyInfo.get(companyCode);
                if (Objects.nonNull(companyInfo)) {
                    e.setOwnershipCompany(companyInfo.getOwnershipCompany());
                }

                if (Objects.nonNull(erpSalesInfo) && Objects.nonNull(salesManInfo)) {
                    //3.销售部门
                    ErpSalesInfoResponseVO erpSalesInfoResponseVO = erpSalesInfo.get(salesManInfo.getEmployeeCode());
                    if (Objects.nonNull(erpSalesInfoResponseVO)) {
                        e.setUnitName(erpSalesInfoResponseVO.getUnitName());
                    }
                }


            });
            fbQuotationMapper.updateBatchById(fbQuotationPOS);
        }
        return BusinessResponse.ok(null);
    }

    /**
     * Fb报价单单据统计金额
     *
     * @param fbQuotationPageRequestVO
     * @return
     */
    @Override
    public BusinessResponse<FbQuotationTotalPriceCountResponseVO> getFbQuotationCount(FbQuotationPageRequestVO fbQuotationPageRequestVO) {
        fbQuotationPageRequestVO.parseDate();
        FbQuotationTotalPriceCountResponseVO fbQuotationTotalPriceCountResponseVO = new FbQuotationTotalPriceCountResponseVO();
        //数据权限
        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getEmpNo())) {
            //获取当前员工下的企业编码
            CompanyInfoListByJobNumberRequestVO companyInfoListByJobNumberRequestVO = new CompanyInfoListByJobNumberRequestVO();

            companyInfoListByJobNumberRequestVO.setJobNumber(fbQuotationPageRequestVO.getEmpNo());

            companyInfoListByJobNumberRequestVO.setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE);

            List<String> companyCodeList = UserApiUtil.getCompanyCodeListByJobNumberMethod(companyInfoListByJobNumberRequestVO);


            if (CollUtil.isEmpty(companyCodeList)) {
                fbQuotationTotalPriceCountResponseVO.setTotalPriceAll(BigDecimal.valueOf(BaseConstant.Zero));
                fbQuotationTotalPriceCountResponseVO.setTotalPricePart(BigDecimal.valueOf(BaseConstant.Zero));
                return BusinessResponse.ok(fbQuotationTotalPriceCountResponseVO);
            }
            fbQuotationPageRequestVO.setCompanyCodeList(companyCodeList);
        }

        //查询全部报价单总金额
        FbQuotationPO fbQuotationPO = new FbQuotationPO();
        QueryWrapper<FbQuotationPO> wrapper = new QueryWrapper<>();
        wrapper.select("sum(total_price) as totalPriceAll")
                .eq("delete_status", QuotationDeleteStatusConstant.NORMAL);

        if (CollectionUtils.isNotEmpty(fbQuotationPageRequestVO.getCompanyCodeList())) {
            wrapper.in("company_code", fbQuotationPageRequestVO.getCompanyCodeList());
        }

        FbQuotationPO totalPriceAll = fbQuotationDAO.selectOne(wrapper);
        if (totalPriceAll == null) {
            fbQuotationPO.setTotalPriceAll(BigDecimal.valueOf(BaseConstant.Zero));
        } else {
            fbQuotationPO.setTotalPriceAll(totalPriceAll.getTotalPriceAll());
        }
        logger.info("get totaprice result:{}", fbQuotationPO.getTotalPriceAll());


        //查询部分报价单总金额
        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getUnitName()) && fbQuotationPageRequestVO.getUnitName().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setUnitName(CommonConstant.PERCENT + fbQuotationPageRequestVO.getUnitName() + CommonConstant.PERCENT);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getCompanyName()) && fbQuotationPageRequestVO.getCompanyName().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setCompanyName(CommonConstant.PERCENT + fbQuotationPageRequestVO.getCompanyName() + CommonConstant.PERCENT);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getQuotationNumber()) && fbQuotationPageRequestVO.getQuotationNumber().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setQuotationNumber(fbQuotationPageRequestVO.getQuotationNumber() + CommonConstant.PERCENT);
        }

        FbQuotationPO totalPricePart = fbQuotationDAO.selectTotalPricePart(fbQuotationPageRequestVO);
        if (totalPricePart == null) {
            fbQuotationPO.setTotalPricePart(BigDecimal.valueOf(BaseConstant.Zero));
        } else {
            fbQuotationPO.setTotalPricePart(totalPricePart.getTotalPricePart());
        }
        fbQuotationTotalPriceCountResponseVO.setTotalPriceAll(fbQuotationPO.getTotalPriceAll());
        fbQuotationTotalPriceCountResponseVO.setTotalPricePart(fbQuotationPO.getTotalPricePart());
        return BusinessResponse.ok(fbQuotationTotalPriceCountResponseVO);
    }

    /**
     * Fb报价单删除
     *
     * @param fbQuotationAndOrderDeleteRequestVO
     * @return
     */
    @Override
    public BusinessResponse<Object> deleteFbQuotation(FbQuotationAndOrderDeleteRequestVO fbQuotationAndOrderDeleteRequestVO) {

        //批量删除
        fbQuotationDAO.update(null, new LambdaUpdateWrapper<FbQuotationPO>()
                .in(FbQuotationPO::getId, fbQuotationAndOrderDeleteRequestVO.getIdList())
                .set(FbQuotationPO::getDeleteStatus, QuotationDeleteStatusConstant.DELETED));
        return BusinessResponse.ok(fbQuotationAndOrderDeleteRequestVO.getIdList());
    }

    /**
     * 报价单导出数据
     *
     * @param fbQuotationPageRequestVO
     * @return
     */
    @Override
    public BusinessResponse<List<FbQuotationaExportResponseVO>> getFbQuotationData(FbQuotationPageRequestVO fbQuotationPageRequestVO) {
        //数据权限处理

        fbQuotationPageRequestVO.parseDate();

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getEmpNo())) {
            //获取当前员工下的企业编码
            CompanyInfoListByJobNumberRequestVO companyInfoListByJobNumberRequestVO = new CompanyInfoListByJobNumberRequestVO();

            companyInfoListByJobNumberRequestVO.setJobNumber(fbQuotationPageRequestVO.getEmpNo());

            companyInfoListByJobNumberRequestVO.setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE);

            List<String> companyCodeList = UserApiUtil.getCompanyCodeListByJobNumberMethod(companyInfoListByJobNumberRequestVO);

            if (CollUtil.isEmpty(companyCodeList)) {
                return BusinessResponse.ok(null);
            }

            fbQuotationPageRequestVO.setCompanyCodeList(companyCodeList);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getUnitName()) && fbQuotationPageRequestVO.getUnitName().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setUnitName(CommonConstant.PERCENT + fbQuotationPageRequestVO.getUnitName() + CommonConstant.PERCENT);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getCompanyName()) && fbQuotationPageRequestVO.getCompanyName().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setCompanyName(CommonConstant.PERCENT + fbQuotationPageRequestVO.getCompanyName() + CommonConstant.PERCENT);
        }

        if (StringUtils.isNotBlank(fbQuotationPageRequestVO.getQuotationNumber()) && fbQuotationPageRequestVO.getQuotationNumber().length() >= CommonConstant.TWO) {
            fbQuotationPageRequestVO.setQuotationNumber(fbQuotationPageRequestVO.getQuotationNumber() + CommonConstant.PERCENT);
        }

        if (fbQuotationPageRequestVO.getPageNum() != 0) {
            PageMethod.startPage(fbQuotationPageRequestVO.getPageNum(), fbQuotationPageRequestVO.getPageSize());
        }

        //获取报价单数据
        List<FbQuotationListResponseVO> fbOrderListResponseVOList = fbQuotationDAO.selectFbQuotationPage(fbQuotationPageRequestVO);

        if (CollUtil.isEmpty(fbOrderListResponseVOList)) {
            return BusinessResponse.ok(null);
        }

        //获取报价单id
        List<String> ids = fbOrderListResponseVOList.stream().map(FbQuotationListResponseVO::getId).collect(Collectors.toList());

        //根据id列表查询报价单明细
        Map<String, List<FbQuotationListPO>> fbQuotationListMap = new HashMap<>();
        for (String id : ids) {
            LambdaQueryWrapper<FbQuotationListPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FbQuotationListPO::getQuotationId, id).orderByAsc(FbQuotationListPO::getSort);
            List<FbQuotationListPO> fbQuotationListPOS = fbQuotationListDAO.selectList(queryWrapper);
            fbQuotationListMap.put(id, fbQuotationListPOS);
        }

        //报价单列表的数据的返回
        List<FbQuotationListResponseVO> fbQuotationListResponseVOList = fbOrderListResponseVOList.stream().map(fbQuotationPO -> {
            FbQuotationListResponseVO fbQuotationListResponseVO = new FbQuotationListResponseVO();
            BeanUtils.copyProperties(fbQuotationPO, fbQuotationListResponseVO);

            //获取报价单明细列表
            List<FbQuotationListPO> fbQuotationListPOS = fbQuotationListMap.get(fbQuotationPO.getId());

            //报价单明细的列表数据的返回
            List<FbQuotationDetailChildResponseVO> fbQuotationDetailChildResponseVOList;
            if (CollectionUtils.isNotEmpty(fbQuotationListPOS)) {
                fbQuotationDetailChildResponseVOList = fbQuotationListPOS.stream().map(m -> {

                    FbQuotationDetailChildResponseVO fbQuotationDetailChildResponseVO = new FbQuotationDetailChildResponseVO();
                    BeanUtils.copyProperties(m, fbQuotationDetailChildResponseVO);

                    if (StringUtils.isNotBlank(m.getTotalDiscountRate())) {
                        fbQuotationDetailChildResponseVO.setTotalDiscountRate(new BigDecimal(m.getTotalDiscountRate()).multiply(new BigDecimal(100)).toString() + CommonConstant.PERCENT);
                    }

                    return fbQuotationDetailChildResponseVO;
                }).collect(Collectors.toList());
            } else {
                fbQuotationDetailChildResponseVOList = Collections.emptyList();
            }
            fbQuotationListResponseVO.setFbQuotationDetailChildResponseVOList(fbQuotationDetailChildResponseVOList);

            return fbQuotationListResponseVO;

        }).collect(Collectors.toList());


        try {
            List<String> userCodeList = fbOrderListResponseVOList.stream().map(FbQuotationListResponseVO::getUserCode).collect(Collectors.toList());
            List<String> purchaseUserCodeList = fbOrderListResponseVOList.stream().map(FbQuotationListResponseVO::getPurchaseUserCode).collect(Collectors.toList());

            userCodeList.addAll(purchaseUserCodeList);
            HashSet<String> hashSet = new HashSet<>(userCodeList);
            BaseUserInfoRequestVO baseUserInfoRequestVO = new BaseUserInfoRequestVO();
            baseUserInfoRequestVO.setUserCodeList(hashSet);
            List<UserInfo> userInfoByUserCodesOrUserAccounts = UserApiUtil.getUserInfoByUserCodesOrUserAccounts(baseUserInfoRequestVO);
            Map<String, UserInfo> userInfoMap = userInfoByUserCodesOrUserAccounts.stream().collect(Collectors.toMap(UserInfo::getUserCode, v -> v, (k1, k2) -> k1));
            fbQuotationListResponseVOList.forEach(e -> {
                String userCode = e.getUserCode();
                String purchaseUserCode = e.getPurchaseUserCode();
                UserInfo userInfo = userInfoMap.get(userCode);
                if (Objects.nonNull(userInfo)) {
                    e.setUserCode(StringUtils.join(StringUtils.EMPTY, userInfo.getUserName(), CommonConstant.LEFT_BRACKET, userInfo.getUserCode(), CommonConstant.RIGHT_BRACKET));
                }

                UserInfo purchaseUserInfo = userInfoMap.get(purchaseUserCode);
                if (Objects.nonNull(purchaseUserInfo)) {
                    e.setPurchaseUserCode(StringUtils.join(StringUtils.EMPTY, purchaseUserInfo.getUserName(), CommonConstant.LEFT_BRACKET, purchaseUserInfo.getUserCode(), CommonConstant.RIGHT_BRACKET));
                }
            });
        } catch (Exception e) {
            logger.error("get user name fail {} ", e.getMessage());
        }


        List<FbQuotationaExportResponseVO> fbQuotationaExportResponseVOS = fbQuotationListResponseVOList.stream()
                .flatMap(m -> {
                    FbQuotationaExportResponseVO fbQuotationaExportResponseVO = new FbQuotationaExportResponseVO();
                    BeanUtils.copyProperties(m, fbQuotationaExportResponseVO);

                    List<FbQuotationDetailChildResponseVO> fbQuotationDetailChildResponseVOList = m.getFbQuotationDetailChildResponseVOList();

                    if (fbQuotationDetailChildResponseVOList != null && !fbQuotationDetailChildResponseVOList.isEmpty()) {
                        return fbQuotationDetailChildResponseVOList.stream().map(p -> {
                            FbQuotationaExportResponseVO childFbQuotationaExportResponseVO = new FbQuotationaExportResponseVO();
                            BeanUtils.copyProperties(fbQuotationaExportResponseVO, childFbQuotationaExportResponseVO); // 复制父项属性
                            BeanUtils.copyProperties(p, childFbQuotationaExportResponseVO); // 复制子项属性
                            return childFbQuotationaExportResponseVO;
                        });
                    } else {
                        return Stream.of(fbQuotationaExportResponseVO); // 如果没有子项，返回父项本身
                    }
                })
                .collect(Collectors.toList());

        //添加序号
        int sort = 0;
        for (int i = 0; i < fbQuotationaExportResponseVOS.size(); i++) {
            sort++;
            fbQuotationaExportResponseVOS.get(i).setSort(sort);
        }
        return BusinessResponse.ok(fbQuotationaExportResponseVOS);
    }

}
