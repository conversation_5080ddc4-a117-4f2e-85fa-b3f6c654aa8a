package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndMerchandiserAndSalesmanInfoResponseVO;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.CompanyInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.DateUtil;
import com.yhd.common.util.LogUtils;
import com.yhd.common.util.UUIDUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.OrderStatusConstant;
import com.yhd.fa.marketing.cms.constant.PaymentStatusConstant;
import com.yhd.fa.marketing.cms.constant.PaymentTypeConstant;
import com.yhd.fa.marketing.cms.dao.OrderCancelDAO;
import com.yhd.fa.marketing.cms.dao.OrderDAO;
import com.yhd.fa.marketing.cms.dao.OrderListDAO;
import com.yhd.fa.marketing.cms.dao.OrderMarketingDAO;
import com.yhd.fa.marketing.cms.enums.*;
import com.yhd.fa.marketing.cms.mapper.OrderCancelListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderMapper;
import com.yhd.fa.marketing.cms.pojo.po.*;
import com.yhd.fa.marketing.cms.pojo.vo.request.HelpUserCancelOrderApplyRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ReturnCouponRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.HelpUserCancelOrderBasicsResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderCancelService;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.FaMarketingSystemService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.BaseUtil;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.RegexUtils;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: HelpUserCancelOrderLogic.java, v 0.1 2023/3/1 16:34 JiangYuHong Exp $
 */
@Component
public class HelpUserCancelOrderLogic {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(HelpUserCancelOrderLogic.class.getName());

    /**
     * 订单DAO
     */
    @Resource
    private OrderDAO orderDAO;

    @Resource
    private OrderMapper orderMapper;

    /**
     * 订单明细DAO
     */
    @Resource
    private OrderListDAO orderListDAO;

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 订单取消DAO
     */
    @Resource
    private OrderCancelDAO orderCancelDAO;

    /**
     * 订单取消明细服务
     */
    @Resource
    private OrderCancelListMapper orderCancelListMapper;

    @Resource
    private OrderMarketingDAO orderMarketingDAO;

    @Resource
    private FaMarketingSystemService faMarketingSystemService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 生成取消单号
     *
     * @return String
     */
    private static String getCancelOrderNo() {
        String prefix = "SQ";
        String time = DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss");
        String randStr = RandomStringUtils.randomAlphanumeric(4).toUpperCase();
        return prefix + time + randStr;
    }

    /**
     * 取消订单初始化接口
     *
     * @param orderId 订单id
     * @return BusinessResponse
     */
    public BusinessResponse<List<HelpUserCancelOrderBasicsResponseVO>> helpUserCancelOrderBasics(String orderId) {
        logger.info("start get help user cancel order basics logic.");

        //获取订单信息
        MPJLambdaWrapper<OrderPO> orderPOMPJLambdaWrapper = new MPJLambdaWrapper<OrderPO>()
                .selectAll(OrderPO.class)
                .eq(OrderPO::getId, orderId);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            orderPOMPJLambdaWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        OrderPO orderPO = orderDAO.selectOne(orderPOMPJLambdaWrapper);
        if (ObjectUtil.isNull(orderPO)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_IS_NOT_EXISTS);
        }
        List<HelpUserCancelOrderBasicsResponseVO> helpUserCancelOrderBasicsResponseVOS = new ArrayList<>();
        orderPO.getOrderDetailList().forEach(orderList -> {
            HelpUserCancelOrderBasicsResponseVO helpUserCancelOrderBasicsResponseVO = new HelpUserCancelOrderBasicsResponseVO();
            BeanUtil.copyProperties(orderList, helpUserCancelOrderBasicsResponseVO);
            //设置状态颜色
            helpUserCancelOrderBasicsResponseVO.setOrderDetailStatusColor(
                    OrderDetailStatusEnum.valueOf(RegexUtils.humpToLowerLine(orderList.getOrderDetailStatus(), true, false)).getOrderDetailStatusColor()
            );
            helpUserCancelOrderBasicsResponseVO.setOrderDetailStatus(
                    OrderDetailStatusEnum.valueOf(RegexUtils.humpToLowerLine(orderList.getOrderDetailStatus(), true, false)).getOrderDetailStatusCn()
            );
            helpUserCancelOrderBasicsResponseVOS.add(helpUserCancelOrderBasicsResponseVO);
        });

        return BusinessResponseCommon.ok(helpUserCancelOrderBasicsResponseVOS);
    }

    /**
     * 申请取消订单
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    public BusinessResponse<Object> helpUserCancelOrderApply(HelpUserCancelOrderApplyRequestVO requestVO) {

        logger.info("start help user cancel order logic.");

        //获取订单信息
        MPJLambdaWrapper<OrderPO> orderPOMPJLambdaWrapper = new MPJLambdaWrapper<OrderPO>().eq(OrderPO::getId, requestVO.getOrderId());
        orderPOMPJLambdaWrapper.selectAll(OrderPO.class);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            orderPOMPJLambdaWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        OrderPO orderPO = orderMapper.getOneDeep(orderPOMPJLambdaWrapper);

        //判断订单主状态是否可以申请取消
        if (StringUtils.equals(orderPO.getOrderStatus(), OrderStatusConstant.FINISH) ||
                StringUtils.equals(orderPO.getOrderStatus(), OrderStatusConstant.CANCEL) ||
                StringUtils.equals(orderPO.getOrderStatus(), OrderStatusConstant.CLOSED)) {

            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.CANCEL_ORDER_ERROR_ORDER);
        }

        //获取需要取消的明细
        List<OrderListPO> cancelOrderList = orderPO.getOrderDetailList().stream().filter(e -> requestVO.getOrderSortId().contains(e.getSortId())).collect(Collectors.toList());

        //检查明细状态是否可以申请
        BusinessResponse<Object> checkStatusBusinessResponse = checkOrderStatus(requestVO, cancelOrderList);
        if (!checkStatusBusinessResponse.success()) {
            return checkStatusBusinessResponse;
        }

        //获取订单原企业信息
        Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> companyAndMerchandiserAndSalesmanInfo = userBucCmsService.getCompanyAndMerchandiserAndSalesmanInfo(Collections.singletonList(orderPO.getCompanyCode()));
        if (ObjectUtil.isNull(companyAndMerchandiserAndSalesmanInfo) || !companyAndMerchandiserAndSalesmanInfo.containsKey(orderPO.getCompanyCode())) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.GET_ORIGINAL_COMPANY_CODE_FAIL);
        }
        CompanyInfo companyInfo = companyAndMerchandiserAndSalesmanInfo.get(orderPO.getCompanyCode()).getCompanyInfo();

        //是否整单取消
        boolean allCancel = cancelOrderList.size() == orderPO.getOrderDetailList().size();
        //计算取消后的订单总金额
        BigDecimal cancelTotal = cancelOrderList.stream().map(orderListPO -> new BigDecimal(String.valueOf(orderListPO.getTotalPrice()))).reduce(BigDecimal.ZERO, BigDecimal::add);
        logger.info("cancel order money is :{}", cancelTotal.doubleValue());
        //取消后的订单总金额
        BigDecimal totalMoney = new BigDecimal(String.valueOf(orderPO.getPayablePrice())).subtract(cancelTotal);
        //设置取消主表信息
        OrderCancelPO orderCancelPO = setOrderCancelPO(requestVO.getReason(), cancelTotal, orderPO, allCancel, companyInfo);
        //设置取消单状态
        setCancelStatus(orderPO, cancelOrderList, orderCancelPO);
        //设置取消明细信息
        List<OrderCancelListPO> orderCancelListPOS = setOrderCancelListPOS(cancelOrderList, orderCancelPO);

        //设置订单取消后的金额
        orderPO.setPayablePrice(totalMoney);

        saveDb(orderPO, cancelOrderList, orderCancelPO, orderCancelListPOS);

        //sync data
        if (StringUtils.equals(orderCancelPO.getCancelStatus(), CancelStatusEnum.CANCELING.getCode())) {
            SpringUtil.getBean(OrderCancelService.class).synchronizationOrderCancel(orderCancelPO.getId());
        }else {
            // 退还优惠券
            SpringUtil.getBean(HelpUserCancelOrderLogic.class).returnCoupon(orderCancelPO, orderPO);
        }

        return BusinessResponseCommon.ok(null);
    }

    /**
     * 设置取消单的状态
     *
     * @param orderPO         订单信息
     * @param cancelOrderList 需要取消的订单明细项
     * @param orderCancelPO   取消主表信息
     */
    private void setCancelStatus(OrderPO orderPO, List<OrderListPO> cancelOrderList, OrderCancelPO orderCancelPO) {
        //结算方式为在线并且未支付的不需要同步到ERP审核,可以直接取消
        if (hasSync(orderPO)) {

            //设置订单明细项状态为已取消
            cancelOrderList.forEach(orderListPO -> {
                orderListPO.setOrderDetailStatus(OrderStatusConstant.CANCEL);
                BaseUtil.setUpdateParams(orderListPO);
            });


            orderCancelPO.setCancelStatus(CancelStatusEnum.PROCESSED.getCode());
            orderCancelPO.setSyncStatus(SynchronizeStatusEnum.SUCCESS.getStatus());
            orderCancelPO.setSyncDate(LocalDateTime.now());
            orderCancelPO.setHandelDate(LocalDateTime.now());
        } else {
            //需要到ERP审核

            //订单明细状态直接修改为取消中
            cancelOrderList.forEach(orderListPO -> {
                orderListPO.setOrderDetailStatus(OrderStatusConstant.CANCELING);
                BaseUtil.setUpdateParams(orderListPO);
            });

            //订单取消主表状态为canceling
            orderCancelPO.setCancelStatus(CancelStatusEnum.CANCELING.getCode());
            //订单取消主表同步状态为wait
            orderCancelPO.setSyncStatus(SynchronizeStatusEnum.WAIT.getStatus());

        }

        //更新最新的明细状态(用于维护订单主表取消状态字段)
        orderPO.getOrderDetailList().forEach(e -> cancelOrderList.forEach(f -> {
            if (Objects.equals(e.getSortId(), f.getSortId())) {
                e.setOrderDetailStatus(f.getOrderDetailStatus());
            }
        }));

        //判断订单主状态
        String status = OrderUtil.judgmentStatus(orderPO.getOrderDetailList());
        orderPO.setOrderStatus(status);
        // 判断订单明细是否全部已取消(这里是给订单主表的取消字段使用)
        orderPO.setCancelStatus(OrderUtil.hasAllCancel(orderPO.getOrderDetailList()) ? CommonConstant.TRUE : CommonConstant.FALSE);

    }

    /**
     * 数据库操作
     *
     * @param orderInfo          订单信息
     * @param cancelOrderList    需要取消的订单明细项
     * @param orderCancelPO      取消主表PO
     * @param orderCancelListPOS 取消明细
     */
    private void saveDb(OrderPO orderInfo, List<OrderListPO> cancelOrderList, OrderCancelPO orderCancelPO, List<OrderCancelListPO> orderCancelListPOS) {
        //更新订单
        LambdaUpdateWrapper<OrderPO> orderPOLambdaUpdateWrapper = new LambdaUpdateWrapper<OrderPO>()
                .set(OrderPO::getOrderStatus, orderInfo.getOrderStatus())
                .set(OrderPO::getPayablePrice, orderInfo.getPayablePrice())
                .set(OrderPO::getCancelStatus, orderInfo.getCancelStatus())
                .eq(OrderPO::getId, orderInfo.getId());
        orderDAO.update(null, orderPOLambdaUpdateWrapper);

        //更新订单明细
        List<String> detailStatus = cancelOrderList.stream().map(OrderListPO::getOrderDetailStatus).distinct().collect(Collectors.toList());
        List<Integer> orderSortId = cancelOrderList.stream().map(OrderListPO::getSortId).distinct().collect(Collectors.toList());
        orderListDAO.update(null, new LambdaUpdateWrapper<OrderListPO>()
                .eq(OrderListPO::getOrderId, orderInfo.getId())
                .in(OrderListPO::getSortId, orderSortId)
                .set(OrderListPO::getOrderDetailStatus, detailStatus.get(0))
        );

        //插入取消订单主表
        orderCancelDAO.insert(orderCancelPO);
        //插入取消明细表
        orderCancelListMapper.saveBatch(orderCancelListPOS);

    }

    /**
     * 检查订单信息以及明细状态是否可以申请取消
     *
     * @param requestVO       请求参数
     * @param cancelOrderList 申请取消的明细列表
     * @return BusinessResponse
     */
    private BusinessResponse<Object> checkOrderStatus(HelpUserCancelOrderApplyRequestVO requestVO, List<OrderListPO> cancelOrderList) {

        if (ObjectUtil.isNull(cancelOrderList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_CANCEL_IS_NOT_EXISTS);
        }
        //创建一个Map存储订单明细状态和对应的错误枚举值
        Map<String, FaDocMarketingResponseEnum> statusMap = new HashMap<>();
        statusMap.put(OrderStatusConstant.CANCEL, FaDocMarketingResponseEnum.CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_CANCEL);
        statusMap.put(OrderStatusConstant.FINISH, FaDocMarketingResponseEnum.CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_FINISH);
        statusMap.put(OrderStatusConstant.STOCK_UP, FaDocMarketingResponseEnum.CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_STOCK_UP);
        statusMap.put(OrderStatusConstant.CANCELING, FaDocMarketingResponseEnum.CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_CANCELLING);
        statusMap.put(OrderStatusConstant.TAKE_DELIVERED, FaDocMarketingResponseEnum.CANCEL_ORDER_ERROR_ORDER_DETAIL_SORT_IS_TAKE_DELIVERED);

        //用一个循环来检查是否存在不允许取消的状态
        for (Map.Entry<String, FaDocMarketingResponseEnum> status : statusMap.entrySet()) {
            List<Integer> list = cancelOrderList.stream().filter(e -> StringUtils.equals(e.getOrderDetailStatus(), status.getKey())).map(OrderListPO::getSortId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                return BusinessResponse.fail(statusMap.get(status.getKey()).getCode(),
                        MessageFormatter.format(statusMap.get(status.getKey()).getDesc(), list).getMessage());
            }
        }

        //是否存在非法明细项
        if (cancelOrderList.size() != requestVO.getOrderSortId().size()) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.CANCEL_ORDER_ERROR_ORDER_SORT);
        }
        return BusinessResponseCommon.ok(null);
    }

    /**
     * 设置取消主表信息
     *
     * @param reason      取消理由
     * @param totalMoney  取消总金额
     * @param orderPO     订单信息
     * @param allCancel   是否整单取消
     * @param companyInfo 订单原企业信息
     * @return OrderCancelPO
     */
    private OrderCancelPO setOrderCancelPO(String reason, BigDecimal totalMoney, OrderPO orderPO, boolean allCancel, CompanyInfo companyInfo) {
        OrderCancelPO orderCancelPO = new OrderCancelPO();
        orderCancelPO.setId(UUIDUtils.getStringUUID());
        orderCancelPO.setOrderId(orderPO.getId());
        orderCancelPO.setCancelNumber(getCancelOrderNo());
        orderCancelPO.setOrderNumber(orderPO.getOrderNumber());
        orderCancelPO.setCompanyCode(orderPO.getCompanyCode());
        orderCancelPO.setCompanyName(orderPO.getCompanyName());
        orderCancelPO.setErpCode(companyInfo.getErpCompanyCode());
        orderCancelPO.setUserCode(orderPO.getUserCode());
        orderCancelPO.setTotalMoney(totalMoney);
        orderCancelPO.setCancelType(allCancel ? CancelTypeEnum.ALL.getCode() : CancelTypeEnum.PART.getCode());
        orderCancelPO.setTerritory(orderPO.getTerritory());
        orderCancelPO.setReason(reason);
        //设置内部员工号
        orderCancelPO.setIsInsideCreated(CommonConstant.TRUE);
        orderCancelPO.setInsideEmployeeCode(SecurityUtil.getAccountNo());
        BaseUtil.setCreatedParams(orderCancelPO);
        return orderCancelPO;
    }

    /**
     * 设置取消明细信息
     *
     * @param cancelOrderList 需要取消的订单明细项
     * @param orderCancelPO   取消主表信息
     * @return List<OrderCancelListPO>
     */
    private List<OrderCancelListPO> setOrderCancelListPOS(List<OrderListPO> cancelOrderList, OrderCancelPO orderCancelPO) {
        return cancelOrderList.stream().map(orderListPO -> {
            OrderCancelListPO orderCancelListPO = new OrderCancelListPO();
            BaseUtil.setCreatedParams(orderCancelListPO);

            orderCancelListPO.setCancelId(orderCancelPO.getId());
            String cancelListStatus = Objects.equals(orderCancelPO.getCancelStatus(), CancelListStatusEnum.CANCELING.getCode()) ?
                    CancelListStatusEnum.CANCELING.getCode() : CancelListStatusEnum.CANCEL.getCode();
            orderCancelListPO.setCancelDetailStatus(cancelListStatus);
            orderCancelListPO.setProductCode(orderListPO.getProductCode());
            orderCancelListPO.setProductModel(orderListPO.getProductModel());
            orderCancelListPO.setProductName(orderListPO.getProductName());
            orderCancelListPO.setQuantity(orderListPO.getQuantity());
            orderCancelListPO.setSortId(orderListPO.getSortId());
            orderCancelListPO.setOrderListId(orderListPO.getId());
            orderCancelListPO.setPrice(orderListPO.getPrice());
            orderCancelListPO.setDiscountPrice(orderListPO.getDiscountPrice());
            orderCancelListPO.setTaxDiscountPrice(orderListPO.getTaxDiscountPrice());
            orderCancelListPO.setTotalPrice(orderListPO.getTotalPrice());
            orderCancelListPO.setDelivery(orderListPO.getDelivery());

            return orderCancelListPO;
        }).collect(Collectors.toList());
    }

    /**
     * 判断是否需要同步取消单
     *
     * @param orderPO 订单信息
     * @return boolean
     */
    private boolean hasSync(OrderPO orderPO) {
        List<String> onlinePay = Arrays.asList(
                PaymentTypeConstant.ALI_PAY,
                PaymentTypeConstant.WECHAT_PAY,
                PaymentTypeConstant.UNION_PAY
        );
        //如果是未支付的,不需要同步
        if (StringUtils.equals(orderPO.getPaymentType(), PaymentTypeConstant.UNKNOWN)) {
            return true;
        } else
            return onlinePay.contains(orderPO.getPaymentType()) && StringUtils.equals(orderPO.getPaymentStatus(), PaymentStatusConstant.UNPAID);
    }

    /**
     * 退还优惠券
     * @param orderCancelPO 取消订单信息
     */
    @Async
    public void returnCoupon(OrderCancelPO orderCancelPO, OrderPO orderPO) {

        logger.info("start return coupon logic.");

        // 判断是否整单取消
        if (OrderUtil.hasAllCancel(orderPO.getOrderDetailList())) {

            // 查询是否使用了优惠券
            OrderMarketingPO orderMarketingPO = orderMarketingDAO.selectOne(new LambdaQueryWrapper<OrderMarketingPO>().eq(OrderMarketingPO::getOrderNumber, orderCancelPO.getOrderNumber()));

            // 如果使用了优惠券则请求接口，退还优惠券
            Optional.ofNullable(orderMarketingPO).ifPresent(orderMarketing -> {
                ReturnCouponRequestVO requestVO = ReturnCouponRequestVO.builder()
                        .userCode(orderCancelPO.getUserCode())
                        .userCouponId(orderMarketing.getUserCouponId())
                        .orderNumber(orderCancelPO.getOrderNumber())
                        .build();
                BusinessResponse<Object> businessResponse = faMarketingSystemService.returnCoupon(requestVO);

                if (!businessResponse.success()) {
                    logger.error("request return coupon api error. errorMsg:{}", businessResponse.getRt_msg());
                }
            });
        }

    }

}
