package com.yhd.fa.marketing.cms.constant;

/**
 * <AUTHOR>
 * @version Id: QuotedPriceStatusConstant.java, v0.1 2022/9/23 9:33 yehuasheng Exp $
 */
public class QuotedPriceStatusConstant {
    /**
     * 已确认
     */
    public static final String CONFIRMED = "confirmed";
    /**
     * 未处理
     */
    public static final String UNTREATED = "untreated";
    /**
     * 计算错误
     */
    public static final String CALCULATION_ERROR = "calculationError";
    /**
     * 已计算
     */
    public static final String CALCULATED = "calculated";
    /**
     * 不计算
     */
    public static final String NOT_CALCULATED = "notCalculated";
    /**
     * 型号错误
     */
    public static final String ERROR_MODEL = "errorModel";
    /**
     * 数量超出
     */
    public static final String QUANTITY_EXCESS = "quantityExcess";
    /**
     * 无价格
     */
    public static final String NO_PRICE = "noPrice";
    /**
     * 无交期
     */
    public static final String NO_DELIVERY = "noDelivery";

    private QuotedPriceStatusConstant() {
    }
}
