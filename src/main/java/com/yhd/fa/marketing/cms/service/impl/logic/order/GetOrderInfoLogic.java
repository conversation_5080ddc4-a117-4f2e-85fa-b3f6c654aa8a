package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.OrderStatusConstant;
import com.yhd.fa.marketing.cms.constant.PaymentStatusConstant;
import com.yhd.fa.marketing.cms.constant.SettlementTypeConstant;
import com.yhd.fa.marketing.cms.dao.OrderClickHouseDAO;
import com.yhd.fa.marketing.cms.dao.OrderDAO;
import com.yhd.fa.marketing.cms.enums.*;
import com.yhd.fa.marketing.cms.mapper.OrderErpLogisticsListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderErpLogisticsMapper;
import com.yhd.fa.marketing.cms.mapper.OrderListMapper;
import com.yhd.fa.marketing.cms.pojo.po.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.*;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.ProductCenterCmsService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderInfoLogic.java, v0.1 2023/2/20 11:47 yehuasheng Exp $
 */
@Component
public class GetOrderInfoLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderInfoLogic.class.getName());

    /**
     * 订单明细mapper
     */
    @Resource
    private OrderListMapper orderListMapper;

    /**
     * 订单物流mapper
     */
    @Resource
    private OrderErpLogisticsMapper orderErpLogisticsMapper;

    /**
     * 订单物流明细mapper
     */
    @Resource
    private OrderErpLogisticsListMapper orderErpLogisticsListMapper;

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 产品中心服务
     */
    @Resource
    private ProductCenterCmsService productCenterCmsService;

    /**
     * 订单DAO
     */
    @Resource
    private OrderDAO orderDAO;

    @Resource
    private OrderClickHouseDAO orderClickHouseDAO;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 获取订单详情信息
     *
     * @param orderId 订单id
     * @return BusinessResponse<OrderInfoResponseVO>
     */
    public BusinessResponse<OrderInfoResponseVO> exec(String orderId) {
        logger.info("start get order info logic.");

        // 获取订单数据
        OrderInfoResponseVO orderInfo = getOrderInfo(orderId);

        // 判断订单是否存在
        if (ObjectUtil.isNull(orderInfo)) {
            orderInfo = getOrderInfoToClickHouse(orderId);
            if (ObjectUtil.isNull(orderInfo)) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_IS_NOT_EXISTS);
            }
        }

        // 设置订单其他值
        setOrderInfoOtherValue(orderInfo);

        // 设置订单明细
        setOrderDetails(orderInfo);

        // 设置订单快递信息
        setOrderLogistics(orderInfo);

        return BusinessResponse.ok(orderInfo);
    }

    /**
     * 查询订单详情数据
     *
     * @param orderId 订单id
     * @return OrderInfoResponseVO
     */
    private OrderInfoResponseVO getOrderInfo(String orderId) {
        logger.info("get order info for order id:{}", orderId);

        // 查询数据
        return orderDAO.selectJoinOne(OrderInfoResponseVO.class, setGetOrderInfoQueryWrapper(orderId));
    }

    /**
     * 设置查询条件
     *
     * @param orderId 订单id
     * @return MPJLambdaWrapper<OrderPO>
     */
    private MPJLambdaWrapper<OrderPO> setGetOrderInfoQueryWrapper(String orderId) {
        logger.info("set get order info query wrapper.");

        // 设置查询条件
        MPJLambdaWrapper<OrderPO> queryWrapper = OrderUtil.setQueryWrapperSelectAs()
                .selectAs(OrderPO::getShipping, OrderInfoResponseVO::getShipping)
                .selectAs(OrderPO::getFirstShippingDay, OrderInfoResponseVO::getFirstShippingDay)
                .selectAs(OrderPO::getCustomerNumber, OrderInfoResponseVO::getCustomerNumber)
                .selectAs(OrderPO::getPayDate, OrderInfoResponseVO::getPayDate)
                .selectAs(OrderPO::getRemark, OrderInfoResponseVO::getRemark)
                .selectAs(OrderPO::getConsignee, OrderInfoResponseVO::getConsignee)
                .selectAs(OrderPO::getContactNumber, OrderInfoResponseVO::getContactNumber)
                .selectAs(OrderPO::getProvince, OrderInfoResponseVO::getProvince)
                .selectAs(OrderPO::getCity, OrderInfoResponseVO::getCity)
                .selectAs(OrderPO::getTown, OrderInfoResponseVO::getTown)
                .selectAs(OrderPO::getAddress, OrderInfoResponseVO::getAddress)
                .selectAs(OrderPO::getPaymentStatus, OrderInfoResponseVO::getPaymentStatus)
                .selectAs(OrderPO::getSettlementType, OrderInfoResponseVO::getSettlementType)
                .selectAs(OrderPO::getOrderStatus, OrderInfoResponseVO::getOrderStatus)
                .selectAs(OrderPO::getDiscountPrice, OrderInfoResponseVO::getDiscountPrice)
                .eq(OrderPO::getId, orderId)
                .last(FaDocMarketingCmsConstant.LIMIT);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        return queryWrapper;
    }

    /**
     * 查询订单详情数据
     *
     * @param orderId 订单id
     * @return OrderInfoResponseVO
     */
    private OrderInfoResponseVO getOrderInfoToClickHouse(String orderId) {
        logger.info("get click house order info for order id:{}", orderId);

        // 查询数据
        return orderClickHouseDAO.selectJoinOne(OrderInfoResponseVO.class, setGetOrderInfoClickHouseQueryWrapper(orderId));
    }

    /**
     * 设置查询条件
     *
     * @param orderId 订单id
     * @return MPJLambdaWrapper<OrderPO>
     */
    private MPJLambdaWrapper<OrderClickHousePO> setGetOrderInfoClickHouseQueryWrapper(String orderId) {
        logger.info("set get click house order info query wrapper.");

        // 设置查询条件
        MPJLambdaWrapper<OrderClickHousePO> queryWrapper = OrderUtil.setClickHouseQueryWrapperSelectAs()
                .selectAs(OrderClickHousePO::getShipping, OrderInfoResponseVO::getShipping)
                .selectAs(OrderClickHousePO::getFirstShippingDay, OrderInfoResponseVO::getFirstShippingDay)
                .selectAs(OrderClickHousePO::getCustomerNumber, OrderInfoResponseVO::getCustomerNumber)
                .selectAs(OrderClickHousePO::getPayDate, OrderInfoResponseVO::getPayDate)
                .selectAs(OrderClickHousePO::getRemark, OrderInfoResponseVO::getRemark)
                .selectAs(OrderClickHousePO::getConsignee, OrderInfoResponseVO::getConsignee)
                .selectAs(OrderClickHousePO::getContactNumber, OrderInfoResponseVO::getContactNumber)
                .selectAs(OrderClickHousePO::getProvince, OrderInfoResponseVO::getProvince)
                .selectAs(OrderClickHousePO::getCity, OrderInfoResponseVO::getCity)
                .selectAs(OrderClickHousePO::getTown, OrderInfoResponseVO::getTown)
                .selectAs(OrderClickHousePO::getAddress, OrderInfoResponseVO::getAddress)
                .selectAs(OrderClickHousePO::getPaymentStatus, OrderInfoResponseVO::getPaymentStatus)
                .selectAs(OrderClickHousePO::getSettlementType, OrderInfoResponseVO::getSettlementType)
                .selectAs(OrderClickHousePO::getOrderStatus, OrderInfoResponseVO::getOrderStatus)
                .selectAs(OrderClickHousePO::getDiscountPrice, OrderInfoResponseVO::getDiscountPrice)
                .eq(OrderClickHousePO::getId, orderId)
                .last(FaDocMarketingCmsConstant.LIMIT);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderClickHousePO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        return queryWrapper;
    }

    /**
     * 设置订单详情其他值
     *
     * @param orderInfo 订单详情
     */
    private void setOrderInfoOtherValue(OrderInfoResponseVO orderInfo) {
        logger.info("set order info other value.");

        // 获取用户信息
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(orderInfo.getUserCode(), orderInfo.getCompanyCode());
//        orderInfo.setUserName(userInfo.getUserInfo().getUserName());
        Optional.ofNullable(userInfo.getUserInfo()).ifPresent(u -> orderInfo.setUserName(u.getUserName()));

        // 设置跟单
        Optional.ofNullable(userInfo.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserInfo -> {
            orderInfo.setMerchandiser(merchandiserInfo.getEmployeeCode() + CommonConstant.SLASH + merchandiserInfo.getEmployeeName());
            orderInfo.setMerchandiserContact(merchandiserInfo.getMobile());
        });

        // 设置业务员
        Optional.ofNullable(userInfo.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> {
            orderInfo.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
            orderInfo.setSalesmanContact(salesMan.getMobile());
        });

        // 设置订单状态名称
        Map<String, String> orderStatusName = Arrays.stream(OrderStatusEnum.values()).collect(Collectors.toMap(OrderStatusEnum::getOrderStatus, OrderStatusEnum::getOrderStatusCn));
        orderInfo.setOrderStatusName(StringUtils.equals(orderInfo.getPaymentStatus(), PaymentStatusConstant.PAYMENT_FAILED) && StringUtils.equals(orderInfo.getSettlementType(), SettlementTypeConstant.ONLINE) && StringUtils.equals(orderInfo.getOrderStatus(), OrderStatusConstant.UNPAID) ? "支付异常" : orderStatusName.get(orderInfo.getOrderStatus()));

        // 设置订单支付状态名称
        Map<String, String> paymentTypeStatusName = Arrays.stream(PaymentTypeStatusEnum.values()).collect(Collectors.toMap(PaymentTypeStatusEnum::getPaymentTypeStatus, PaymentTypeStatusEnum::getPaymentTypeStatusCn));
        orderInfo.setPaymentTypeName(StringUtils.equals(orderInfo.getPaymentStatus(), PaymentStatusConstant.PAYMENT_FAILED) && StringUtils.equals(orderInfo.getSettlementType(), SettlementTypeConstant.ONLINE) && StringUtils.equals(orderInfo.getOrderStatus(), OrderStatusConstant.UNPAID) ? "支付异常" : paymentTypeStatusName.get(orderInfo.getPaymentType()));

        // 设置订单来源名称
        Map<String, String> orderSourceName = Arrays.stream(OrderSourceEnum.values()).collect(Collectors.toMap(OrderSourceEnum::getOrderSource, OrderSourceEnum::getOrderSourceCn));
        orderInfo.setOrderSourceName(orderSourceName.get(orderInfo.getOrderSource()));

        // 设置订单渠道名称
        Map<String, String> orderChannelName = Arrays.stream(OrderChannelEnum.values()).collect(Collectors.toMap(OrderChannelEnum::getOrderChannel, OrderChannelEnum::getOrderChannelCn));
        orderInfo.setOrderChannelName(orderChannelName.get(orderInfo.getOrderChannel()));

        //拼接完整地址
        String address = StringUtils.join(new String[]{
                orderInfo.getProvince(),
                orderInfo.getCity(),
                orderInfo.getTown(),
                orderInfo.getAddress()
        }, CommonConstant.EMPTY);
        orderInfo.setAddress(address);
    }

    /**
     * 设置订单明细
     *
     * @param orderInfo 订单详情
     */
    private void setOrderDetails(OrderInfoResponseVO orderInfo) {
        logger.info("get order detail list.");

        // 获取明细数据
        List<OrderDetailsResponseVO> orderDetails = orderListMapper.selectJoinList(OrderDetailsResponseVO.class,
                new MPJLambdaWrapper<OrderListPO>()
                        .selectAs(OrderListPO::getId, OrderDetailsResponseVO::getOrderDetailId)
                        .selectAs(OrderListPO::getSortId, OrderDetailsResponseVO::getOrderSortId)
                        .selectAs(OrderListPO::getQuotationNumber, OrderDetailsResponseVO::getQuotationNumber)
                        .selectAs(OrderListPO::getQuotationSortId, OrderDetailsResponseVO::getQuotationSortId)
                        .selectAs(OrderListPO::getProductModel, OrderDetailsResponseVO::getProductModel)
                        .selectAs(OrderListPO::getProductCode, OrderDetailsResponseVO::getProductCode)
                        .selectAs(OrderListPO::getProductName, OrderDetailsResponseVO::getProductName)
                        .selectAs(OrderListPO::getCustomerModel, OrderDetailsResponseVO::getCustomerModel)
                        .selectAs(OrderListPO::getCustomerMaterialCode, OrderDetailsResponseVO::getCustomerMaterialCode)
                        .selectAs(OrderListPO::getCustomerProductName, OrderDetailsResponseVO::getCustomerProductName)
                        .selectAs(OrderListPO::getQuantity, OrderDetailsResponseVO::getQuantity)
                        .selectAs(OrderListPO::getIsStandard, OrderDetailsResponseVO::getIsStandard)
                        .selectAs(OrderListPO::getPrice, OrderDetailsResponseVO::getPrice)
                        .selectAs(OrderListPO::getDiscountPrice, OrderDetailsResponseVO::getDiscountPrice)
                        .selectAs(OrderListPO::getTaxDiscountPrice, OrderDetailsResponseVO::getTaxDiscountPrice)
                        .selectAs(OrderListPO::getTotalPrice, OrderDetailsResponseVO::getTotalPrice)
                        .selectAs(OrderListPO::getPayablePrice, OrderDetailsResponseVO::getPayablePrice)
                        .selectAs(OrderListPO::getQuantityDiscount, OrderDetailsResponseVO::getQuantityDiscount)
                        .selectAs(OrderListPO::getTotalDiscount, OrderDetailsResponseVO::getTotalDiscount)
                        .selectAs(OrderListPO::getDelivery, OrderDetailsResponseVO::getDelivery)
                        .selectAs(OrderListPO::getShipDate, OrderDetailsResponseVO::getShipDate)
                        .selectAs(OrderListPO::getUnit, OrderDetailsResponseVO::getUnit)
                        .selectAs(OrderListPO::getEstimatedShippingDate, OrderDetailsResponseVO::getEstimatedShippingDate)
                        .selectAs(OrderListPO::getReplyToDelivery, OrderDetailsResponseVO::getReplyToDelivery)
                        .selectAs(OrderListPO::getFileUrl, OrderDetailsResponseVO::getFileUrl)
                        .selectAs(OrderListPO::getOrderDetailStatus, OrderDetailsResponseVO::getOrderDetailStatus)
                        .selectAs(OrderListPO::getExamineRemark, OrderDetailsResponseVO::getExamineRemark)
                        .selectAs(OrderListPO::getRemark, OrderDetailsResponseVO::getRemark)
                        .selectAs(OrderListPO::getDiscountType, OrderDetailsResponseVO::getDiscountType)
                        .selectAs(OrderListPO::getOriginalDiscountPrice, OrderDetailsResponseVO::getOriginalDiscountPrice)
                        .selectAs(OrderListPO::getOriginalTaxDiscountPrice, OrderDetailsResponseVO::getOriginalTaxDiscountPrice)
                        .selectAs(OrderListPO::getOriginalTotalPrice, OrderDetailsResponseVO::getOriginalTotalPrice)
                        .selectAs(OrderListPO::getDetailDiscountPrice, OrderDetailsResponseVO::getDetailDiscountPrice)
                        .eq(OrderListPO::getOrderId, orderInfo.getOrderId())
        );

        if (CollUtil.isNotEmpty(orderDetails)) {
            // 设置订单明细状态名称
            Map<String, String> orderDetailStatusMap = Arrays.stream(OrderDetailStatusEnum.values()).collect(Collectors.toMap(OrderDetailStatusEnum::getOrderDetailStatus, OrderDetailStatusEnum::getOrderDetailStatusCn));

            // 设置分类
            List<String> productCode = orderDetails.stream().map(OrderDetailsResponseVO::getProductCode).distinct().collect(Collectors.toList());
            Map<String, ProductInfoResponseVO> productInfo = productCenterCmsService.getProductInfo(productCode);

            // 设置订单明细其他参数
            orderDetails.forEach(orderDetailsResponseVO -> {
                // 设置状态名称
                orderDetailsResponseVO.setOrderDetailStatusName(orderDetailStatusMap.get(orderDetailsResponseVO.getOrderDetailStatus()));
                // 设置分类
                if (productInfo.containsKey(orderDetailsResponseVO.getProductCode())) {
                    ProductInfoResponseVO productInfoResponseVO = productInfo.get(orderDetailsResponseVO.getProductCode());
                    orderDetailsResponseVO.setTypeCode(productInfoResponseVO.getTypeCode());
                    orderDetailsResponseVO.setCatCode(productInfoResponseVO.getCatCode());
                    orderDetailsResponseVO.setGoodsCode(productInfoResponseVO.getGoodsCode());
                }
            });
        }

        orderInfo.setOrderDetails(orderDetails);
        // 设置是否可以取消
        orderInfo.setCancelStatus(
                orderDetails.stream().allMatch(
                        e -> GetOrderListLogic.canNotCancelStatusList.contains(e.getOrderDetailStatus()))
                        ? CommonConstant.TRUE : CommonConstant.FALSE
        );
    }

    /**
     * 查询订单物流并设置详情中
     *
     * @param orderInfo 订单详情
     */
    private void setOrderLogistics(OrderInfoResponseVO orderInfo) {
        logger.info("set order logistics.");

        // 查询是否有物流包裹
        List<OrderLogisticsInformationResponseVO> orderErpLogisticsList = orderErpLogisticsMapper.selectJoinList(OrderLogisticsInformationResponseVO.class,
                new MPJLambdaWrapper<OrderErpLogisticsPO>()
                        .selectAs(OrderErpLogisticsPO::getId, OrderLogisticsInformationResponseVO::getOrderLogisticsId)
                        .selectAs(OrderErpLogisticsPO::getExpressNumber, OrderLogisticsInformationResponseVO::getExpressNumber)
                        .selectAs(OrderErpLogisticsPO::getLogisticsCompany, OrderLogisticsInformationResponseVO::getLogisticsCompany)
                        .selectAs(OrderErpLogisticsPO::getDeliveryNumber, OrderLogisticsInformationResponseVO::getDeliveryNumber)
                        .selectAs(OrderErpLogisticsPO::getSigningStatus, OrderLogisticsInformationResponseVO::getSigningStatus)
                        .selectAs(OrderErpLogisticsPO::getDeliveryTime, OrderLogisticsInformationResponseVO::getDeliveryTime)
                        .selectAs(OrderErpLogisticsPO::getOrigin, OrderLogisticsInformationResponseVO::getOrigin)
                        .eq(OrderErpLogisticsPO::getOrderNumber, orderInfo.getOrderNumber())
        );

        // 查询物流包裹明细
        if (CollUtil.isNotEmpty(orderErpLogisticsList)) {
            // 提取物流id
            List<String> orderLogisticsId = orderErpLogisticsList.stream().map(OrderLogisticsInformationResponseVO::getOrderLogisticsId).collect(Collectors.toList());
            // 查询物流明细
            List<OrderLogisticsDetailsResponseVO> orderLogisticsDetails = orderErpLogisticsListMapper.selectJoinList(OrderLogisticsDetailsResponseVO.class,
                    new MPJLambdaWrapper<OrderErpLogisticsListPO>()
                            .selectAs(OrderErpLogisticsListPO::getId, OrderLogisticsDetailsResponseVO::getOrderLogisticsDetailId)
                            .selectAs(OrderErpLogisticsListPO::getParentId, OrderLogisticsDetailsResponseVO::getOrderLogisticsId)
                            .selectAs(OrderErpLogisticsListPO::getOrderSortId, OrderLogisticsDetailsResponseVO::getOrderSort)
                            .selectAs(OrderErpLogisticsListPO::getQuantity, OrderLogisticsDetailsResponseVO::getQuantity)
                            .in(OrderErpLogisticsListPO::getParentId, orderLogisticsId)
            );
            if (CollUtil.isNotEmpty(orderLogisticsDetails)) {
                // 设置型号以及名称
                Map<Integer, OrderDetailsResponseVO> orderDetailsMap = orderInfo.getOrderDetails()
                        .stream()
                        .filter(orderDetailsResponseVO ->
                                StringUtils.isNotBlank(orderDetailsResponseVO.getProductModel()))
                        .collect(Collectors
                                .toMap(OrderDetailsResponseVO::getOrderSortId,
                                        Function.identity(),
                                        (a, b) -> a)
                        );
                orderLogisticsDetails.forEach(orderLogisticsDetailsResponseVO -> {
                    if (orderDetailsMap.containsKey(orderLogisticsDetailsResponseVO.getOrderSort())) {
                        OrderDetailsResponseVO orderDetailsResponseVO = orderDetailsMap.get(orderLogisticsDetailsResponseVO.getOrderSort());
                        orderLogisticsDetailsResponseVO.setProductModel(orderDetailsResponseVO.getProductModel());
                        orderLogisticsDetailsResponseVO.setProductName(orderDetailsResponseVO.getProductName());
                        orderLogisticsDetailsResponseVO.setTotalQuantity(orderDetailsResponseVO.getQuantity());
                    }
                });

                // 将物流明细整合到详情中
                Map<String, List<OrderLogisticsDetailsResponseVO>> orderLogisticsDetailsMap = orderLogisticsDetails.stream().collect(Collectors.groupingBy(OrderLogisticsDetailsResponseVO::getOrderLogisticsId));
                orderErpLogisticsList.forEach(orderLogisticsInformationResponseVO -> orderLogisticsInformationResponseVO.setLogisticsDetails(orderLogisticsDetailsMap.get(orderLogisticsInformationResponseVO.getOrderLogisticsId())));
            }

            //如果存在一条明细多个包裹的就合并发货数量
            Map<Integer, Long> deliveryQuantityMap = orderErpLogisticsList.stream()
                    .map(OrderLogisticsInformationResponseVO::getLogisticsDetails)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(OrderLogisticsDetailsResponseVO::getOrderSort, OrderLogisticsDetailsResponseVO::getQuantity, Long::sum));

            //设置明细发货数量
            orderInfo.getOrderDetails().forEach(orderDetail -> {
                if (deliveryQuantityMap.containsKey(orderDetail.getOrderSortId())) {
                    orderDetail.setDeliveryQuantity(deliveryQuantityMap.get(orderDetail.getOrderSortId()));
                }
            });

            // 设置订单的物流
            orderInfo.setOrderLogisticsInformation(orderErpLogisticsList);
        }
    }
}
