package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderLogisticsInformationResponseVO.java, v0.1 2023/2/20 10:11 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderLogisticsInformationResponseVO extends BaseVO {
    /**
     * 快递单号id
     */
    @Schema(description = "快递单号id", example = "0000a04f4c114ca5abceca9c1930246a")
    private String orderLogisticsId;

    /**
     * 快递单号
     */
    @Schema(description = "快递单号", example = "A0234511441")
    private String expressNumber;

    /**
     * 物流公司
     */
    @Schema(description = "物流公司", example = "顺丰")
    private String logisticsCompany;

    /**
     * 出库单号
     */
    @Schema(description = "出库单号", example = "BI123132456")
    private String deliveryNumber;

    /**
     * 签收状态
     */
    @Schema(description = "签收状态 received已签收 notSigned未签收", example = "notSigned")
    private String signingStatus;

    /**
     * 实际发货时间
     */
    @Schema(description = "实际发货时间", example = "2022-01-01 10:10:10")
    private LocalDateTime deliveryTime;

    /**
     * 发货地 DG 东莞 SZ 苏州 SD 山东 HB 湖北
     */
    @Schema(description = "发货地(DG 东莞 SZ 苏州 SD 山东 HB 湖北)", example = "DG")
    private String origin;
    /**
     * 物流包裹明细
     */
    @Schema(description = "物流包裹明细")
    private List<OrderLogisticsDetailsResponseVO> logisticsDetails;
}
