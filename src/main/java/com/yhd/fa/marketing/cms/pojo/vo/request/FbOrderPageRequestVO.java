package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.OrderSourceConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Size;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 8:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单分页列表数据获取")
public class FbOrderPageRequestVO extends PageRequestVO {


    @Schema(description = "员工工号")
    private String empNo;

    @Schema(description = "企业编码集合")
    private List<String> companyCodeList;

    @Schema(description = "订单id集合")
    @Size(max = 200, message = "id集合长度异常")
    private List<String> idList;

    @Schema(description = "订单id")
    private String orderId;

    @Schema(description = "订单编号")
    private String orderNumber;

    @Schema(description = "订单状态")
    private List<String> orderStatus;

    @Schema(description = "企业名称")
    private String companyName;

    @Schema(description = "下单日期 开始：yyyy-MM-dd")
    private String startDate;

    @Schema(description = "下单日期 结束：yyyy-MM-dd")
    private String endDate;

    @Schema(description = "下单来源  fb_buy一键购买  fb_quotation报价单  （'quotation','mergeQuotation')", example = "fb_buy")
    @ValueInEnum(matchTarget = {CommonConstant.EMPTY , OrderSourceConstant.FB_QUOTATION,OrderSourceConstant.FB_BUY}, message = "请选择正确的下单来源")
    private String orderSource;

    @Schema(description = "报价单号 ")
    private String quotationNumber;

    /**
     * 日期后面加上23:59:59.*********
     * 例:2022-12-19  变成 2022-12-19 23:59:59.*********
     */
    public void parseDate() {
        if (StringUtils.isNotBlank(endDate)) {
            this.endDate = String.join(CommonConstant.SINGLE_SPACE, endDate, LocalTime.MAX.toString());
        }
    }
}
