package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: QuotationPO.java, v0.1 2022/12/1 17:03 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("fa_quotation")
public class QuotationPO extends BaseEntity {
    /**
     * 报价单号
     */
    private String quotationNumber;

    /**
     * 报价单等级
     */
    private String quotationLevel;

    /**
     * 客户单号
     */
    private String customerQuotationNumber;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 用户编号
     */
    private String userCode;

    /**
     * 采购员编号
     */
    private String purchaseUserCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核人id
     */
    private String examineId;

    /**
     * 审核时间
     */
    private LocalDateTime examineDate;

    /**
     * 报价审核完成状态，agree同意、refuse拒绝
     */
    private String examineStatus;

    /**
     * 报价完成时间
     */
    private LocalDateTime quotationCompletionDate;

    /**
     * erp报价单人
     */
    private String quotationUserCode;

    /**
     * 报价状态，not_quoted未报价。quotation快速报价中，finish报价完成，close已终止，outTime超出报价有效期
     */
    private String quotationStatus;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 应付金额
     */
    private BigDecimal payablePrice;

    /**
     * 销售报价单号
     */
    private String saleQuotationNumber;

    /**
     * 删除状态，normal正常，recycle已加入回收站，deleted永久删除
     */
    private String deleteStatus;

    /**
     * 同步状态，synchronized已同步，notSynchronized未同步
     */
    private String synchronizationStatus;

    /**
     * 同步时间
     */
    private LocalDateTime synchronizationDate;

    /**
     * 删除时间
     */
    private LocalDateTime deleteDate;

    /**
     * 是否已转订单，true已转，false未转
     */
    private String transferOrder;

    /**
     * 是否需要管理后台审核，true是，false否
     */
    private String isExamine;

    /**
     * 是否已完成报价推送给用户，pushed已推送，not_pushed未推送
     */
    private String pushUser;

    /**
     * 是否未报价推送给业务员，not_pushed未推送，pushed_two_min为报价单2分钟推送给业务员，pushed_three_hour未报价3小时推送给业务员
     */
    private String pushSalesman;

    /**
     * 无效原因
     */
    private String invalidReason;

    /**
     * 渠道来源
     */
    private String channelType;

    /**
     * 是否内部人员创建
     */
    private String isInsideCreated;

    /**
     * 内部员工编号
     */
    private String insideEmployeeCode;

    /**
     * 员工名字
     */
    private String employeeName;

    /**
     * erp企业编码
     */
    @TableField(exist = false)
    private String erpCompanyCode;
}
