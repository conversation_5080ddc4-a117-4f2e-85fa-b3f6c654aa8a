package com.yhd.fa.marketing.cms.service.impl.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.github.pagehelper.PageInfo;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.EnquiryLogInfoResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.dao.CompanyDAO;
import com.yhd.fa.marketing.cms.dao.EnquiryLogDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.ShareSystemdCompanyLevelEnum;
import com.yhd.fa.marketing.cms.pojo.dto.ShareSystemdCompanyLevelDTO;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.CompanyPO;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import com.yhd.fa.marketing.cms.pojo.po.UcSyncCustomerInfoPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.EnquiryLogListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryLogListResponseVO;
import com.yhd.fa.marketing.cms.service.controller.EnquiryLogService;
import com.yhd.fa.marketing.cms.service.impl.logic.quotation.GetEnquiryLogLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogServiceImpl.java, v0.1 2022/12/8 11:17 yehuasheng Exp $
 */
@Service
public class EnquiryLogServiceImpl implements EnquiryLogService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(EnquiryLogServiceImpl.class.getName());

    /**
     * 用户的服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private CompanyDAO companyDAO;

    /**
     * 查价单的mapper
     */
    @Resource
    private EnquiryLogDAO enquiryLogDAO;
    /**
     * 查价单逻辑
     */
    @Resource
    private GetEnquiryLogLogic getEnquiryLogLogic;

    /**
     * 获取查价单记录集合
     *
     * @param enquiryLogListRequestVO 查价单参数
     * @return BusinessResponse<PageInfo < EnquiryLogListResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<EnquiryLogListResponseVO>> getEnquiryLogList(EnquiryLogListRequestVO enquiryLogListRequestVO) {
        logger.info("start get enquiry log list.");

        // 执行查询
        return getEnquiryLogLogic.exec(enquiryLogListRequestVO);
    }

    @Override
    public BusinessResponse<String> enquiryLogAdd(List<EnquiryLogPO> enquiryLogPOList) {
        if(CollectionUtils.isEmpty(enquiryLogPOList)){
            return BusinessResponse.ok(FaDocMarketingResponseEnum.SUCCESS.getDesc());
        }
        SpringUtil.getBean(EnquiryLogService.class).addSync(enquiryLogPOList);
        return  BusinessResponse.ok(FaDocMarketingResponseEnum.SUCCESS.getDesc());
    }


    @Async("MyTaskAsync")
    public void addSync(List<EnquiryLogPO> enquiryLogPOList) {
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = enquiryLogPOList
                .stream()
                .map(enquiryLogPO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .userCode(enquiryLogPO.getUserCode())
                        .companyCode(enquiryLogPO.getCompanyCode())
                        .build())
                .collect(Collectors.toList());
        // 获取用户的信息
        Map<String, EnquiryLogInfoResponseVO> userInfoMap = userBucCmsService.getEnquiryLogInfo(userCodeAndCompanyCodeDTO);


        enquiryLogPOList
                .stream()
                .filter(enquiryLogPO
                        -> StringUtils.isNotBlank(enquiryLogPO.getUserCode())
                        && userInfoMap.containsKey(enquiryLogPO.getUserCode()))
                .forEach(enquiryLogPO -> Optional.ofNullable(userInfoMap.get(enquiryLogPO.getUserCode())).ifPresent(enquiryLogInfoResponseVO -> {
                    // 设置用户的名字和企业的名称
                    enquiryLogPO.setUserName(StringUtils.isBlank(enquiryLogInfoResponseVO.getUserName())?null:enquiryLogInfoResponseVO.getUserName());
                    enquiryLogPO.setUserPhone(StringUtils.isBlank(enquiryLogInfoResponseVO.getMobile())?null:enquiryLogInfoResponseVO.getMobile());
                    enquiryLogPO.setUserEmail(StringUtils.isBlank(enquiryLogInfoResponseVO.getEmail())?null:enquiryLogInfoResponseVO.getEmail());
                    enquiryLogPO.setMerchandiser(StringUtils.isBlank(enquiryLogInfoResponseVO.getMerchandiserEmployeeCode())?null:enquiryLogInfoResponseVO.getMerchandiserEmployeeName() + CommonConstant.SLASH + enquiryLogInfoResponseVO.getMerchandiserEmployeeCode());
                    enquiryLogPO.setSalesman(StringUtils.isBlank(enquiryLogInfoResponseVO.getSalesmanEmployeeCode())?null:enquiryLogInfoResponseVO.getSalesmanEmployeeName() + CommonConstant.SLASH + enquiryLogInfoResponseVO.getSalesmanEmployeeCode());
                    enquiryLogPO.setCompanyName(StringUtils.isBlank(enquiryLogInfoResponseVO.getCompanyName())?null:enquiryLogInfoResponseVO.getCompanyName());
                }));

        List<String> companyList = enquiryLogPOList.stream().map(EnquiryLogPO::getCompanyCode).distinct().collect(Collectors.toList());
        if(CollUtil.isNotEmpty(companyList)){
            companyDAO.selectJoinList(ShareSystemdCompanyLevelDTO.class, new MPJLambdaWrapper<CompanyPO>()
                            .leftJoin(UcSyncCustomerInfoPO.class, UcSyncCustomerInfoPO::getCustomerNo, CompanyPO::getErpCompanyCode)
                            .selectAs(CompanyPO::getCompanyCode, ShareSystemdCompanyLevelDTO::getCompanyCode)
                            .selectAs(UcSyncCustomerInfoPO::getCustomerNo, ShareSystemdCompanyLevelDTO::getCustomerNo)
                            .selectAs(UcSyncCustomerInfoPO::getCustomerGrade, ShareSystemdCompanyLevelDTO::getCustomerGrade)
                            .selectAll(CompanyPO.class)
                            .in(CompanyPO::getCompanyCode, companyList))
                    .forEach(ucSyncCustomerInfoPrivatePO -> enquiryLogPOList.stream()
                            .filter(enquiryLogPO -> StringUtils.equals(enquiryLogPO.getCompanyCode(), ucSyncCustomerInfoPrivatePO.getCompanyCode()))
                            .forEach(enquiryLogPO -> enquiryLogPO.setCustomerGrade(ShareSystemdCompanyLevelEnum.getName(ucSyncCustomerInfoPrivatePO.getCustomerGrade()))));
        }

        enquiryLogDAO.insertBatch(enquiryLogPOList);
    }
}
