package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: FaQuotationSynchronizationOfflineDTO.java, v0.1 2023/1/13 14:25 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaQuotationSynchronizationOfflineDTO extends BaseDTO {
    /**
     * 报价单id
     */
    private String id;

    /**
     * 报价单号
     */
    private String quotationNumber;

    /**
     * 报价单等级
     */
    private String quotationLevel;

    /**
     * 客户单号
     */
    private String customerQuotationNumber;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 用户编号(选型人)
     */
    private String userCode;

    /**
     * 采购员编号(采购人)
     */
    private String purchaseUserCode;

    /**
     * 报价状态，not_quoted未报价。quotation快速报价中，finish报价完成，close已终止，outTime超出报价有效期
     */
    private String quotationStatus;

    /**
     * 总价(总金额)
     */
    private BigDecimal totalPrice;

    /**
     * 应付金额(含税金额)
     */
    private BigDecimal payablePrice;

    /**
     * 渠道来源
     */
    private String channelType;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * erp企业编码
     */
    private String erpCompanyCode;

    /**
     * 报价单详情
     */
    private List<FaQuotationDetailSynchronizationOfflineDTO> quotationDetails;
}
