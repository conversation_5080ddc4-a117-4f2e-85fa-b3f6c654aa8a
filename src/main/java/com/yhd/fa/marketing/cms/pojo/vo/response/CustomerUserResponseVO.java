package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.common.util.CommonConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: CustomerUserResponseVO.java, v0.1 2023/1/3 17:36 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerUserResponseVO extends BaseVO {
    /**
     * 用户名称
     */
    @Schema(description = "用户名称", example = "测试")
    private String userName;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "12")
    private String userCode;

    /**
     * 是否有创建订单的权限 true有 false没有
     */
    @Schema(description = "是否有创建订单的权限 true有 false没有", example = CommonConstant.TRUE, allowableValues = {CommonConstant.TRUE, CommonConstant.FALSE})
    private boolean createOrderPermission;
}
