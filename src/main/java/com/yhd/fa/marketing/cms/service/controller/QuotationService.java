package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: QuotationService.java, v0.1 2022/12/2 11:02 yehuasheng Exp $
 */
public interface QuotationService {
    /**
     * 报价单列表
     *
     * @param quotationInfoRequestVO 获取报价单列表的参数
     * @return BusinessResponse<PageInfo < QuotationInfoResponseVO>>
     */
    BusinessResponse<PageInfo<QuotationInfoResponseVO>> getQuotationList(QuotationInfoRequestVO quotationInfoRequestVO);

    /**
     * 获取报价单详情
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<QuotationDetailResponseVO>
     */
    BusinessResponse<QuotationDetailResponseVO> getQuotationDetail(String quotationId);

    /**
     * 获取审核报价单列表
     *
     * @param approveQuotationRequestVO 审核报价单参数
     * @return BusinessResponse<PageInfo < ApproveQuotationResponseVO>>
     */
    BusinessResponse<PageInfo<ApproveQuotationResponseVO>> getApproveQuotationList(ApproveQuotationRequestVO approveQuotationRequestVO);

    /**
     * 获取审核报价单详情
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<ApproveQuotationResponseVO>
     */
    BusinessResponse<ApproveQuotationDetailResponseVO> getApproveQuotationDetail(String quotationId);

    /**
     * 获取审核报价单明细中型号的类别编码
     *
     * @param approveQuotationSelectionTypeRequestVO 请求获取类别参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    BusinessResponse<List<ApproveQuotationTypeResponseVO>> getApproveQuotationType(ApproveQuotationSelectionTypeRequestVO approveQuotationSelectionTypeRequestVO);

    /**
     * 获取审核报价单明细中型号的类别编码
     *
     * @param approveQuotationSelectionCategoryRequestVO 请求获取类别参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    BusinessResponse<List<ApproveQuotationCategoryResponseVO>> getApproveQuotationCategory(ApproveQuotationSelectionCategoryRequestVO approveQuotationSelectionCategoryRequestVO);

    /**
     * 检查客户型号价格和交期
     *
     * @param checkApproveQuotationParameterRequestVO 请求参数
     * @return BusinessResponse<List < QuotationResponseVO>>
     */
    BusinessResponse<List<QuotationResponseVO>> checkCustomerModel(CheckApproveQuotationParameterRequestVO checkApproveQuotationParameterRequestVO);

    /**
     * 保存待审核报价单
     *
     * @param approveQuotationRequestVO 待审核报价单参数
     * @return BusinessResponse<Object>
     */
    BusinessResponse<Object> saveApproveQuotation(SaveApproveQuotationRequestVO approveQuotationRequestVO);

    /**
     * 获取客户信息列表
     *
     * @param customerListRequestVO 请求获取客户信息参数
     * @return BusinessResponse<PageInfo < CustomerListResponseVO>>
     */
    BusinessResponse<List<CustomerCompanyListResponseVO>> getCustomerList(CustomerListRequestVO customerListRequestVO);

    /**
     * 创建报价单
     *
     * @param createQuotationRequestVO 创建报价单参数
     * @return BusinessResponse<String>
     */
    BusinessResponse<String> createQuotation(CreateQuotationRequestVO createQuotationRequestVO);

    /**
     * 同步报价单
     *
     * @param quotationId 报价单id
     * @return BusinessResponse<Void>
     */
    BusinessResponse<Void> synchronizationQuotation(String quotationId);

    /**
     * 获取企业列表
     *
     * @param companyNameRequestVO 企业名称
     * @return BusinessResponse<List < CustomerUserResponseVO>>
     */
    BusinessResponse<List<CustomerCompanyListResponseVO>> getCompanyListByCompanyName(CompanyNameRequestVO companyNameRequestVO);

    /**
     * 根据企业编码获取用户列表
     *
     * @param userListByCompanyCodeRequestVO 企业编码请求参数
     * @return BusinessResponse<List < CustomerUserResponseVO>>
     */
    BusinessResponse<List<CustomerUserResponseVO>> getUserListByCompanyCode(UserListByCompanyCodeRequestVO userListByCompanyCodeRequestVO);
}
