package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRemarkRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsReplyRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProblemCategoriesListResponseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsService.java, v0.1 2023/2/24 18:53 yehuasheng Exp $
 */
public interface OrderCommentsService {
    /**
     * 获取订单评论列表
     *
     * @param orderCommentsRequestVO 订单评论列表请求参数
     * @return BusinessResponse<PageInfo < OrderCommentsResponseVO>>
     */
    BusinessResponse<PageInfo<OrderCommentsResponseVO>> getOrderCommentsList(OrderCommentsRequestVO orderCommentsRequestVO);

    /**
     * 获取订单评论详情页
     *
     * @param orderCommentsId 订单评论id
     * @return BusinessResponse<OrderCommentsDetailResponseVO>
     */
    BusinessResponse<OrderCommentsDetailResponseVO> getOrderCommentsInfo(String orderCommentsId);

    /**
     * 订单评价回复
     *
     * @param requestVO 请求参数
     * @return BusinessResponse<Object>
     */
    BusinessResponse<Object> orderCommentsReply(OrderCommentsReplyRequestVO requestVO);

    /**
     * 订单评论详情页修改备注
     * @param requestVO 请求参数
     * @return BusinessResponse<Object>
     */
    BusinessResponse<Object> orderCommentsRemark(OrderCommentsRemarkRequestVO requestVO);

    /**
     * 获取订单评论问题分类列表
     *
     * @return BusinessResponse
     */
    BusinessResponse<List<ProblemCategoriesListResponseVO>> orderCommentsProblemCategoriesList();
}
