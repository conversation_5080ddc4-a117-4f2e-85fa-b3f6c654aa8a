package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ModelSplitCodeRequestVO.java, v0.1 2023/5/5 14:30 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelSplitCodeRequestVO extends BaseVO {
    /**
     * 型号集合
     */
    private List<String> model;

    /**
     * 来源 线上 online  线下offline
     */
    private String source;
}
