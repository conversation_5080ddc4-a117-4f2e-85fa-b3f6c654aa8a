package com.yhd.fa.marketing.cms.service.impl.sao;

import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.QuotationSourceConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.ModelSplitCodeRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ModelSplitCodeResponseVO;
import com.yhd.fa.marketing.cms.sao.ProductBasisSAO;
import com.yhd.fa.marketing.cms.service.sao.ProductBasisService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: ProductBasisServiceImpl.java, v0.1 2023/5/5 14:34 yehuasheng Exp $
 */
@Service
public class ProductBasisServiceImpl implements ProductBasisService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(ProductBasisServiceImpl.class.getName());

    /**
     * 产品基础信息sao
     */
    @Resource
    private ProductBasisSAO productBasisSAO;

    /**
     * 型号拆分代码信息
     *
     * @param model 型号集合
     * @return Map<String, ModelSplitCodeResponseVO>
     */
    @Override
    public Map<String, ModelSplitCodeResponseVO> getModelSplitCodeMap(List<String> model) {
        logger.info("get model split code map info.");

        // 定义输出的对象
        Map<String, ModelSplitCodeResponseVO> modelSplitCodeMap = new HashMap<>();

        try {
            // 设置请求时间
            Stopwatch stopwatch = Stopwatch.createStarted();

            // 设置请求参数
            ModelSplitCodeRequestVO modelSplitCodeRequestVO = ModelSplitCodeRequestVO
                    .builder()
                    .model(model)
                    .source(QuotationSourceConstant.ONLINE)
                    .build();
            logger.info("get model split code map info parameter modelSplitCodeRequestVO:{}", modelSplitCodeRequestVO);

            // 请求接口
            BusinessResponse<List<ModelSplitCodeResponseVO>> businessResponse = productBasisSAO.modelSplitCode(modelSplitCodeRequestVO);
            logger.info("get model split code success. response code:{} msg:{}", businessResponse.getRt_code(), businessResponse.getRt_msg());
            logger.info("get model split code cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            // 整合输出的内容
            if (businessResponse.success()) {
                modelSplitCodeMap.putAll(businessResponse.getData().stream().collect(Collectors.toMap(ModelSplitCodeResponseVO::getModel, Function.identity(), (a, b) -> a)));
            }
        } catch (Exception e) {
            logger.error("get model split code map fail. errorMsg:{} model:{}", e.getMessage(), model);
        }

        // 输出结果
        return modelSplitCodeMap;
    }
}
