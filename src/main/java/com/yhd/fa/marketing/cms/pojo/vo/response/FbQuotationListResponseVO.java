package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/17 9:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "报价单分页列表数据获取")
public class FbQuotationListResponseVO  extends BaseVO {
    @Schema(description = "报价单id")
    private String id;

    @Schema(description = "报价单号")
    private String quotationNumber;

    @Schema(description = "报价单等级（ordCo普通企业，certCo认证企业）")
    private String quotationLevel;

    @Schema(description = "客户报价单号")
    private String customerQuotationNumber;

    @Schema(description = "企业编码")
    private String companyCode;

    @Schema(description = "企业名称")
    private String companyName;

    @Schema(description = "报价状态")
    private String quotationStatus;

    @Schema(description = "创建人用户编码")
    private String userCode;

    @Schema(description = "采购用户编码")
    private String purchaseUserCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "报价完成时间")
    private String quotationCompletionDate;

    @Schema(description = "erp报价人员")
    private String quotationUserCode;

    @Schema(description = "含税总金额")
    private String totalPrice;


    @Schema(description = "是否转单")
    private String transferOrder;

    @Schema(description = "制单日期")
    private String createdDate;

    @Schema(description = "公司归属")
    private String ownershipCompany;

    @Schema(description = "跟单员")
    private String merchandiser;

    @Schema(description = "业务员")
    private String operator;


    @Schema(description = "销售部门")
    private String unitName;

    private List<FbQuotationDetailChildResponseVO> fbQuotationDetailChildResponseVOList;

}
