package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationTypeResponseVO.java, v0.1 2022/12/22 10:02 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveQuotationTypeResponseVO extends BaseVO {
    /**
     * 分类编码
     */
    @Schema(description = "一级分类或者二级分类或者代码的编码", example = "A")
    private String code;

    /**
     * 分类名称
     */
    @Schema(description = "一级分类或者二级分类或者代码的名称", example = "导向轴")
    private String codeName;
}
