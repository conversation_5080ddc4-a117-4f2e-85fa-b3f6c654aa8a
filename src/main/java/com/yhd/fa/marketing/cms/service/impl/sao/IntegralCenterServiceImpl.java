package com.yhd.fa.marketing.cms.service.impl.sao;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.AddUserIntegralRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.BatchAddUserIntegralRequestVO;
import com.yhd.fa.marketing.cms.sao.IntegralCenterSAO;
import com.yhd.fa.marketing.cms.service.sao.IntegralCenterService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: IntegralCenterServiceImpl.java, v 0.1 2023/3/14 15:12 JiangYuHong Exp $
 */
@Service
public class IntegralCenterServiceImpl implements IntegralCenterService {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(IntegralCenterServiceImpl.class.getName());

    @Resource
    private IntegralCenterSAO integralCenterSAO;

    /**
     * 添加积分
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @Override
    public BusinessResponse<Object> addUserIntegral(AddUserIntegralRequestVO requestVO) {

        //请求积分中心给用户添加积分
        logger.info("start add user integral logic. parameter:{}", requestVO);
        try {

            BusinessResponse<Object> businessResponse = integralCenterSAO.addUserIntegral(requestVO);

            if (!businessResponse.success()) {
                logger.error("add user integral fail. errorMsg:{}", businessResponse.getRt_msg());
                return BusinessResponse.fail(null);
            }
        } catch (Exception e) {
            logger.error("add user integral fail. errorMsg:{}", e.toString());
            return BusinessResponse.fail(null);
        }


        return BusinessResponse.ok(null);
    }

    /**
     * 批量添加积分（慎用，应与积分中心沟通批量添加的时间）
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @Override
    public BusinessResponse<Object> batchAddUserIntegral(BatchAddUserIntegralRequestVO requestVO) {

        //请求积分中心给用户添加积分
        logger.info("start batch add user integral logic. parameter:{}", requestVO);
        try {

            BusinessResponse<Object> businessResponse = integralCenterSAO.batchAddUserIntegral(requestVO);

            if (!businessResponse.success()) {
                logger.error("add user integral fail. errorMsg:{}", businessResponse.getRt_msg());
                return BusinessResponse.fail(null);
            }
        } catch (Exception e) {
            logger.error("add user integral fail. errorMsg:{}", e.toString());
            return BusinessResponse.fail(null);
        }


        return BusinessResponse.ok(null);
    }
}
