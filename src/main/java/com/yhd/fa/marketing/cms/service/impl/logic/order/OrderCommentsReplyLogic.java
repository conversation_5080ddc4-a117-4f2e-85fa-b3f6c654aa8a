package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.dao.OrderCommentsCategoriesRelDAO;
import com.yhd.fa.marketing.cms.dao.OrderCommentsDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCommentsCategoriesRelMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsCategoriesRelPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.AddUserIntegralRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsReplyRequestVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.IntegralCenterService;
import com.yhd.fa.marketing.cms.util.BaseUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsReplyLogic.java, v 0.1 2023/3/6 15:17 JiangYuHong Exp $
 */
@Component
public class OrderCommentsReplyLogic {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderCommentsReplyLogic.class.getName());

    /**
     * 订单评价DAO
     */
    @Resource
    private OrderCommentsDAO orderCommentsDAO;

    /**
     * 积分中心接口
     */
    @Resource
    private IntegralCenterService integralCenterService;
    @Resource
    private DataAuthLogic dataAuthLogic;

    @Resource
    private OrderCommentsCategoriesRelMapper orderCommentsCategoriesRelMapper;
    @Resource
    private OrderCommentsCategoriesRelDAO orderCommentsCategoriesRelDAO;

    /**
     * 订单评价回复
     *
     * @param requestVO 请求参数
     * @return BusinessResponse<Object>
     */
    public BusinessResponse<Object> orderCommentsReply(OrderCommentsReplyRequestVO requestVO) {

        logger.info("start order comments reply logic.");

        //查询订单评论信息是否存在
        MPJLambdaWrapper<OrderCommentsPO> queryWrapper = new MPJLambdaWrapper<>();

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderCommentsPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        queryWrapper.selectAll(OrderCommentsPO.class).eq(OrderCommentsPO::getId, requestVO.getCommentsId());

        OrderCommentsPO orderCommentsPO = orderCommentsDAO.selectOne(queryWrapper);
        if (ObjectUtil.isNull(orderCommentsPO)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_COMMENTS_INFO_NOT_EXIST);
        }
        //判断是否已回复
        if (StringUtils.equals(orderCommentsPO.getReplied(), CommonConstant.TRUE)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_COMMENTS_REPLIED);
        }
        //设置更新条件构造器
        LambdaUpdateWrapper<OrderCommentsPO> updateWrapper = new LambdaUpdateWrapper<OrderCommentsPO>().eq(OrderCommentsPO::getId, requestVO.getCommentsId());

        updateWrapper.set(OrderCommentsPO::getReply, requestVO.getReplyContent())
                .set(OrderCommentsPO::getRepliedDate, LocalDateTime.now())
                .set(OrderCommentsPO::getReplied, CommonConstant.TRUE)
                .set(OrderCommentsPO::getReplyPeople, SecurityUtil.getAccountNoAndUserName())
                .set(OrderCommentsPO::getVisitRecords, requestVO.getVisitRecords())
                .set(OrderCommentsPO::getBonusPoints, requestVO.getPoints());
        BaseUtil.setLambdaUpdateWrapperUpdateParams(updateWrapper, SecurityUtil.getAccountNoAndUserName());

        if(StringUtils.isNotBlank(requestVO.getRemark())){
            updateWrapper.set(OrderCommentsPO::getRemark,requestVO.getRemark());
        }

        int update = 0;
        int updatedCategoriesRel = 0;
        try {
            update = orderCommentsDAO.update(null, updateWrapper);
            updatedCategoriesRel = updateCategoriesRelId(orderCommentsPO, requestVO.getProblemCategoriesIds());
        } catch (Exception e) {
            logger.error("update order comments fail. errorMsg:{}", e.toString());
        }

        if (update < CommonConstant.ONE || updatedCategoriesRel < CommonConstant.ONE) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_COMMENTS_UPDATE_FAIL);
        }

        //赠送积分
        SpringUtil.getBean(OrderCommentsReplyLogic.class).addIntegral(requestVO.getPoints(), orderCommentsPO.getUserCode(), orderCommentsPO.getId());

        return BusinessResponseCommon.ok(null);
    }

    /**
     * 更新问题分类关联关系
     *
     * @param orderCommentsPO      订单评论信息
     * @param problemCategoriesIds 问题分类id
     * @return 更新结果
     */
    private int updateCategoriesRelId(OrderCommentsPO orderCommentsPO, List<Integer> problemCategoriesIds) {
        // 如果没有问题分类ID，直接返回成功
        if (problemCategoriesIds.isEmpty()) {
            return 1;
        }

        // 构建分类关系对象列表
        List<OrderCommentsCategoriesRelPO> categoriesRelList = problemCategoriesIds.stream()
                .map(categoryId -> OrderCommentsCategoriesRelPO.builder()
                        .commentId(orderCommentsPO.getId())
                        .categoryId(categoryId)
                        .createdBy(SecurityUtil.getAccountNoAndUserName())
                        .createdDate(LocalDateTime.now())
                        .updatedBy(SecurityUtil.getAccountNoAndUserName())
                        .updatedDate(LocalDateTime.now())
                        .build())
                .collect(Collectors.toList());

        // 如果已有关联记录，先删除旧记录
        orderCommentsCategoriesRelDAO.delete(
                new LambdaUpdateWrapper<OrderCommentsCategoriesRelPO>()
                        .eq(OrderCommentsCategoriesRelPO::getCommentId, orderCommentsPO.getId())
        );

        // 保存新记录
        return orderCommentsCategoriesRelMapper.saveBatch(categoriesRelList) ? 1 : 0;
    }


    /**
     * 添加积分
     *
     * @param integral   积分
     * @param userCode   用户编码
     * @param commentsId 问题反馈id
     */
    @Async
    public void addIntegral(Integer integral, String userCode, String commentsId) {
        AddUserIntegralRequestVO addUserIntegralRequestVO = new AddUserIntegralRequestVO();
        addUserIntegralRequestVO.setIntegralNumber(commentsId);
        addUserIntegralRequestVO.setUserCode(userCode);
        addUserIntegralRequestVO.setPlatform(FaDocMarketingCmsConstant.PLATFORM_CODE);
        addUserIntegralRequestVO.setRuleName("订单评价赠送积分");
        addUserIntegralRequestVO.setIntegral(integral);
        addUserIntegralRequestVO.setOperator(SecurityUtil.getAccountNoAndUserName());
        integralCenterService.addUserIntegral(addUserIntegralRequestVO);
    }
}
