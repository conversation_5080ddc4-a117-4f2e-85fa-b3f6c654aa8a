package com.yhd.fa.marketing.cms.mapper.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.SaleCompanyRelationDAO;
import com.yhd.fa.marketing.cms.mapper.SaleCompanyRelationMapper;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogMapperImpl.java, v0.1 2022/12/6 10:28 yehuasheng Exp $
 */
@Service
@DS("ch")
public class SaleCompanyRelationMapperImpl extends MPJBaseServiceImpl<SaleCompanyRelationDAO, SaleCompanyRelationPO> implements SaleCompanyRelationMapper {

    @Async("MyTaskAsync")
    @Override
    @DS("")
    public void saveAllToOrderDatabase(List<SaleCompanyRelationPO> list) {
        saveBatch(list);
    }

    @Async("MyTaskAsync")
    @DS("quotation")
    @Override
    public void saveAllToQuotationDatabase(List<SaleCompanyRelationPO> list) {
        saveBatch(list);
    }
}
