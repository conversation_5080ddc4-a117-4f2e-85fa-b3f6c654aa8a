package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.UserInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.OrderByFieldConstant;
import com.yhd.fa.marketing.cms.dao.OrderCommentsCategoriesRelDAO;
import com.yhd.fa.marketing.cms.dao.OrderCommentsProblemCategoriesDAO;
import com.yhd.fa.marketing.cms.mapper.OrderCommentsMapper;
import com.yhd.fa.marketing.cms.pojo.dto.OrderCommentsProblemCategoriesDTO;
import com.yhd.fa.marketing.cms.pojo.po.*;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderCommentsRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.SqlUtil;
import io.seata.common.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderCommentsListLogic.java, v0.1 2023/2/24 19:07 yehuasheng Exp $
 */
@Component
public class GetOrderCommentsListLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(GetOrderCommentsListLogic.class.getName());

    /**
     * 订单评论mapper
     */
    @Resource
    private OrderCommentsMapper orderCommentsMapper;

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    @Resource
    private OrderCommentsProblemCategoriesDAO orderCommentsProblemCategoriesDAO;

    @Resource
    private OrderCommentsCategoriesRelDAO orderCommentsCategoriesRelDAO;

    /**
     * 获取订单评论列表
     *
     * @param orderCommentsRequestVO 请求订单评论参数
     * @return BusinessResponse<PageInfo < OrderCommentsResponseVO>>
     */
    public BusinessResponse<PageInfo<OrderCommentsResponseVO>> exec(OrderCommentsRequestVO orderCommentsRequestVO) {
        logger.info("start exec get order comments list logic.");

        // 设置分页参数
        PageMethod.startPage(orderCommentsRequestVO.getPageNum(), orderCommentsRequestVO.getPageSize());

        // 设置查询字段以及条件
        MPJLambdaWrapper<OrderCommentsPO> queryWrapper = setOrderCommentsQueryWrapper(orderCommentsRequestVO);

        // 查询列表
        List<OrderCommentsResponseVO> orderCommentsList = orderCommentsMapper.selectJoinList(OrderCommentsResponseVO.class, queryWrapper);

        // 设置分页
        PageInfo<OrderCommentsResponseVO> pageInfo = new PageInfo<>(orderCommentsList);

        // 设置其他值
        setOrderCommentsOtherValue(pageInfo.getList());

        // 设置问题分类
        setProblemCategories(pageInfo.getList());

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 设置订单评论查询字段以及条件
     *
     * @param orderCommentsRequestVO 订单评论请求参数
     * @return MPJLambdaWrapper<OrderCommentsPO>
     */
    private MPJLambdaWrapper<OrderCommentsPO> setOrderCommentsQueryWrapper(OrderCommentsRequestVO orderCommentsRequestVO) {
        logger.info("set order comments query wrapper.");

        // 获取查询字段
        MPJLambdaWrapper<OrderCommentsPO> queryWrapper = OrderUtil.setOrderCommentsSelectAs()
                .distinct();

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderCommentsPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 查询订单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderCommentsRequestVO.getOrderNumber(), queryWrapper, OrderCommentsPO::getOrderNumber);

        // 查询企业名称
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderCommentsRequestVO.getCompanyName(), queryWrapper, OrderCommentsPO::getCompanyName);

        // 设置查询是否已评论
        setRepliedQueryWrapper(queryWrapper, orderCommentsRequestVO);

        //设置查询时间
        setTimeQueryWrapper(queryWrapper, orderCommentsRequestVO);

        //设置查询问题分类
        setCategoriesQueryWrapper(queryWrapper, orderCommentsRequestVO);

        //设置查询备注
        Optional.ofNullable(orderCommentsRequestVO.getRemark())
                .filter(StringUtils::isNotBlank)
                .ifPresent(remark -> queryWrapper.eq(OrderCommentsPO::getRemark, remark));

        //设置排序
        setSortQueryWrapper(queryWrapper, orderCommentsRequestVO);

        return queryWrapper;
    }

    /**
     * 设置查询问题分类
     *
     * @param queryWrapper           查询条件
     * @param orderCommentsRequestVO 请求参数
     */
    private void setCategoriesQueryWrapper(MPJLambdaWrapper<OrderCommentsPO> queryWrapper, OrderCommentsRequestVO orderCommentsRequestVO) {
        if (null == orderCommentsRequestVO.getProblemCategoriesId() || orderCommentsRequestVO.getProblemCategoriesId().isEmpty())
            return;

        queryWrapper.leftJoin(OrderCommentsCategoriesRelPO.class, OrderCommentsCategoriesRelPO::getCommentId, OrderCommentsPO::getId)
                .in(OrderCommentsCategoriesRelPO::getCategoryId, orderCommentsRequestVO.getProblemCategoriesId());
    }

    /**
     * 设置排序
     *
     * @param queryWrapper           查询条件
     * @param orderCommentsRequestVO 请求参数
     */
    private void setSortQueryWrapper(MPJLambdaWrapper<OrderCommentsPO> queryWrapper, OrderCommentsRequestVO orderCommentsRequestVO) {
        if (StringUtils.isNotBlank(orderCommentsRequestVO.getOrderByField()) && StringUtils.isNotBlank(orderCommentsRequestVO.getOrderByType())) {
            queryWrapper.orderBy(true, StringUtils.equals(orderCommentsRequestVO.getOrderByType().toUpperCase(), OrderByFieldConstant.ASC),
                    OrderCommentsPO::getCompanyNameInitial, OrderCommentsPO::getCompanyName);
        } else {
            queryWrapper.orderByDesc(OrderCommentsPO::getCreatedDate);
        }
    }

    /**
     * 设置查询传时间
     *
     * @param queryWrapper           条件构造器
     * @param orderCommentsRequestVO 请求参数
     */
    private void setTimeQueryWrapper(MPJLambdaWrapper<OrderCommentsPO> queryWrapper, OrderCommentsRequestVO orderCommentsRequestVO) {
        if (null != orderCommentsRequestVO.getStartDateTime()) {
            queryWrapper.ge(OrderPO::getCreatedDate, orderCommentsRequestVO.getStartDateTime());
        }
        if (null != orderCommentsRequestVO.getEndDateTime()) {
            queryWrapper.le(OrderPO::getCreatedDate, orderCommentsRequestVO.getEndDateTime());
        }
    }

    /**
     * 设置查询是否已评论、产品描述相符度、人员服务态度、产品交付时效
     *
     * @param queryWrapper           查询条件
     * @param orderCommentsRequestVO 请求参数
     */
    private void setRepliedQueryWrapper(MPJLambdaWrapper<OrderCommentsPO> queryWrapper, OrderCommentsRequestVO orderCommentsRequestVO) {
        logger.info("set replied query wrapper.");

        if (StringUtils.isNotBlank(orderCommentsRequestVO.getReplied())) {
            queryWrapper.eq(OrderCommentsPO::getReplied, orderCommentsRequestVO.getReplied());
        }
        queryWrapper.and(CollectionUtils.isNotEmpty(orderCommentsRequestVO.getProductDescRating()) || CollectionUtils.isNotEmpty(orderCommentsRequestVO.getPersonnelServiceRating())
                || CollectionUtils.isNotEmpty(orderCommentsRequestVO.getProductDeliveryRating()), lambdaWrapper ->
                lambdaWrapper.in(orderCommentsRequestVO.getProductDescRating() != null && !orderCommentsRequestVO.getProductDescRating().isEmpty(), OrderCommentsPO::getProductDescRating, orderCommentsRequestVO.getProductDescRating()).or()
                        .in(orderCommentsRequestVO.getPersonnelServiceRating() != null && !orderCommentsRequestVO.getPersonnelServiceRating().isEmpty(), OrderCommentsPO::getPersonnelServiceRating, orderCommentsRequestVO.getPersonnelServiceRating()).or()
                        .in(orderCommentsRequestVO.getProductDeliveryRating() != null && !orderCommentsRequestVO.getProductDeliveryRating().isEmpty(), OrderCommentsPO::getProductDeliveryRating, orderCommentsRequestVO.getProductDeliveryRating()));

    }

    /**
     * 设置订单评论其他值
     *
     * @param orderCommentsList 订单评论列表
     */
    private void setOrderCommentsOtherValue(List<OrderCommentsResponseVO> orderCommentsList) {
        logger.info("set order comments other value.");

        if (CollUtil.isNotEmpty(orderCommentsList)) {
            // 设置用户名称
            Map<String, UserInfo> userBaseInfo = getUserBaseInfo(orderCommentsList);
            // 设置用户名
            orderCommentsList.forEach(orderCommentsResponseVO ->
                    Optional.ofNullable(userBaseInfo.get(orderCommentsResponseVO.getUserCode()))
                            .ifPresent(userInfo ->
                                    orderCommentsResponseVO.setUserName(userInfo.getUserName()))
            );
        }
    }

    /**
     * 获取用户信息
     *
     * @param orderCommentsList 订单评论列表
     * @return Map<String, BaseUserInfoResponseVO>
     */
    private Map<String, UserInfo> getUserBaseInfo(List<OrderCommentsResponseVO> orderCommentsList) {
        logger.info("get order list user info.");

        // 提取订单列表中的用户编码
        List<String> userCode = orderCommentsList.stream().map(OrderCommentsResponseVO::getUserCode).distinct().collect(Collectors.toList());

        // 获取用户信息map
        return userBucCmsService.getUserBaseMapByUserCode(userCode);
    }

    /**
     * 设置问题分类
     *
     * @param orderCommentsList 订单评论列表
     */
    private void setProblemCategories(List<OrderCommentsResponseVO> orderCommentsList) {
        logger.info("set order comments problem categories.");

        if (CollUtil.isEmpty(orderCommentsList)) return;

        List<String> orderCommentsIds = orderCommentsList.stream().map(OrderCommentsResponseVO::getOrderCommentsId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(orderCommentsIds)) return;


        List<OrderCommentsCategoriesRelPO> orderCommentsCategoriesRelPOS = orderCommentsCategoriesRelDAO.selectList(
                new LambdaQueryWrapper<OrderCommentsCategoriesRelPO>().in(OrderCommentsCategoriesRelPO::getCommentId, orderCommentsIds));
        if (CollUtil.isEmpty(orderCommentsCategoriesRelPOS)) return;


        List<Integer> problemCategoriesIds = orderCommentsCategoriesRelPOS.stream()
                .map(OrderCommentsCategoriesRelPO::getCategoryId)
                .distinct()
                .collect(Collectors.toList());

        // 设置问题分类
        List<OrderCommentsProblemCategoriesDTO> categoriesPOList = orderCommentsProblemCategoriesDAO.selectJoinList(
                OrderCommentsProblemCategoriesDTO.class,
                new MPJLambdaWrapper<OrderCommentsProblemCategoriesPO>()
                        .leftJoin(OrderCommentsProblemCategoriesPO.class, "p", OrderCommentsProblemCategoriesPO::getId, OrderCommentsProblemCategoriesPO::getParentId)
                        .selectAs(OrderCommentsProblemCategoriesPO::getId, OrderCommentsProblemCategoriesDTO::getChildId)
                        .selectAs(OrderCommentsProblemCategoriesPO::getName, OrderCommentsProblemCategoriesDTO::getChildName)
                        .selectAs("p", OrderCommentsProblemCategoriesPO::getId, OrderCommentsProblemCategoriesDTO::getParentId)
                        .selectAs("p", OrderCommentsProblemCategoriesPO::getName, OrderCommentsProblemCategoriesDTO::getParentName)
                        .in(OrderCommentsProblemCategoriesPO::getId, problemCategoriesIds)
        );

        Map<Integer, OrderCommentsProblemCategoriesDTO> categoriesMap = categoriesPOList.stream()
                .collect(Collectors.toMap(OrderCommentsProblemCategoriesDTO::getChildId, Function.identity(), (k1, k2) -> k1));

        // 使用groupingBy直接构建commentId到分类列表的映射
        Map<String, List<String>> problemCategoriesMap = orderCommentsCategoriesRelPOS.stream()
                .filter(rel -> categoriesMap.containsKey(rel.getCategoryId()))
                .collect(Collectors.groupingBy(
                        OrderCommentsCategoriesRelPO::getCommentId,
                        Collectors.mapping(
                                rel -> {
                                    OrderCommentsProblemCategoriesDTO category = categoriesMap.get(rel.getCategoryId());
                                    return category.getParentName() + "/" + category.getChildName();
                                },
                                Collectors.toList()
                        )
                ));

        // 设置问题分类到响应对象
        orderCommentsList.forEach(e -> e.setProblemCategories(problemCategoriesMap.getOrDefault(e.getOrderCommentsId(), Collections.emptyList())));

    }
}
