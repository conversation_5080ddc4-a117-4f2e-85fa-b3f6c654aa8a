package com.yhd.fa.marketing.cms.configure;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version Id: StringTrimModule.java, v0.1 2022/12/6 14:01 yehuasheng Exp $
 */
@Component
public class StringTrimModule {

    @Resource
    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addDeserializer(String.class, new JsonDeserializer<String>() {
            @Override
            public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                if (StringUtils.isNotBlank(p.getValueAsString())) {
                    return p.getValueAsString().trim();
                }
                return p.getValueAsString();
            }
        });
        objectMapper.registerModule(simpleModule);
    }
}
