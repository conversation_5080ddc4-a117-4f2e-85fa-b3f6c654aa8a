package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderInvoiceStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceStatusEnum.java, v0.1 2023/2/23 12:12 yehuasheng Exp $
 */
@Getter
public enum OrderInvoiceStatusEnum {
    INVOICING(OrderInvoiceStatusConstant.INVOICING, "开票中"),
    INVOICED(OrderInvoiceStatusConstant.INVOICED, "已开票"),
    SHIPPED(OrderInvoiceStatusConstant.SHIPPED, "已发货"),
    CANCEL(OrderInvoiceStatusConstant.CANCEL, "已取消"),
    ;

    private final String orderInvoiceStatus;
    private final String orderInvoiceStatusName;

    OrderInvoiceStatusEnum(String orderInvoiceStatus, String orderInvoiceStatusName) {
        this.orderInvoiceStatus = orderInvoiceStatus;
        this.orderInvoiceStatusName = orderInvoiceStatusName;
    }
}
