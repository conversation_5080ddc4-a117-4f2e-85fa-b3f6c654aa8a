package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.util.ObjectUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.*;
import com.yhd.fa.marketing.cms.dao.OrderClickHouseDAO;
import com.yhd.fa.marketing.cms.pojo.po.OrderClickHousePO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderListResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.SqlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderListClickHouseLogic.java, v 0.1 2025/3/20 10:13 JiangYuHong Exp $
 */
@Component
public class OrderListClickHouseLogic {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderListClickHouseLogic.class.getName());


    @Resource
    private OrderClickHouseDAO orderClickHouseDAO;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 查询订单列表
     *
     * @param requestVO 请求参数
     * @return 订单列表
     */
    public List<OrderListResponseVO> selectListDeepForClickHouse(OrderListRequestVO requestVO, String lastSql) {

        logger.info("start select order list for click house");

        // 查询 ClickHouse 数据
        MPJLambdaWrapper<OrderClickHousePO> queryWrapper = setQueryWrapper(requestVO);
        queryWrapper.last(lastSql);
        List<OrderListResponseVO> orderClickHousePOS = orderClickHouseDAO.selectJoinList(OrderListResponseVO.class, queryWrapper);

        logger.info("select order list for click house end.");

        return orderClickHousePOS;
    }


    /**
     * 设置查询条件
     *
     * @param orderListRequestVO 订单列表查询参数
     * @return MPJLambdaWrapper<OrderPO>
     */
    private MPJLambdaWrapper<OrderClickHousePO> setQueryWrapper(OrderListRequestVO orderListRequestVO) {
        logger.info("set query wrapper.");

        // 设置查询的字段
        MPJLambdaWrapper<OrderClickHousePO> queryWrapper = new MPJLambdaWrapper<OrderClickHousePO>()
                .selectAs(OrderClickHousePO::getId, OrderListResponseVO::getOrderId)
                .selectAs(OrderClickHousePO::getOrderNumber, OrderListResponseVO::getOrderNumber)
                .selectAs(OrderClickHousePO::getOrderStatus, OrderListResponseVO::getOrderStatus)
                .selectAs(OrderClickHousePO::getCancelStatus, OrderListResponseVO::getCancelStatus)
                .selectAs(OrderClickHousePO::getDeleteStatus, OrderListResponseVO::getDeleteStatus)
                .selectAs(OrderClickHousePO::getCompanyCode, OrderListResponseVO::getCompanyCode)
                .selectAs(OrderClickHousePO::getCompanyName, OrderListResponseVO::getCompanyName)
                .selectAs(OrderClickHousePO::getUserCode, OrderListResponseVO::getUserCode)
                .selectAs(OrderClickHousePO::getSettlementType, OrderListResponseVO::getSettlementType)
                .selectAs(OrderClickHousePO::getPaymentStatus, OrderListResponseVO::getPaymentStatus)
                .selectAs(OrderClickHousePO::getPaymentType, OrderListResponseVO::getPaymentType)
                .selectAs(OrderClickHousePO::getTotalPrice, OrderListResponseVO::getTotalPrice)
                .selectAs(OrderClickHousePO::getPayablePrice, OrderListResponseVO::getPayablePrice)
                .selectAs(OrderClickHousePO::getSynchronizeStatus, OrderListResponseVO::getSynchronizeStatus)
                .selectAs(OrderClickHousePO::getSynchronizeMessage, OrderListResponseVO::getSynchronizeMessage)
                .selectAs(OrderClickHousePO::getOrderChannel, OrderListResponseVO::getOrderChannel)
                .selectAs(OrderClickHousePO::getOrderSource, OrderListResponseVO::getOrderSource)
                .selectAs(OrderClickHousePO::getCreatedDate, OrderListResponseVO::getCreatedDate)
                .selectAs(OrderClickHousePO::getTradeNo, OrderListResponseVO::getTradeNo)
                .selectAs(OrderCommentsPO::getRemark, OrderCommentsResponseVO::getRemark);

        // 设置排序
        queryWrapper.orderByDesc(OrderClickHousePO::getCreatedDate);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderClickHousePO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        // 判断是否有传时间
        setTimeQueryWrapper(orderListRequestVO, queryWrapper);

        // 判断查询是否订单号
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderListRequestVO.getOrderNumber(), queryWrapper, OrderClickHousePO::getOrderNumber);
        // 企业名称
        SqlUtil.setLambdaWrapperLikeOrInSearch(orderListRequestVO.getCompanyName(), queryWrapper, OrderClickHousePO::getCompanyName);
        // 订单状态
        setOrderStatus(orderListRequestVO, queryWrapper);

        // 支付类型
        setPaymentType(orderListRequestVO, queryWrapper);

        // 订单渠道
        setOrderChannel(orderListRequestVO, queryWrapper);

        // 订单来源
        setOrderSource(orderListRequestVO, queryWrapper);

        //订单所属平台
        setPlatformCode(FaDocMarketingCmsConstant.PLATFORM_CODE, queryWrapper);

        return queryWrapper;
    }

    private void setPlatformCode(String platformCode, MPJLambdaWrapper<OrderClickHousePO> queryWrapper) {

        if (StringUtils.isNotBlank(platformCode)) {
            queryWrapper.eq(OrderClickHousePO::getPlatformCode, platformCode);
        }

    }

    /**
     * 设置查询时间
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setTimeQueryWrapper(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderClickHousePO> queryWrapper) {
        logger.info("get order list set time query wrapper.");

        // 开始时间
        if (ObjectUtil.isNotNull(orderListRequestVO.getStartDateTime())) {
            queryWrapper.ge(OrderClickHousePO::getCreatedDate, orderListRequestVO.getStartDateTime());
        }

        // 结束时间
        if (ObjectUtil.isNotNull(orderListRequestVO.getEndDateTime())) {
            queryWrapper.le(OrderClickHousePO::getCreatedDate, orderListRequestVO.getEndDateTime());
        }
    }

    /**
     * 设置查询订单状态
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setOrderStatus(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderClickHousePO> queryWrapper) {
        logger.info("get order list set order status query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getOrderStatus())) {
            if(StringUtils.equals(orderListRequestVO.getOrderStatus(), PaymentStatusConstant.PAYMENT_FAILED)) {
                queryWrapper.eq(OrderClickHousePO::getPaymentStatus, PaymentStatusConstant.PAYMENT_FAILED)
                        .eq(OrderClickHousePO::getSettlementType, SettlementTypeConstant.ONLINE)
                        .eq(OrderClickHousePO::getOrderStatus, OrderStatusConstant.UNPAID);
            } else {
                queryWrapper.eq(OrderClickHousePO::getOrderStatus, orderListRequestVO.getOrderStatus());
            }
        }
    }

    /**
     * 设置查询订单支付类型
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setPaymentType(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderClickHousePO> queryWrapper) {
        logger.info("get order list set payment type query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getPaymentType())) {
            if (StringUtils.equals(orderListRequestVO.getPaymentType(), PaymentTypeConstant.OFFLINE_PAY)) {
                // 线下付款
                queryWrapper.eq(OrderClickHousePO::getSettlementType, SettlementTypeConstant.ONLINE);
                queryWrapper.eq(OrderClickHousePO::getPaymentType, PaymentTypeConstant.BANK_TRANSFER);
            } else if (StringUtils.equals(orderListRequestVO.getPaymentType(), PaymentTypeConstant.MONTHLY)) {
                // 月结
                queryWrapper.eq(OrderClickHousePO::getSettlementType, SettlementTypeConstant.OFFLINE);
            } else {
                queryWrapper.eq(OrderClickHousePO::getSettlementType, SettlementTypeConstant.ONLINE);
                queryWrapper.eq(OrderClickHousePO::getPaymentType, orderListRequestVO.getPaymentType());
            }
        }
    }

    /**
     * 设置查询订单渠道
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setOrderChannel(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderClickHousePO> queryWrapper) {
        logger.info("get order list set order channel query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getOrderChannel())) {
            queryWrapper.eq(OrderClickHousePO::getOrderChannel, orderListRequestVO.getOrderChannel());
        }
    }

    /**
     * 设置查询订单来源
     *
     * @param orderListRequestVO 订单列表参数
     * @param queryWrapper       查询条件
     */
    private void setOrderSource(OrderListRequestVO orderListRequestVO, MPJLambdaWrapper<OrderClickHousePO> queryWrapper) {
        logger.info("get order list set order source query wrapper.");

        if (StringUtils.isNotBlank(orderListRequestVO.getOrderSource())) {
            queryWrapper.eq(OrderClickHousePO::getOrderSource, orderListRequestVO.getOrderSource());
        }
    }
}
