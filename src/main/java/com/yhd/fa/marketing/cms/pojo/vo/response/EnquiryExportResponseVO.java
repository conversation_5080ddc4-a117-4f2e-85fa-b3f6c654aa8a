package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: QuotationExportResponseVO.java, v 0.1 2022/3/25 14:57 JiangYuHong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EnquiryExportResponseVO extends BaseVO {


    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否可以获取成功
     */
    private boolean pass;

    /**
     * 明细
     */
    private List<EnquiryListExportResponseVO> list;
}
