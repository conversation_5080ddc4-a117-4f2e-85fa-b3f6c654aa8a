package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbOrderPageRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.FbQuotationAndOrderDeleteRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderExportResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.FbOrderTotalPriceCountResponseVO;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2024/3/19 8:55
 */
public interface FbOrderApiService {
    /**
     * Fb订单列表数据获取
     *
     * @param fbOrderPageRequestVO
     * @return
     */
    BusinessResponse<PageInfo<FbOrderListResponseVO>> getFbOrderPage(FbOrderPageRequestVO fbOrderPageRequestVO);

    /**
     * Fb订单详情数据获取
     *
     * @param id
     * @return
     */
    BusinessResponse<FbOrderDetailResponseVO> getFbOrderDetail(String id);

    /**
     * Fb订单单据统计金额
     *
     * @param fbOrderPageRequestVO
     * @return
     */

    BusinessResponse<FbOrderTotalPriceCountResponseVO> getFbOrderCount(FbOrderPageRequestVO fbOrderPageRequestVO);

    /**
     * Fb订单删除
     *
     * @param fbOrderDeleteRequestVO
     * @return
     */
    BusinessResponse<Object> deleteOrder(FbQuotationAndOrderDeleteRequestVO fbOrderDeleteRequestVO);

    /**
     * Fb订单列表导出数据
     * @param fbOrderPageRequestVO
     * @return
     */
    BusinessResponse<List<FbOrderExportResponseVO>> getFbOrderExport(FbOrderPageRequestVO fbOrderPageRequestVO);
}
