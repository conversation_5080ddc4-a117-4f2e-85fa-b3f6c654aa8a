package com.yhd.fa.marketing.cms.util;

import cn.afterturn.easypoi.excel.export.styler.AbstractExcelExportStyler;
import cn.afterturn.easypoi.excel.export.styler.IExcelExportStyler;
import org.apache.poi.ss.usermodel.*;

/**
 * <AUTHOR>
 * @version Id: ExcelExportImpl.java, v 0.1 2022/3/25 16:31 JiangYuHong Exp $
 */
public class ExcelExportImpl extends AbstractExcelExportStyler implements IExcelExportStyler {
    public ExcelExportImpl(Workbook workbook) {
        super.createStyles(workbook);
    }

    @Override
    public CellStyle stringSeptailStyle(Workbook workbook, boolean isWarp) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setDataFormat(STRING_FORMAT);
        if (isWarp) {
            style.setWrapText(true);
        }
        return style;
    }

    @Override
    public CellStyle getHeaderStyle(short color) {
        CellStyle titleStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 16);
        titleStyle.setFont(font);
        titleStyle.setAlignment(HorizontalAlignment.LEFT);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return titleStyle;
    }

    @Override
    public CellStyle stringNoneStyle(Workbook workbook, boolean isWarp) {
        CellStyle styles = workbook.createCellStyle();
        styles.setAlignment(HorizontalAlignment.CENTER);
        styles.setVerticalAlignment(VerticalAlignment.CENTER);
        styles.setDataFormat(STRING_FORMAT);
        if (isWarp) {
            styles.setWrapText(true);
        }
        return styles;
    }

    @Override
    public CellStyle getTitleStyle(short color) {
        CellStyle titleStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 16);
        titleStyle.setFont(font);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return titleStyle;
    }
}
