package com.yhd.fa.marketing.cms.schedule;


import cn.hutool.extra.spring.SpringUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.dao.CompanyDAO;
import com.yhd.fa.marketing.cms.dao.SaleCompanyRelationDAO;
import com.yhd.fa.marketing.cms.mapper.SaleCompanyRelationMapper;
import com.yhd.fa.marketing.cms.pojo.dto.SaleCompanyRelationDTO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.util.SyncSaleCompanyUtil;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: SyncSaleCompanyToMysqlQuotationDatabaseSchedule.java, v 0.1 2025/5/13 15:32 JiangYuHong Exp $
 */
@Component
public class SyncSaleCompanyToMysqlQuotationDatabaseSchedule {

    private static final Logger logger = LogUtils.getLogger(SyncSaleCompanyToMysqlQuotationDatabaseSchedule.class.getName());

    @Resource
    private CompanyDAO companyDAO;

    @Resource
    private SaleCompanyRelationDAO saleCompanyRelationDAO;


    private void truncateMysqlQuotationDatabase() {
        saleCompanyRelationDAO.truncateQuotationData();
    }

    @XxlJob(value = "syncSaleCompanyToMysqlQuotationDatabase")
    public void syncSaleCompanyToMysqlQuotationDatabase() {
        logger.info("开始同步员工对应企业信息到Mysql报价单数据库");

        try {
            // 清空表
            truncateMysqlQuotationDatabase();

            // 查询MySQL数据
            int batchNumber = 1;
            int batchCount = 0; // 批次计数器

            while (true) {
                List<SaleCompanyRelationDTO> list = SyncSaleCompanyUtil.selectSaleCompanyInfoList(batchNumber, companyDAO);
                if (list.isEmpty()) {
                    break;
                }

                // 组装数据
                List<SaleCompanyRelationPO> saleCompanyRelationPOList = list.stream().map(SyncSaleCompanyUtil::applyMysql).collect(Collectors.toList());

                SpringUtil.getBean(SaleCompanyRelationMapper.class).saveAllToQuotationDatabase(saleCompanyRelationPOList);

                batchNumber++;
                batchCount++;

                // 每处理10批数据后暂停10秒
                if (batchCount % 10 == 0) {
                    logger.info("已处理{}批数据，暂停10秒让异步任务执行完成", batchCount);
                    Thread.sleep(10000);
                }

            }

        } catch (Exception e) {
            Thread.currentThread().interrupt();
            logger.error("同步到Mysql报价单数据库失败，错误信息:{}", e.toString());
        }
    }
}
