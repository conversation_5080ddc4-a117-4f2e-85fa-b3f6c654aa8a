package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.QuotationListMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationDetailListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ModelSplitCodeResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProductInfoResponseVO;
import com.yhd.fa.marketing.cms.service.sao.ProductBasisService;
import com.yhd.fa.marketing.cms.service.sao.ProductCenterCmsService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version Id: GetApproveQuotationDetailLogic.java, v0.1 2022/12/14 14:46 yehuasheng Exp $
 */
@Component
public class GetApproveQuotationDetailLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetApproveQuotationDetailLogic.class.getName());

    /**
     * 报价单列表mapper
     */
    @Resource
    private QuotationListMapper quotationListMapper;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 型号基础服务
     */
    @Resource
    private ProductBasisService productBasisService;

    /**
     * 产品中心服务
     */
    @Resource
    private ProductCenterCmsService productCenterCmsService;

    /**
     * 获取审核报价单详情
     *
     * @param quotationInfo 报价单详情
     * @return BusinessResponse<ApproveQuotationDetailResponseVO>
     */
    public BusinessResponse<ApproveQuotationDetailResponseVO> exec(QuotationPO quotationInfo) {
        logger.info("start exec get approve quotation detail logic.");

        // 获取报价单的明细
        List<QuotationListPO> quotationDetailList = quotationListMapper.list(
                new MPJLambdaWrapper<QuotationListPO>()
                        .select(QuotationListPO::getId,
                                QuotationListPO::getQuotationStatus,
                                QuotationListPO::getModel,
                                QuotationListPO::getCustomerModel,
                                QuotationListPO::getCustomerProductName,
                                QuotationListPO::getCustomerMaterialCode,
                                QuotationListPO::getRemark,
                                QuotationListPO::getErrorMessage,
                                QuotationListPO::getGoodsStatus,
                                QuotationListPO::getStandardStatus,
                                QuotationListPO::getFileUrl,
                                QuotationListPO::getOriginalPrice,
                                QuotationListPO::getDiscountPrice,
                                QuotationListPO::getTaxDiscountPrice,
                                QuotationListPO::getTotalPrice,
                                QuotationListPO::getQuantityDiscountRate,
                                QuotationListPO::getTotalDiscountRate,
                                QuotationListPO::getQuantity
                        )
                        .eq(QuotationListPO::getQuotationId, quotationInfo.getId()
                        )
        );
        if (CollUtil.isEmpty(quotationDetailList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_DETAIL_IS_NULL);
        }

        // 组装输出内容
        return BusinessResponseCommon.ok(setApproveQuotationDetailResult(quotationInfo, quotationDetailList));
    }

    /**
     * 设置输出的审核报价单详情内容
     *
     * @param quotationInfo       报价单详情
     * @param quotationDetailList 报价单明细
     * @return ApproveQuotationDetailResponseVO
     */
    private ApproveQuotationDetailResponseVO setApproveQuotationDetailResult(QuotationPO quotationInfo, List<QuotationListPO> quotationDetailList) {
        logger.info("set approve quotation detail result.");

        // 初始化返回参数
        ApproveQuotationDetailResponseVO approveQuotationDetailResponseVO = ApproveQuotationDetailResponseVO.builder()
                .quotationId(quotationInfo.getId())
                .quotationStatus(quotationInfo.getQuotationStatus())
                .quotationNumber(quotationInfo.getQuotationNumber())
                .companyCode(quotationInfo.getCompanyCode())
                .companyName(quotationInfo.getCompanyName())
                .userCode(quotationInfo.getUserCode())
                .purchaseUserCode(quotationInfo.getPurchaseUserCode())
                .createdDate((quotationInfo.getCreatedDate()))
                .build();

        // 设置用户的名称和跟单
        setApproveQuotationUserInfo(approveQuotationDetailResponseVO);

        // 设置报价单明细
        approveQuotationDetailResponseVO.setQuotationDetailList(
                quotationDetailList
                        .stream()
                        .map(quotationListPO ->
                                ApproveQuotationDetailListResponseVO.builder()
                                        .quotationDetailId(quotationListPO.getId())
                                        .quotationDetailStatus(quotationListPO.getQuotationStatus())
                                        .goodsStatus(quotationListPO.getGoodsStatus())
                                        .errorMessage(quotationListPO.getErrorMessage())
                                        .standardStatus(quotationListPO.getStandardStatus())
                                        .customerModel(quotationListPO.getCustomerModel())
                                        .model(quotationListPO.getModel())
                                        .quantity(quotationListPO.getQuantity())
                                        .customerMaterialCode(quotationListPO.getCustomerMaterialCode())
                                        .customerProductName(quotationListPO.getCustomerProductName())
                                        .remark(quotationListPO.getRemark())
                                        .fileUrl(quotationListPO.getFileUrl())
                                        .price(quotationListPO.getOriginalPrice())
                                        .discountPrice(quotationListPO.getDiscountPrice())
                                        .taxDiscountPrice(quotationListPO.getTaxDiscountPrice())
                                        .totalPrice(quotationListPO.getTotalPrice())
                                        .delivery(quotationListPO.getDelivery())
                                        .quantityDiscountRate(quotationListPO.getQuantityDiscountRate())
                                        .totalDiscountRate(quotationListPO.getTotalDiscountRate())
                                        .build()
                        )
                        .collect(Collectors.toList())
        );

        // 设置型号拆分代码信息
        setModelSplitCodeInfoMap(approveQuotationDetailResponseVO.getQuotationDetailList());

        return approveQuotationDetailResponseVO;
    }

    /**
     * 设置用户的信息
     *
     * @param approveQuotationDetail 审核报价单详情
     */
    private void setApproveQuotationUserInfo(ApproveQuotationDetailResponseVO approveQuotationDetail) {
        logger.info("set approve quotation detail user info.");

        // 获取用户的信息
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = Stream.of(UserCodeAndCompanyCodeDTO
                .builder()
                .companyCode(approveQuotationDetail.getCompanyCode())
                .purchaseUserCode(approveQuotationDetail.getPurchaseUserCode())
                .userCode(approveQuotationDetail.getUserCode())
                .build()).collect(Collectors.toList());


        // 获取用户的信息
        Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userInfoMap = userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);

        Optional.ofNullable(userInfoMap.get(approveQuotationDetail.getUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
            // 设置用户的跟单
            Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).ifPresent(merchandiserResponseVO -> {
                approveQuotationDetail.setMerchandiserContact(merchandiserResponseVO.getMobile());
                approveQuotationDetail.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
            });

            // 设置用户名称
            Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getUserInfo()).ifPresent(baseUserInfoResponseVO -> approveQuotationDetail.setUserName(baseUserInfoResponseVO.getUserName()));
        });

        Optional.ofNullable(userInfoMap.get(approveQuotationDetail.getPurchaseUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> approveQuotationDetail.setPurchaseUserName(userAndCompanyAndMerchandiserResponseVO.getUserInfo().getUserName()));
    }

    /**
     * 设置型号拆分代码信息
     *
     * @param approveQuotationDetailList 待评审报价单明细
     */
    private void setModelSplitCodeInfoMap(List<ApproveQuotationDetailListResponseVO> approveQuotationDetailList) {
        logger.info("set model split code info.");

        // 提取报价中的型号
        List<String> model = approveQuotationDetailList
                .stream()
                .filter(approveQuotationDetailListResponseVO
                        -> StringUtils.equals(approveQuotationDetailListResponseVO.getQuotationDetailStatus(), QuotationStatusConstant.QUOTATION))
                .map(ApproveQuotationDetailListResponseVO::getCustomerModel)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(model)) {
            // 获取产品信息
            Map<String, ModelSplitCodeResponseVO> modelSplitCodeMap = productBasisService.getModelSplitCodeMap(model);

            // 提取代码
            List<String> code = modelSplitCodeMap.values().stream().filter(ModelSplitCodeResponseVO::isHasCode).map(ModelSplitCodeResponseVO::getCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(code)) {
                // 请求产品中心
                Map<String, ProductInfoResponseVO> productInfo = productCenterCmsService.getProductInfo(code);

                // 设置待审核报价单明细的类别
                approveQuotationDetailList
                        .stream()
                        .filter(approveQuotationDetailListResponseVO
                                -> StringUtils.equals(approveQuotationDetailListResponseVO.getQuotationDetailStatus(), QuotationStatusConstant.QUOTATION))
                        .forEach(approveQuotationDetailListResponseVO -> Optional.ofNullable(modelSplitCodeMap.get(approveQuotationDetailListResponseVO.getCustomerModel())).filter(ModelSplitCodeResponseVO::isHasCode).ifPresent(modelSplitCodeResponseVO -> {
                            // 设置代码
                            approveQuotationDetailListResponseVO.setCode(modelSplitCodeResponseVO.getCode());
                            // 设置类别
                            approveQuotationDetailListResponseVO.setCategoryCode(modelSplitCodeResponseVO.getTypeCode());
                            // 设置类别名称
                            approveQuotationDetailListResponseVO.setCategoryName(modelSplitCodeResponseVO.getProductName());
                            // 如果有产品信息
                            Optional.ofNullable(productInfo.get(modelSplitCodeResponseVO.getCode())).ifPresent(productInfoResponseVO -> {
                                // 设置一级分类
                                approveQuotationDetailListResponseVO.setTypeCode(productInfoResponseVO.getTypeCode());
                                approveQuotationDetailListResponseVO.setTypeName(productInfoResponseVO.getTypeCodeName());
                                // 设置二级分类
                                approveQuotationDetailListResponseVO.setCatCode(productInfoResponseVO.getCatCode());
                                approveQuotationDetailListResponseVO.setCatName(productInfoResponseVO.getCatCodeName());
                                // 设置系列编码
                                approveQuotationDetailListResponseVO.setGoodsCode(productInfoResponseVO.getGoodsCode());
                                approveQuotationDetailListResponseVO.setGoodsName(productInfoResponseVO.getGoodsCodeName());
                            });
                        }));
            }
        }
    }
}
