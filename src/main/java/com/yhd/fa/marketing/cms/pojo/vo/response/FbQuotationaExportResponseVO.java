package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "报价单列表导出数据")
public class FbQuotationaExportResponseVO extends BaseVO {
    @Schema
    private Integer sort;

    @Schema(description = "报价单id")
    private String id;

    @Schema(description = "报价单号")
    private String quotationNumber;

    @Schema(description = "报价单等级（ordCo普通企业，certCo认证企业）")
    private String quotationLevel;

    @Schema(description = "客户报价单号")
    private String customerQuotationNumber;

    @Schema(description = "企业编码")
    private String companyCode;

    @Schema(description = "企业名称")
    private String companyName;

    @Schema(description = "报价状态")
    private String quotationStatus;

    @Schema(description = "创建人用户编码")
    private String userCode;

    @Schema(description = "采购用户编码")
    private String purchaseUserCode;

    @Schema(description = "报价完成时间")
    private String quotationCompletionDate;

    @Schema(description = "erp报价人员")
    private String quotationUserCode;

    @Schema(description = "是否转单")
    private String transferOrder;

    @Schema(description = "制单日期")
    private String createdDate;

    @Schema(description = "公司归属")
    private String ownershipCompany;

    @Schema(description = "跟单员")
    private String merchandiser;

    @Schema(description = "业务员")
    private String operator;


    @Schema(description = "销售部门")
    private String unitName;

    @Schema(description = "yhd型号")
    private String model;

    @Schema(description = "客户型号")
    private String customerModel;

    @Schema(description = "产品名称")
    private String productName;


    @Schema(description = "原单价")
    private String originalPrice;

    @Schema(description = "交期")
    private String delivery;

    @Schema(description = "数量")
    private String quantity;

    @Schema(description = "总折扣")
    private String totalDiscountRate;

    @Schema(description = "未税折扣单价")
    private String discountPrice;

    @Schema(description = "含税折扣单价")
    private String taxDiscountPrice;

    @Schema(description = "总价")
    private String totalPrice;

    @Schema(description = "客户备注")
    private String remark;

    @Schema(description = "yhd备注")
    private String examineRemark;

    @Schema(description = "附件")
    private String fileUrl;

    @Schema(description = "图纸")
    private String imageUrl;

    @Schema(description = "报价方式")
    private String quotationType;

    @Schema(description = "报价人名字+工号")
    private String createdBy;


    @Schema(description = "报价人")
    private String quotationUserType;


    @Schema(description = "材料")
    private String rawMaterial;

    @Schema(description = "表面处理")
    private String surfaceTreatment;

    @Schema(description = "热处理")
    private String heatTreatment;

    @Schema(description = "利润系数")
    private String profitCoefficient;

    @Schema(description = "类别")
    private String categoryCode;

    @Schema(description = "物料类型")
    private String materialType;

}
