package com.yhd.fa.marketing.cms.constant;

/**
 * <AUTHOR>
 * @version Id: PaymentTypeConstant.java, v0.1 2023/2/17 9:14 yehuasheng Exp $
 */
public class PaymentTypeConstant {
    /**
     * 未支付
     */
    public static final String UNKNOWN = "unknown";
    /**
     * 支付宝支付
     */
    public static final String ALI_PAY = "aliPay";
    /**
     * 微信支付
     */
    public static final String WECHAT_PAY = "weChatPay";
    /**
     * 企业网银
     */
    public static final String UNION_PAY = "unionPay";
    /**
     * 线下支付
     */
    public static final String OFFLINE_PAY = "offlinePay";
    /**
     * 信用月结
     */
    public static final String MONTHLY = "monthlyKnot";
    /**
     * 银行转账
     */
    public static final String BANK_TRANSFER = "bankTransfer";
    /**
     * 月结30
     */
    public static final String MONTHLY_KNOT_30 = "monthlyKnot30";
    /**
     * 月结60
     */
    public static final String MONTHLY_KNOT_60 = "monthlyKnot60";
    /**
     * 月结90
     */
    public static final String MONTHLY_KNOT_90 = "monthlyKnot90";

    private PaymentTypeConstant() {
    }
}
