package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.listener.PageReadListener;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.pojo.dto.QuotationExcelTemplateDTO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CheckApproveQuotationParameterModelListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.CheckApproveQuotationParameterRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ExcelFileAnalysisResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: ExcelFileAnalysisLogic.java, v0.1 2023/1/3 17:53 yehuasheng Exp $
 */
@Component
public class ExcelFileAnalysisLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(ExcelFileAnalysisLogic.class.getName());

    /**
     * 坚持客户型号逻辑
     */
    @Resource
    private CheckCustomerModelLogic checkCustomerModelLogic;

    /**
     * 解析excel文件
     *
     * @param excelFile excel文件
     * @param userCode  用户编码
     * @return BusinessResponse<List < ExcelFileAnalysisResponseVO>>
     */
    public BusinessResponse<List<ExcelFileAnalysisResponseVO>> exec(MultipartFile excelFile, String userCode) {
        logger.info("start analysis excel logic.");

        // 定义初始数据
        List<ExcelFileAnalysisResponseVO> excelFileAnalysisResponseVOS = new ArrayList<>();

        try {
            // 这里每次会读取100条数据 然后返回过来 直接调用使用数据就行
            EasyExcelFactory.read(excelFile.getInputStream(),
                            QuotationExcelTemplateDTO.class,
                            new PageReadListener<QuotationExcelTemplateDTO>(dataList ->
                                    dataList.forEach(quotationExcelTemplateDTO -> excelFileAnalysisResponseVOS.add(ExcelFileAnalysisResponseVO.builder()
                                            .customerProductName(quotationExcelTemplateDTO.getCustomerProductName())
                                            .materialCode(quotationExcelTemplateDTO.getCustomerMaterialCode())
                                            .model(quotationExcelTemplateDTO.getCustomerModel())
                                            .quantity(quotationExcelTemplateDTO.getQuantity())
                                            .remark(quotationExcelTemplateDTO.getRemark())
                                            .customerModel(quotationExcelTemplateDTO.getCustomerModel())
                                            .build()))))
                    .sheet().doRead();
        } catch (IOException ioException) {
            logger.error("analysis excel fail. ioErrorMsg:{}", ioException.getMessage());
        } catch (Exception e) {
            logger.error("analysis excel fail. errorMsg:{}", e.getMessage());
        }

        // 调用请求报价逻辑
        return getQuotationPrice(userCode, excelFileAnalysisResponseVOS);
    }

    /**
     * 调用请求报价逻辑
     *
     * @param userCode                     用户编码
     * @param excelFileAnalysisResponseVOS excel内容
     * @return BusinessResponse<List < ExcelFileAnalysisResponseVO>>
     */
    private BusinessResponse<List<ExcelFileAnalysisResponseVO>> getQuotationPrice(String userCode, List<ExcelFileAnalysisResponseVO> excelFileAnalysisResponseVOS) {
        logger.info("excel file analysis get quotation price.");

        // 当excel内容不为空的时候进行询价操作
        if (CollUtil.isNotEmpty(excelFileAnalysisResponseVOS)) {
            // 提取请求价格与交期的参数
            List<CheckApproveQuotationParameterModelListRequestVO> checkApproveQuotationParameterModelList = excelFileAnalysisResponseVOS
                    .stream()
                    .map(excelFileAnalysisResponseVO ->
                            CheckApproveQuotationParameterModelListRequestVO
                                    .builder()
                                    .model(excelFileAnalysisResponseVO.getCustomerModel())
                                    .quantity(excelFileAnalysisResponseVO.getQuantity())
                                    .build())
                    .collect(Collectors.toList());

            // 调用检查客户型号逻辑获取价格和交期
            BusinessResponse<List<QuotationResponseVO>> quotationBusinessResponse = checkCustomerModelLogic.exec(CheckApproveQuotationParameterRequestVO.builder().userCode(userCode).list(checkApproveQuotationParameterModelList).build());
            if (!quotationBusinessResponse.success()) {
                return BusinessResponseCommon.fail(quotationBusinessResponse.getRt_code(), quotationBusinessResponse.getRt_msg());
            }

            // 组装输出内容
            Map<String, QuotationResponseVO> quotationResultMap = quotationBusinessResponse.getData().stream().collect(Collectors.toMap(QuotationResponseVO::getSort, Function.identity(), (a, b) -> a));
            excelFileAnalysisResponseVOS.forEach(excelFileAnalysisResponseVO -> {
                // 组合排序
                String sort = excelFileAnalysisResponseVO.getQuantity() + CommonConstant.DASH + excelFileAnalysisResponseVOS.indexOf(excelFileAnalysisResponseVO);
                // 判断集合是否存在该元素
                if (quotationResultMap.containsKey(sort)) {
                    excelFileAnalysisResponseVO.setQuotationResult(quotationResultMap.get(sort));
                }
            });
        }

        // 返回结果
        return BusinessResponse.ok(excelFileAnalysisResponseVOS);
    }
}
