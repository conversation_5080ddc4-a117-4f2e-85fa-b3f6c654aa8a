package com.yhd.fa.marketing.cms.util;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yhd.common.pojo.po.BaseEntity;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.constant.OrderConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: BaseUtil.java, v 0.1 2022/11/23 16:47 JiangYuHong Exp $
 */
public class BaseUtil {

    private BaseUtil() {
    }


    /**
     * 创建时公用参数赋值
     */
    public static <T extends BaseEntity> void setCreatedParams(T t) {
        t.setId(IdUtil.simpleUUID());
        if (ObjectUtils.isNotEmpty(SecurityUtil.getEmployeeInfo())) {
            t.setCreatedBy(SecurityUtil.getAccountNoAndUserName());
            t.setUpdatedBy(SecurityUtil.getAccountNoAndUserName());
        } else {
            t.setCreatedBy(OrderConstant.CREATE_BY);
            t.setUpdatedBy(OrderConstant.CREATE_BY);
        }
        t.setCreatedDate(LocalDateTime.now());
        t.setUpdatedDate(LocalDateTime.now());
    }


    /**
     * 更新时公用参数赋值
     */
    public static <T extends BaseEntity> void setUpdateParams(T t) {
        t.setUpdatedBy(SecurityUtil.getAccountNoAndUserName());
        t.setUpdatedDate(LocalDateTime.now());
    }

    /**
     * 设置LambdaUpdateWrapper更新时的公用参数
     *
     * @param wrapper  wrapper
     * @param updateBy 指定更新人(默认取当前登录用户)
     * @param <T>      PO
     */
    public static <T extends BaseEntity> void setLambdaUpdateWrapperUpdateParams(LambdaUpdateWrapper<T> wrapper, String updateBy) {
        //设置更新人
        if (StringUtils.isBlank(updateBy)) {
            wrapper.set(T::getUpdatedBy, SecurityUtil.getAccountNoAndUserName());
        } else {
            wrapper.set(T::getUpdatedBy, updateBy);
        }
        //设置更新时间
        wrapper.set(T::getUpdatedDate, LocalDateTime.now());
    }

    /**
     * 将域添加到图像 Urls
     *
     * @param domain      域名
     * @param inputString 相对路径
     * @return String
     */
    public static String addDomainToImageUrls(String domain, String inputString) {

        if (StringUtils.isBlank(inputString)) return "";

        StringBuilder outputBuilder = new StringBuilder();

        String[] paths = inputString.split(",");
        for (String path : paths) {
            // 去除路径前后的空格
            String trimmedPath = path.trim();

            //判断相对路径是否存在'/'
            if (!trimmedPath.startsWith(CommonConstant.SLASH)) {
                trimmedPath = CommonConstant.SLASH + trimmedPath;
            }

            // 添加域名到路径前面
            String imageUrl = domain + trimmedPath;

            // 将处理后的路径添加到输出中
            outputBuilder.append(imageUrl).append(",");
        }

        // 去除最后一个逗号
        if (outputBuilder.length() > 0) {
            outputBuilder.deleteCharAt(outputBuilder.length() - 1);
        }

        return outputBuilder.toString();
    }

}
