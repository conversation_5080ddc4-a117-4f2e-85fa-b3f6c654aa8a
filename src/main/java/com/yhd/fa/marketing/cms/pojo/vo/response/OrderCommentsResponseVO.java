package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsResponseVO.java, v0.1 2023/2/24 17:36 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderCommentsResponseVO extends BaseVO {
    /**
     * 订单评论id
     */
    @Schema(description = "订单评论id", example = "2f0d944903d34f9d854acd4a8bb8824f")
    private String orderCommentsId;

    /**
     * 订单号id
     */
    @Schema(description = "订单号id", example = "1348969b8947455995f0fbe5586cbef5")
    private String orderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "YI202212030829479779T5GL")
    private String orderNumber;

    /**
     * 评论的用户编码
     */
    @Schema(description = "评论的用户编码", example = "139779")
    private String userCode;

    /**
     * 评论的用户名
     */
    @Schema(description = "评论的用户名", example = "你猜")
    private String userName;

    /**
     * 评价用户手机号
     */
    @Schema(description = "评价用户手机号", example = "139779")
    private String phone;

    /**
     * 评价用户邮箱
     */
    @Schema(description = "评价用户邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "HA5814973528")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2020-01-01 10:10:10")
    private LocalDateTime createdDate;

    /**
     * 产品描述相符度
     */
    @Schema(description = "产品描述相符度", example = "1")
    private int productDescRating;

    /**
     * 人员服务态度
     */
    @Schema(description = "人员服务态度", example = "1")
    private int personnelServiceRating;

    /**
     * 产品交付时效
     */
    @Schema(description = "产品交付时效", example = "1")
    private int productDeliveryRating;

    /**
     * 是否已回复 true已回复 false未回复
     */
    @Schema(description = "是否已回复 true已回复 false未回复", example = "false")
    private String replied;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "已生成工单")
    private String remark;

    /**
     * 问题分类
     */
    @Schema(description = "问题分类", example = "质量问题")
    private List<String> problemCategories;

    /**
     * 回访记录
     */
    @Schema(description = "回访记录", example = "已回访")
    private String visitRecords;

}
