package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @version Id: OrderCommentsProblemCategoriesPO.java, v 0.1 2025/5/16 11:49 JiangYuHong Exp $
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("fa_order_comments_problem_categories")
public class OrderCommentsProblemCategoriesPO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private Integer parentId;

}