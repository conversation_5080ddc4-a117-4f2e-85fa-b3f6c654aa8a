package com.yhd.fa.marketing.cms.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version Id: RegexUtils.java, v 0.1 2022/11/22 11:16 JiangYuHong Exp $
 */
public class RegexUtils {
    private RegexUtils() {
    }


    /**
     * 驼峰to下换线格式
     *
     * @param str         需要转换的字符串
     * @param toUpperCase 是否要大写
     * @return String
     */
    public static String humpToLowerLine(String str, boolean toUpperCase, boolean number) {
        Pattern compile = Pattern.compile("[A-Z]");
        Matcher matcher = compile.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);

        String s = toUpperCase ? sb.toString().toUpperCase() : sb.toString();
        if (number) {
            return numberToLowerLine(s);
        } else {
            return s;
        }
    }


    public static String numberToLowerLine(String str) {
        Pattern compile = Pattern.compile("\\d");
        Matcher matcher = compile.matcher(str);
        StringBuffer sb = new StringBuffer();
        if (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);

        return sb.toString();
    }
}
