package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_collection_log")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderCollectionLogPO extends BaseEntity {
    /**
     * 付款记录id
     */
    private String orderPayDetailId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 支付金额
     */
    private BigDecimal paymentPrice;

    /**
     * aliPay支付宝、weChatPay微信、unionPay银联、bankTransfer银行转账
     */
    private String paymentType;

    /**
     * 流水号
     */
    private String serialNumber;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 支付时间
     */
    private LocalDateTime paymentDate;

    /**
     * appid
     */
    private String appId;

    /**
     * 是否已同步给erp，success成功、fail失败、notRequired不需要、wait等待
     */
    private String downErpStatus;

    /**
     * 同步次数，最多不能超过3次
     */
    private int synchronizationsNumber;

    /**
     * 同步的信息
     */
    private String synchronizationsMessage;
}

