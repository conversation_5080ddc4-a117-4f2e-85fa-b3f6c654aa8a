package com.yhd.fa.marketing.cms.web.order;

import com.alibaba.nacos.common.http.param.MediaType;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.CouponOrderCountRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderCountResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CouponOrderFinishCountResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderMarketingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: OrderMarketingController.java, v 0.1 2024/8/8 9:21 JiangYuHong Exp $
 */
@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
@Tag(name = "订单优惠券接口", description = "订单营销相关接口")
public class OrderMarketingController {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderMarketingController.class.getName());

    @Resource
    private OrderMarketingService orderMarketingService;

    /**
     * 优惠券订单完成
     * @param couponId 优惠券id
     * @return BusinessResponse
     */
    @Operation(summary = "优惠券订单完成", parameters = {@Parameter(name = "couponId", description = "优惠券id", example = "['1ca7a1c221c947b5bc4489dfd9097913']", required = true)})
    @GetMapping(value = UriConstant.MARKETING_COUPON_ORDER_FINISH)
    public BusinessResponse<CouponOrderFinishCountResponseVO> couponOrderFinish(@RequestParam List<String> couponId) {

        logger.info("start request coupon finish api. parameter:{}", couponId);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<CouponOrderFinishCountResponseVO> businessResponse = orderMarketingService.couponOrderFinish(couponId);
        logger.info("get coupon finish api response:{}", businessResponse);
        logger.info("get coupon finish api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 优惠券订单统计
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @Operation(summary = "优惠券订单统计")
    @PostMapping(value = UriConstant.MARKETING_COUPON_ORDER_COUNT)
    public BusinessResponse<List<CouponOrderCountResponseVO>> couponOrderCount(@RequestBody @Validated CouponOrderCountRequestVO requestVO) {

        logger.info("start request coupon order count api.");

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<List<CouponOrderCountResponseVO>> businessResponse = orderMarketingService.couponOrderCount(requestVO);
        logger.info("get coupon order count api response:{}", businessResponse);
        logger.info("get coupon order count api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }
}
