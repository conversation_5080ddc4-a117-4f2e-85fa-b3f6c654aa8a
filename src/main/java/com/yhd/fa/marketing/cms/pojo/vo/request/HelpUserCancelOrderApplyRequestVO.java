package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: HelpUserCancelOrderApplyRequestVO.java, v 0.1 2023/3/1 17:08 JiangYuHong Exp $
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HelpUserCancelOrderApplyRequestVO extends BaseVO {

    /**
     * 订单id
     */
    @Schema(description = "订单id", required = true, example = "34b43cf1ef314acdb6dc959b0ac017c4")
    @NotEmpty(message = "订单id不能为空")
    private String orderId;

    /**
     * 取消理由
     */
    @Schema(description = "取消理由", required = true, example = "7天无理由退换")
    @NotEmpty(message = "取消理由不能为空")
    private String reason;

    /**
     * 订单明细id列表
     */
    @Schema(description = "订单明细id列表", required = true, example = "[1,2]")
    @NotEmpty(message = "订单明细id不能为空")
    private List<Integer> orderSortId;

}
