package com.yhd.fa.marketing.cms.web.quotation;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.EnquiryLogListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryLogListResponseVO;
import com.yhd.fa.marketing.cms.service.controller.EnquiryLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogController.java, v0.1 2022/12/8 10:44 yehuasheng Exp $
 */
@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
@Tag(name = "查价单", description = "接口包括查价单列表接口")
public class EnquiryLogController {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(EnquiryLogController.class.getName());

    /**
     * 查价单服务
     */
    @Resource
    private EnquiryLogService enquiryLogService;

    /**
     * 查价单记录
     *
     * @param enquiryLogListRequestVO 查价单参数
     * @return BusinessResponse<PageInfo < EnquiryLogListResponseVO>>
     */
    @Operation(summary = "查价单记录")
    @PostMapping(value = UriConstant.ENQUIRY_LOG_LIST)
    public BusinessResponse<PageInfo<EnquiryLogListResponseVO>> enquiryLogList(@RequestBody EnquiryLogListRequestVO enquiryLogListRequestVO) {
        logger.info("request faDoc marketing get enquiry log list api parameter enquiryLogListRequestVO:{}", enquiryLogListRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求获取报价单列表接口
        BusinessResponse<PageInfo<EnquiryLogListResponseVO>> businessResponse = enquiryLogService.getEnquiryLogList(enquiryLogListRequestVO);
       // logger.info("faDoc marketing get enquiry log list success response result businessResponse:{}", businessResponse);
        logger.info("faDoc marketing get enquiry log list cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    @Operation(summary = "查价单记录添加")
    @PostMapping(value = "/enquiry/log/add")
    public BusinessResponse<String> enquiryLogAdd(@RequestBody List<EnquiryLogPO> enquiryLogPOList) {
        logger.info("request enquiry log add ...");

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        // 请求获取报价单列表接口
        BusinessResponse<String> businessResponse = enquiryLogService.enquiryLogAdd(enquiryLogPOList);
        logger.info("request enquiry log add success:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }
}
