package com.yhd.fa.marketing.cms.service.impl.verification.order;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderAfterSaleRequestVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: GetOrderAfterSaleListVerification.java, v0.1 2023/2/24 14:58 yehuasheng Exp $
 */
@Component
public class GetOrderAfterSaleListVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderAfterSaleListVerification.class.getName());

    /**
     * 检查订单售后参数
     *
     * @param orderAfterSaleRequestVO 请求参数
     * @param <T>                     T
     * @return BusinessResponse
     */
    public <T> BusinessResponse<T> check(OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("start check get order after sale list parameter.");

        // 判断时间是否正确
        if (ObjectUtil.isNotNull(orderAfterSaleRequestVO.getEndDateTime())
                && ObjectUtil.isNotNull(orderAfterSaleRequestVO.getStartDateTime())
                && orderAfterSaleRequestVO.getStartDateTime().isAfter(orderAfterSaleRequestVO.getEndDateTime())) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.START_DATE_IS_AFTER_END_TIME_ERROR);
        }

        return BusinessResponse.ok(null);
    }
}
