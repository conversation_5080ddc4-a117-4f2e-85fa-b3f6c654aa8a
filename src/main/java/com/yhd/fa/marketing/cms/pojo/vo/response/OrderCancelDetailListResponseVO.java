package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: OrderCancelDetailListResponseVO.java, v0.1 2023/2/22 15:35 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderCancelDetailListResponseVO extends BaseVO {
    /**
     * 订单取消明细id
     */
    @Schema(description = "订单取消明细id", example = "3f6f3c26c30c47c0b3090aef233a3475")
    private String orderCancelDetailId;

    /**
     * 订单取消id
     */
    @Schema(description = "订单取消id", example = "5012551d99714d3e93c6f649aa87dd1d")
    private String orderCancelId;

    /**
     * 订单取消序号
     */
    @Schema(description = "订单取消序号", example = "1")
    private int sortId;

    /**
     * 取消数量
     */
    @Schema(description = "取消数量", example = "2")
    private long quantity;

    /**
     * 型号
     */
    @Schema(description = "型号", example = "SAD01-D3-L100")
    private String productModel;

    /**
     * 产品代码
     */
    @Schema(description = "产品代码", example = "SAD01")
    private String productCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "导向轴·直杆型")
    private String productName;

    /**
     * 未税折扣单价
     */
    @Schema(description = "未税折扣单价", example = "8.0")
    private BigDecimal discountPrice;

    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价", example = "9.14")
    private BigDecimal taxDiscountPrice;

    /**
     * 含税小计
     */
    @Schema(description = "含税小计", example = "18.28")
    private BigDecimal totalPrice;

    /**
     * 订单取消详情明细状态
     */
    @Schema(description = "订单取消详情明细状态 canceling 取消中, cancel 已取消, turnDown 驳回", example = "cancel")
    private String orderCancelDetailStatus;

    /**
     * 订单取消详情明细状态名称
     */
    @Schema(description = "订单取消详情明细状态名称", example = "已取消")
    private String orderCancelDetailStatusName;
}
