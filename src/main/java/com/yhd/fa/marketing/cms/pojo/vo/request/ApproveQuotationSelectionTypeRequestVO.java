package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationSelectionTypeRequestVO.java, v0.1 2022/12/22 10:05 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveQuotationSelectionTypeRequestVO extends BaseVO {
    /**
     * 一级分类或者二级分类或者代码的编码
     */
    @Schema(description = "一级分类或者二级分类或者代码的编码 当不填时获取一级分类")
    private String code;

    /**
     * 一级分类或者二级分类或者代码的编码类型
     */
    @Schema(description = "一级分类或者二级分类的编码类型 当不填时获取一级分类 type：获取二级分类，cat：获取代码")
    private String type;
}
