package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleStatusPO.java, v 0.1 2023/10/31 10:39 JiangYuHong Exp $
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("fa_order_after_sale_status")
public class OrderAfterSaleStatusPO extends BaseEntity {

    /**
     * 售后单id
     */
    private String afterSaleId;

    /**
     * 售后单号
     */
    private String afterSaleNumber;

    /**
     * 状态：待审核 pendingAudit；
     * 待寄回商品 pendingReturn；
     * 寄回商品待检测 pendingCheck；
     * 待退款 pendingRefund；
     * 待发货 pendingShip；
     * 待收货 pendingReceive；
     * 待上门维修 pendingRepair；
     * 售后完成 completed；
     * 售后关闭 closed；
     * 用户取消 cancel
     * 退款中 refunding
     *
     */
    private String afterSaleStatus;

    /**
     * 状态节点时间
     */
    private LocalDateTime statusTime;

}