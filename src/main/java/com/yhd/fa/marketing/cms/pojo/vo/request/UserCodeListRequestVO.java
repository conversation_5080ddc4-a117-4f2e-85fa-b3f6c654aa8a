package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: UserCodeListRequestVO.java, v0.1 2023/5/3 14:02 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserCodeListRequestVO extends BaseVO {
    /**
     * 用户编码集合
     */
    @Schema(description = "用户编码集合", example = "[\"12\",\"13\"]", required = true)
    @Size(min = 1, max = 2000, message = "用户编码集合不能少于1或者大于2000个元素")
    private List<String> userCodeList;
}
