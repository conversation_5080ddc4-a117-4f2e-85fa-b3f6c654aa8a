package com.yhd.fa.marketing.cms.aop;

import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ValueInEnumAOP.java, v0.1 2022/8/24 11:32 yehuasheng Exp $
 */
public class ValueInEnumAOP implements ConstraintValidator<ValueInEnum, String> {
    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 枚举对象的值
     */
    private List<String> targetEnumValue;

    /**
     * 初始化
     *
     * @param constraintAnnotation 注解
     */
    @Override
    public void initialize(ValueInEnum constraintAnnotation) {
        // 设置值
        this.required = constraintAnnotation.required();
        this.targetEnumValue = Arrays.asList(constraintAnnotation.matchTarget());
    }

    /**
     * 校验参数
     *
     * @param value                      值
     * @param constraintValidatorContext content
     * @return boolean
     */
    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (Boolean.TRUE.equals(required) && StringUtils.isNotBlank(value)) {
            return targetEnumValue.contains(value);
        }
        return true;
    }
}
