package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleResponseVO.java, v0.1 2023/2/24 11:50 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderAfterSaleResponseVO extends BaseVO {

    /**
     * 订单售后id
     */
    @Schema(description = "订单售后id", example = "34b43cf1ef314acdb6dc959b0ac017c4")
    private String orderAfterSaleId;

    /**
     * 售后单号
     */
    @Schema(description = "售后单号", example = "ACI201811136064")
    private String afterSaleNumber;

    /**
     * 售后单类型 线上：shop；线下：offline
     */
    @Schema(description = "售后单类型 线上：shop；线下：offline", example = "shop")
    private String afterSaleOrderType;

    /**
     * 订单id
     */
    @Schema(description = "订单id", example = "34b43cf1ef314acdb6dc959b0ac017c4")
    private String orderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "MYI0000013941364829")
    private String orderNumber;

    /**
     * 公司编码
     */
    @Schema(description = "公司编码", example = "00000000000000000000000000000000")
    private String companyCode;

    /**
     * 公司名称
     */
    @Schema(description = "公司名称", example = "广东唯康教育科技股份有限公司")
    private String companyName;

    /**
     * 订单售后类型 退货退款 refund；换货 exchange；维修 repair
     */
    @Schema(description = "售后的类型 exchange换货 refund退款", example = "exchange")
    private String afterSaleType;

    /**
     * 订单售后类型名称
     */
    @Schema(description = "订单售后类型名称", example = "换货")
    private String afterSaleTypeName;

    /**
     * 状态：待审核 pendingAudit；
     * 待寄回商品 pendingReturn；
     * 寄回商品待检测 pendingCheck；
     * 待退款 pendingRefund；
     * 待发货 pendingShip；
     * 待收货 pendingReceive；
     * 待上门维修 pendingRepair；
     * 售后完成 completed；
     * 售后关闭 closed；
     * 用户取消 cancel
     * 退款中 refunding
     */
    @Schema(description = "状态：" +
            "待审核 pendingAudit；" +
            "待寄回商品 pendingReturn；" +
            "寄回商品待检测 pendingCheck；" +
            "待退款 pendingRefund；" +
            "待发货 pendingShip；" +
            "待收货 pendingReceive；" +
            "待上门维修 pendingRepair；" +
            "售后完成 completed；" +
            "售后关闭 closed；" +
            "用户取消 cancel", example = "processing")
    private String afterSaleStatus;

    /**
     * 订单售后状态名称
     */
    @Schema(description = "订单售后状态名称", example = "处理中")
    private String afterSaleStatusName;

    /**
     * 售后价格
     */
    @Schema(description = "售后价格", example = "100.00")
    private BigDecimal afterSalePrice;

    /**
     * 扣除积分
     */
    @Schema(description = "扣除积分", example = "100")
    private Integer deductionPoints;

    /**
     * 同步状态 wait等待同步 success成功 fail失败
     */
    @Schema(description = "同步状态 success已同步 fail同步失败 wait等待同步 synchronizing同步中", example = "success")
    private String synchronizationStatus;

    /**
     * 推送给erp的报错信息
     */
    @Schema(description = "推送给erp的报错信息", example = "同步成功")
    private String synchronizationErrorMsg;

    /**
     * 申请人
     */
    @Schema(description = "申请人", example = "01111/林杏金")
    private String applicant;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "01111")
    private String userCode;

    /**
     * 所属地区 DGYHD东莞 SZYHD苏州
     */
    @Schema(description = "所属地区 DGYHD东莞 SZYHD苏州", example = "DGYHD")
    private String territory;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-01-01 10:10:10")
    private LocalDateTime createdDate;

    /**
     * 跟单员
     */
    @Schema(description = "跟单员", example = "你猜")
    private String merchandiser;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "1311111111")
    private String merchandiserContact;

    /**
     * 业务员名称
     */
    @Schema(description = "业务员名称", example = "01111/林杏金")
    private String salesmanName;

    /**
     * 业务员联系方式
     */
    @Schema(description = "业务员联系方式", example = "13111111111")
    private String salesmanContact;

    /**
     * 是否已评论 true 已评论 false 未评论
     */
    private String replyStatus;
}
