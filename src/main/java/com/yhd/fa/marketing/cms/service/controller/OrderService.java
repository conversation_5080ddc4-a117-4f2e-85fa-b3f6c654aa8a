package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.CheckUserOrderResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.LastOrderDateResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInfoResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderListResponseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderService.java, v0.1 2022/12/2 14:24 yehuasheng Exp $
 */
public interface OrderService {
    /**
     * 获取订单列表
     *
     * @param orderListRequestVO 订单列表请求参数
     * @return BusinessResponse<PageInfo < OrderListResponseVO>>
     */
    BusinessResponse<PageInfo<OrderListResponseVO>> getOrderList(OrderListRequestVO orderListRequestVO);

    /**
     * 获取订单详情页信息
     *
     * @param orderId 订单id
     * @return BusinessResponse<OrderInfoResponseVO>
     */
    BusinessResponse<OrderInfoResponseVO> getOrderInfo(String orderId);

    /**
     * 同步创建订单
     *
     * @param orderId 订单id
     * @return BusinessResponse<Void>
     */
    BusinessResponse<Void> synchronizationCreateOrder(String orderId);

    /**
     * 同步订单收款单记录
     *
     * @param orderNumber 订单号
     * @return BusinessResponse<Void>
     */
    BusinessResponse<Void> synchronizationOrderCollection(String orderNumber);

    /**
     * 更新订单支付方式以及流水号
     *
     * @param updateOrderPaymentSerialNumberRequestVO 更新的内容
     * @return BusinessResponse<Void>
     */
    BusinessResponse<Void> updateOrderPaymentSerialNumber(UpdateOrderPaymentSerialNumberRequestVO updateOrderPaymentSerialNumberRequestVO);

    /**
     * 检查用户昨天是否下过订单
     *
     * @param userCodeListRequestVO 用户编码集合
     * @return BusinessResponse<List < CheckUserOrderResponseVO>>
     */
    BusinessResponse<List<CheckUserOrderResponseVO>> checkUserOrder(UserCodeListRequestVO userCodeListRequestVO);

    /**
     * 更新订单砸金蛋状态
     * @param updateOrderEggStatusRequestVO 请求参数
     * @return BusinessResponse<Object>
     */
    BusinessResponse<Object> updateOrderEggStatus(UpdateOrderEggStatusRequestVO updateOrderEggStatusRequestVO);

    /**
     * 根据用户编码集合获取最后下单日期
     * @param lastOrderDateRequestVO 请求参数
     * @return BusinessResponse<List<LastOrderDateResponseVO>>
     */
    BusinessResponse<List<LastOrderDateResponseVO>> getLastOrderDate(LastOrderDateRequestVO lastOrderDateRequestVO);


    /**
     * 更新发票信息公司名称
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    BusinessResponse<Object> updateInvoiceInfoCompanyName(UpdateInvoiceCompanyNameRequestVO requestVO);
}
