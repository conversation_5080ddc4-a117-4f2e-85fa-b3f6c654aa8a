package com.yhd.fa.marketing.cms.service.impl.verification.order;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.PaymentTypeConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.UpdateOrderPaymentSerialNumberRequestVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: UpdateOrderPaymentSerialNumberVerification.java, v0.1 2023/3/30 10:09 yehuasheng Exp $
 */
@Component
public class UpdateOrderPaymentSerialNumberVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(UpdateOrderPaymentSerialNumberVerification.class.getName());

    /**
     * 检查参数
     *
     * @param updateOrderPaymentSerialNumberRequestVO 参数
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<Void> check(UpdateOrderPaymentSerialNumberRequestVO updateOrderPaymentSerialNumberRequestVO) {
        logger.info("check update order payment serial number parameter.");

        if (!StringUtils.equals(updateOrderPaymentSerialNumberRequestVO.getPaymentType(), PaymentTypeConstant.BANK_TRANSFER)) {
            // 检查是否没有传流水号
            if (StringUtils.isBlank(updateOrderPaymentSerialNumberRequestVO.getSerialNumber())) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_SERIAL_NUMBER_IS_NULL);
            }

            // 检查支付金额是否没有传
            if (null == updateOrderPaymentSerialNumberRequestVO.getPaymentPrice()
                    || updateOrderPaymentSerialNumberRequestVO.getPaymentPrice().compareTo(BigDecimal.ZERO) == CommonConstant.ZERO) {
                return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_PAYMENT_PRICE_IS_NULL);
            }
        }

        return BusinessResponse.ok(null);
    }
}
