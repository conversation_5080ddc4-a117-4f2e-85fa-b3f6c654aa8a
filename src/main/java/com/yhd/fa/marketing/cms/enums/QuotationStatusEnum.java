package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: QuotationStatusEnum.java, v0.1 2022/8/26 9:12 yehuasheng Exp $
 */
@Getter
public enum QuotationStatusEnum {
    OUT_TIME(QuotationStatusConstant.OUT_TIME, "gray", "报价有效期"),
    QUOTATION(QuotationStatusConstant.QUOTATION, "red", "快速报价中"),
    FINISH(QuotationStatusConstant.FINISH, "green", "报价完成"),
    CLOSE(QuotationStatusConstant.CLOSE, "gray", "已终止"),
    ;

    private final String status;
    private final String color;
    private final String desc;

    QuotationStatusEnum(String status, String color, String desc) {
        this.status = status;
        this.color = color;
        this.desc = desc;
    }
}
