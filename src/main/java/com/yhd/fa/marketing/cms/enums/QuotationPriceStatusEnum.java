package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.QuotedPriceStatusConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: QuotedPriceStatusEnum.java, v0.1 2022/9/23 9:32 yehuasheng Exp $
 */
@Getter
public enum QuotationPriceStatusEnum {
    CONFIRMED(508099, QuotedPriceStatusConstant.CONFIRMED, "已确认"),
    UNTREATED(508100, QuotedPriceStatusConstant.UNTREATED, "未处理"),
    CALCULATION_ERROR(508101, QuotedPriceStatusConstant.CALCULATION_ERROR, "计算错误"),
    CALCULATED(508102, QuotedPriceStatusConstant.CALCULATED, "已计算"),
    NOT_CALCULATED(508103, QuotedPriceStatusConstant.NOT_CALCULATED, "不计算"),
    QUANTITY_EXCESS(508105, QuotedPriceStatusConstant.QUANTITY_EXCESS, "超出数量折扣上限"),
    NO_PRICE(508106, QuotedPriceStatusConstant.NO_PRICE, "暂无价格"),
    MODEL_ERROR(508107, QuotedPriceStatusConstant.ERROR_MODEL, "型号错误"),
    NO_DELIVERY(508108, QuotedPriceStatusConstant.NO_DELIVERY, "无交期");

    private final int status;
    private final String code;
    private final String desc;

    QuotationPriceStatusEnum(int status, String code, String desc) {
        this.status = status;
        this.code = code;
        this.desc = desc;
    }
}
