package com.yhd.fa.marketing.cms.web.quotation;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.ExportEnquiryRequestVO;
import com.yhd.fa.marketing.cms.service.controller.ExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
@Tag(name = "价单导出接口", description = "价单导出接口包含：")
public class ExportController {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(ExportController.class.getName());

    @Resource
    private ExportService exportService;

    /**
     * 导出价单excel文件
     *
     * @param requestVO 导出订单参数
     * @param result    校验的返回值
     * @param response  返回的内容
     */
    @Operation(summary = "导出价单excel文件")
    @PostMapping(value = UriConstant.EXPORT_ENQUIRY_LOG_EXCEL)
    public void exportExcel(@RequestBody @Validated ExportEnquiryRequestVO requestVO, BindingResult result, HttpServletResponse response) {
        logger.info("export order excel. parameter exportOrderRequestVO:{}", requestVO);

        exportService.exportExcel(requestVO, result, response);
    }
}
