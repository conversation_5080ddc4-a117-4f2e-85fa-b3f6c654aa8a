package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.CustomerListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CustomerCompanyListResponseVO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: GetCustomerListLogic.java, v0.1 2023/1/3 16:54 yehuasheng Exp $
 */
@Component
public class GetCustomerListLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetCustomerListLogic.class.getName());

    /**
     * 用户中心
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    /**
     * 获取erp企业编码
     *
     * @param customerListRequestVO 获取企业编码参数
     * @return BusinessResponse<PageInfo < CustomerListResponseVO>>
     */
    public BusinessResponse<List<CustomerCompanyListResponseVO>> exec(CustomerListRequestVO customerListRequestVO) {
        logger.info("start exec get customer list logic.");

        // 根据企业名称获取接口
        return BusinessResponse.ok(userBucCmsService.searchCompanyListByCompanyName(customerListRequestVO.getCustomerName()));
    }
}
