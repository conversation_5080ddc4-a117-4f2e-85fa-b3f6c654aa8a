package com.yhd.fa.marketing.cms.mq.producer;

import com.alibaba.fastjson2.JSON;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.MqConstant;
import com.yhd.fa.marketing.cms.pojo.dto.*;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProductInfoResponseVO;
import com.yhd.fa.marketing.cms.service.sao.ProductCenterCmsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version Id: ProductAddNebulaGraphProducer.java, v0.1 2023/3/13 10:31 yehuasheng Exp $
 */
@Component
public class ProductAddNebulaGraphProducer {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(ProductAddNebulaGraphProducer.class.getName());

    /**
     * mq
     */
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 产品中心
     */
    @Resource
    private ProductCenterCmsService productCenterCmsService;

    /**
     * 发送报价单信息到图数据库
     *
     * @param quotationInfo 报价单详情
     * @param quotationList 报价单明细
     * @param userInfo      用户信息
     */
    @Async
    public void sendMqMessage(QuotationPO quotationInfo, List<QuotationListPO> quotationList, CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("send mq message for add quotation to nebula graph.");

        // 设置图数据库的详情
        List<AddNebulaGraphQuotationDTO> addNebulaGraphQuotationList = setQuotationNebulaGraphData(quotationInfo, userInfo);

        // 获取产品信息
        List<String> codeList = quotationList.stream()
                .map(QuotationListPO::getProductCode)
                .distinct()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<String, ProductInfoResponseVO> productInfo = productCenterCmsService.getProductInfo(codeList);

        // 设置报价单明细
        List<AddNebulaGraphQuotationDetailDTO> addNebulaGraphQuotationDetail = setQuotationDetailNebulaGraphData(quotationList, productInfo);

        // 设置报价单的边
        List<EdgeDTO> quotationEdge = setQuotationEdgeData(quotationInfo, quotationList);

        // 组装要同步的信息
        AddNebulaGraphTagsAndEdgeDTO addNebulaGraphTagsAndEdgeDTO = AddNebulaGraphTagsAndEdgeDTO
                .builder()
                .startTagName("quotation")
                .startTagJsonData(JSON.toJSONString(addNebulaGraphQuotationList))
                .endTagName("quotation_list")
                .endTagJsonData(JSON.toJSONString(addNebulaGraphQuotationDetail))
                .edgeName("quotation_edge")
                .edgeJsonData(JSON.toJSONString(quotationEdge))
                .build();

        // 发送报价单的信息
        logger.info("send mq message data.");
        rocketMQTemplate.convertAndSend(MqConstant.FA_TAG_EDGE_NEBULA_GRAPH_TOPIC, JSON.toJSONString(addNebulaGraphTagsAndEdgeDTO));

        // 设置报价单产品的边
        AddNebulaGraphDTO quotationProductEdge = setQuotationProductEdgeData(quotationList, productInfo);
        // 设置报价单用户的边
        AddNebulaGraphDTO quotationUserEdge = setQuotationUserEdgeData(quotationInfo, userInfo);
        // 发送mq
        rocketMQTemplate.convertAndSend(MqConstant.FA_EDGE_NEBULA_GRAPH_TOPIC, JSON.toJSONString(Arrays.asList(quotationUserEdge, quotationProductEdge)));
    }

    /**
     * 设置报价单详情
     *
     * @param quotationInfo 报价单详情
     * @param userInfo      用户详情
     * @return List<AddNebulaGraphQuotationDTO>
     */
    private List<AddNebulaGraphQuotationDTO> setQuotationNebulaGraphData(QuotationPO quotationInfo, CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("set quotation nebula graph data.");

        return Stream.of(quotationInfo)
                .map(quotationPO
                        -> AddNebulaGraphQuotationDTO
                        .builder()
                        .id(quotationPO.getId())
                        .quotationNumber(quotationPO.getQuotationNumber())
                        .userCode(quotationPO.getUserCode())
                        .userName(userInfo.getUserInfo().getUserName())
                        .companyCode(quotationPO.getCompanyCode())
                        .companyName(quotationPO.getCompanyName())
                        .createdDate(Date.from(quotationPO.getCreatedDate().atZone(ZoneId.systemDefault()).toInstant()))
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 设置报价单明细
     *
     * @param quotationList 报价单明细
     * @param productInfo   产品信息
     * @return List<AddNebulaGraphQuotationDetailDTO>
     */
    private List<AddNebulaGraphQuotationDetailDTO> setQuotationDetailNebulaGraphData(List<QuotationListPO> quotationList, Map<String, ProductInfoResponseVO> productInfo) {
        logger.info("set quotation detail nebula graph data.");

        return quotationList.stream()
                .filter(quotationListPO -> productInfo.containsKey(quotationListPO.getProductCode()))
                .map(quotationListPO
                        -> AddNebulaGraphQuotationDetailDTO
                        .builder()
                        .id(quotationListPO.getId())
                        .quotationId(quotationListPO.getQuotationId())
                        .quotationNumber(quotationListPO.getQuotationNumber())
                        .sort(quotationListPO.getSort())
                        .productName(quotationListPO.getProductName())
                        .model(quotationListPO.getModel())
                        .unit(quotationListPO.getUnit())
                        .code(quotationListPO.getProductCode())
                        .typeCode(productInfo.get(quotationListPO.getProductCode()).getTypeCode())
                        .catCode(productInfo.get(quotationListPO.getProductCode()).getCatCode())
                        .goodsCode(productInfo.get(quotationListPO.getProductCode()).getGoodsCode())
                        .createdDate(Date.from(quotationListPO.getCreatedDate().atZone(ZoneId.systemDefault()).toInstant()))
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 设置报价单边
     *
     * @param quotationInfo 报价单详情
     * @param quotationList 报价单明细
     * @return List<EdgeDTO>
     */
    private List<EdgeDTO> setQuotationEdgeData(QuotationPO quotationInfo, List<QuotationListPO> quotationList) {
        logger.info("set quotation edge data.");

        return quotationList.stream()
                .map(quotationListPO
                        -> EdgeDTO
                        .builder()
                        .startId(quotationInfo.getId())
                        .endId(quotationListPO.getId())
                        .edgeValue(quotationInfo.getQuotationNumber())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 设置报价单产品边
     *
     * @param quotationList 报价单明细
     * @param productInfo   报价单详情
     * @return AddNebulaGraphDTO
     */
    private AddNebulaGraphDTO setQuotationProductEdgeData(List<QuotationListPO> quotationList, Map<String, ProductInfoResponseVO> productInfo) {
        logger.info("start set quotation product edge data.");

        List<EdgeDTO> edge = quotationList.stream()
                .filter(quotationListPO
                        -> productInfo.containsKey(quotationListPO.getProductCode()))
                .map(quotationListPO
                        -> EdgeDTO
                        .builder()
                        .startId(quotationListPO.getId())
                        .endId(productInfo.get(quotationListPO.getProductCode()).getId())
                        .edgeValue(quotationListPO.getProductCode())
                        .build())
                .collect(Collectors.toList());

        return AddNebulaGraphDTO
                .builder()
                .type("edge")
                .edge("quotation_product_edge")
                .dataJson(JSON.toJSONString(edge))
                .build();
    }

    /**
     * 设置报价单用户边
     *
     * @param quotationInfo 报价单详情
     * @param userInfo      用户信息
     * @return AddNebulaGraphDTO
     */
    private AddNebulaGraphDTO setQuotationUserEdgeData(QuotationPO quotationInfo, CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo) {
        logger.info("start set quotation user edge data.");

        List<EdgeDTO> edge = Stream.of(quotationInfo)
                .map(quotationPO
                        -> EdgeDTO
                        .builder()
                        .startId(userInfo.getUserInfo().getId())
                        .endId(quotationPO.getId())
                        .edgeValue(userInfo.getUserCode())
                        .build())
                .collect(Collectors.toList());

        return AddNebulaGraphDTO.builder()
                .type("edge")
                .edge("user_quotation_edge")
                .dataJson(JSON.toJSONString(edge))
                .build();
    }
}
