package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/10 9:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "获取用户最后下单日期入参")
public class LastOrderDateRequestVO extends BaseVO {

    @Schema(description = "用户标识集合")
    @Size(min = 1,max = 2000,message = "入参集合长度异常")
    private List<String> userCodeList;
}
