package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.fa.marketing.cms.constant.RegexConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogListRequestVO.java, v0.1 2022/12/8 11:09 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EnquiryLogListRequestVO extends PageRequestVO {
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-12-08 10:12:23")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-12-08 10:12:23")
    private LocalDateTime endTime;
    /**
     * 注册时间开始
     */
    @Schema(description = "注册时间开始", example = "2022-12-08 10:12:23")
    private LocalDate startRegisterDate;

    /**
     * 注册时间结束
     */
    @Schema(description = "注册时间结束", example = "2022-12-08 10:12:23")
    private LocalDate endRegisterDate;

    /**
     * 型号
     */
    @Schema(description = "型号", example = "SAD01-D3-L100")
    private String model;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "企业名称")
    private String companyName;

    /**
     * 用户手机号码
     */
    @Schema(description = "用户手机号码", example = "13111111111", pattern = RegexConstant.MOBILE_REGEX)
    private String mobile;

    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱", example = "<EMAIL>", pattern = RegexConstant.EMAIL_REGX)
    private String email;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", example = "产品名称")
    private String productName;

    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价(小)", example = "含税折扣单价")
    private BigDecimal taxDiscountPriceMin;
    /**
     * 含税折扣单价
     */
    @Schema(description = "含税折扣单价(大)", example = "含税折扣单价")
    private BigDecimal taxDiscountPriceMax;

    /**
     * 用户类型 Personal 个人用户, Enterprise 普通企业用户, PendingEnterprise 待认证企业用户 , VerifiedEnterprise 认证企业用户'
     */
    @Schema(description = "用户类型 Personal 个人用户, Enterprise 普通企业用户, PendingEnterprise 待认证企业用户, VerifiedEnterprise 认证企业用户", example = "VerifiedEnterprise")
    private String userType;

    /**
     * 职位
     */
    @Schema(description = "职位", example = "采购")
    private String occupation;

    /**
     * 区域
     */
    @Schema(description = "区域", example = "华南")
    private String IpRegion;
}
