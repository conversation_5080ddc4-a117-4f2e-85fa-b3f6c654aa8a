package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogPO.java, v0.1 2022/8/27 10:55 yehuasheng Exp $
 * 业务员跟单员关联表
 */
@Data
@TableName("sale_merchandiser_relation")
public class SaleMerchandiserRelationPO {
    /**
     * 业务员工号
     */
    private String salesNo;

    /**
     * 跟单员编号
     */
    private String empNo;

}
