package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_pay_detail")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayDetailPO extends BaseEntity {
    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 付款金额
     */
    private BigDecimal totalPrice;
}

