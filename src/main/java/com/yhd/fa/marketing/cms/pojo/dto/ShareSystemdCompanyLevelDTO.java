package com.yhd.fa.marketing.cms.pojo.dto;


import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: ShareSystemdCompanyLevelDTO.java, v 0.1 2025/5/15 10:12 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ShareSystemdCompanyLevelDTO extends BaseDTO {

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户等级 资料客户1, 潜在客户2,意向客户3,成交客户4,长期客户5,FANS客户6,沉睡客户7,沉睡可转移8
     */
    private Integer customerGrade;

}
