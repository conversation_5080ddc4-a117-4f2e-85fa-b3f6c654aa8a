package com.yhd.fa.marketing.cms.enums;

import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.constant.OrderChannelConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderChannelEnum.java, v0.1 2023/2/20 8:59 yehuasheng Exp $
 */
@Getter
public enum OrderChannelEnum {
    ALL(CommonConstant.EMPTY, "全部"),
    PC(OrderChannelConstant.PC, "PC端"),
    WAP(OrderChannelConstant.WAP, "手机端"),
    WECHAT(OrderChannelConstant.WECHAT, "微信端"),
    APP(OrderChannelConstant.APP, "app"),
    APPLET(OrderChannelConstant.APPLET, "小程序"),
    ;

    private final String orderChannel;
    private final String orderChannelCn;

    OrderChannelEnum(String orderChannel, String orderChannelCn) {
        this.orderChannel = orderChannel;
        this.orderChannelCn = orderChannelCn;
    }
}
