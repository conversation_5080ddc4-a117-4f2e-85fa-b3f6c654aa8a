package com.yhd.fa.marketing.cms.service.sao;

import com.yhd.buc.cms.api.sdk.pojo.vo.response.*;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.UserInfo;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CustomerCompanyListResponseVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: UserBucCmsService.java, v0.1 2022/12/5 9:03 yehuasheng Exp $
 */
public interface UserBucCmsService {
    /**
     * 根据用户编码获取用户基础信息
     *
     * @param userCode 用户编码
     * @return List<BaseUserInfoResponseVO>
     */
    List<UserInfo> getUserBaseListByUserCode(List<String> userCode);

    /**
     * 根据用户编码获取用户基础信息
     *
     * @param userCode 用户编码
     * @return Map<String, BaseUserInfoResponseVO>
     */
    Map<String, UserInfo> getUserBaseMapByUserCode(List<String> userCode);

    /**
     * 获取用户所有信息
     *
     * @param userCode    用户编码
     * @param companyCode 企业编码
     * @return AllUserInfoResponseVO
     */
    CompanyAndUserAndMerchandiserAndResourcesResponseVO getUserInfo(String userCode, String companyCode);

    /**
     * 根据用户编码集合获取用户信息-企业信息-跟单信息
     *
     * @param userCodeAndCompanyCodeDTO 用户编码集合
     * @return Map<String, UserAndCompanyAndMerchandiserResponseVO>
     */
    Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserAndCompanyBaseInfo(List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO);

    /**
     * 根据企业名称搜索企业列表
     *
     * @param companyName 企业名称
     * @return List<CustomerCompanyListResponseVO>
     */
    List<CustomerCompanyListResponseVO> searchCompanyListByCompanyName(String companyName);

    /**
     * 根据企业编码获取用户列表
     *
     * @param companyCode 企业编码
     * @return List<CustomerUserListResponseVO>
     */
    List<CompanyUserCustomResponseVO> getUserListByCompanyCode(String companyCode);

    /**
     * 根据手机号码或者邮箱模糊查询用户编码
     *
     * @param model 手机号码
     * @param email 电子邮箱
     * @return List<String>
     */
    List<String> getUserCodeByModelOrEmail(String model, String email);

    /**
     * 根据企业编码获取企业信息
     *
     * @param companyCode 企业编码列表
     * @return Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO>
     */
    Map<String, CompanyAndMerchandiserAndSalesmanInfoResponseVO> getCompanyAndMerchandiserAndSalesmanInfo(List<String> companyCode);


    /**
     * 根据收货地址id获取收货地址完整信息
     *
     * @param addressId   地址id
     * @param userCode    用户编码
     * @param addressType 地址类型 invoice(发票地址),shipping（收货地址)
     * @return ShippingAddressResponseVO
     */
    ShippingAddressResponseVO getUserAddressByAddressId(String addressId, String addressType, String userCode);

    Map<String, EnquiryLogInfoResponseVO> getEnquiryLogInfo(List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO);
}
