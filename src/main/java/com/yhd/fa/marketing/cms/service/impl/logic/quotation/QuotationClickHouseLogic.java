package com.yhd.fa.marketing.cms.service.impl.logic.quotation;


import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.QuotationPeriodConstant;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotationSynchronizationStatusConstant;
import com.yhd.fa.marketing.cms.dao.QuotationClickHouseDAO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationClickHousePO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationInfoRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationInfoResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: QuotationClickHouseLogic.java, v 0.1 2025/4/25 15:26 JiangYuHong Exp $
 */
@Component
public class QuotationClickHouseLogic {

    private static final Logger logger = LogUtils.getLogger(QuotationClickHouseLogic.class.getName());
    /**
     * 72小时
     */
    private static final Integer SEVENTY_TWO = 72;
    @Resource
    private QuotationClickHouseDAO quotationClickHouseDAO;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 查询ClickHouse报价单列表数据
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param lastSql                最后拼接sql
     * @return List<QuotationInfoResponseVO>
     */
    public List<QuotationInfoResponseVO> selectListDeepForQueryCrossData(QuotationInfoRequestVO quotationInfoRequestVO, String lastSql) {

        logger.info("start select cold data get quotation list for click house.");

        MPJLambdaWrapper<QuotationClickHousePO> queryWrapper = setQueryWrapper(quotationInfoRequestVO);

        if (queryWrapper != null) {
            queryWrapper.last(lastSql);
        }

        List<QuotationInfoResponseVO> quotationClickHousePOS = quotationClickHouseDAO.selectJoinList(QuotationInfoResponseVO.class, queryWrapper);

        logger.info("select cold data get quotation list for click house end.");

        return quotationClickHousePOS;
    }


    /**
     * 设置查询条件
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @return MPJLambdaWrapper<QuotationClickHousePO>
     */
    private MPJLambdaWrapper<QuotationClickHousePO> setQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO) {
        logger.info("set get quotation list query wrapper.");

        // 设置查询的字段以及排序
        MPJLambdaWrapper<QuotationClickHousePO> queryWrapper = new MPJLambdaWrapper<QuotationClickHousePO>()
                .selectAs(QuotationClickHousePO::getId, QuotationInfoResponseVO::getQuotationId)
                .selectAs(QuotationClickHousePO::getQuotationNumber, QuotationInfoResponseVO::getQuotationNumber)
                .selectAs(QuotationClickHousePO::getUserCode, QuotationInfoResponseVO::getUserCode)
                .selectAs(QuotationClickHousePO::getPurchaseUserCode, QuotationInfoResponseVO::getPurchaseUserCode)
                .selectAs(QuotationClickHousePO::getCompanyCode, QuotationInfoResponseVO::getCompanyCode)
                .selectAs(QuotationClickHousePO::getCompanyName, QuotationInfoResponseVO::getCompanyName)
                .selectAs(QuotationClickHousePO::getPayablePrice, QuotationInfoResponseVO::getTotalPrice)
                .selectAs(QuotationClickHousePO::getCreatedBy, QuotationInfoResponseVO::getCreatedBy)
                .selectAs(QuotationClickHousePO::getDeleteStatus, QuotationInfoResponseVO::getDeleteStatus)
                .selectAs(QuotationClickHousePO::getQuotationStatus, QuotationInfoResponseVO::getQuotationStatus)
                .selectAs(QuotationClickHousePO::getIsInsideCreated, QuotationInfoResponseVO::getIsInsideCreated)
                .selectAs(QuotationClickHousePO::getInsideEmployeeCode, QuotationInfoResponseVO::getInsideEmployeeCode)
                .selectAs(QuotationClickHousePO::getEmployeeName, QuotationInfoResponseVO::getEmployeeName)
                .selectAs(QuotationClickHousePO::getSynchronizationStatus, QuotationInfoResponseVO::getSynchronizationStatus)
                .selectAs(QuotationClickHousePO::getSynchronizationDate, QuotationInfoResponseVO::getSynchronizeDate)
                .selectAs(QuotationClickHousePO::getCreatedDate, QuotationInfoResponseVO::getCreatedDate)
                .selectAs(QuotationClickHousePO::getExamineDate, QuotationInfoResponseVO::getExamineDate)
                .selectAs(QuotationClickHousePO::getQuotationCompletionDate, QuotationInfoResponseVO::getQuotationCompletionDate)
                .selectAs(QuotationClickHousePO::getIsExamine, QuotationInfoResponseVO::getIsExamine)
                .selectAs(QuotationClickHousePO::getTransferOrder, QuotationInfoResponseVO::getTransferOrder)
                .orderByDesc(QuotationClickHousePO::getCreatedDate);

        // 如果有传递报价单号
        setQuotationNumberQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传时间
        setTimeQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传企业名称
        setCompanyNameQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传状态
        setQuotationStatusQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有超报价时效
        setQuotationPeriodQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传建单内部人
        setInsideEmployeeCodeQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传是否转订单
        setTransferOrderQueryWrapper(quotationInfoRequestVO, queryWrapper);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, QuotationClickHousePO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }


        // 返回查询的条件
        return queryWrapper;
    }

    /**
     * 设置查询报价单号
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setQuotationNumberQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationClickHousePO> queryWrapper) {
        logger.info("set quotation number query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getQuotationNumber())) {
            // 将报价单号以“,”号隔开
            List<String> quotationNumberList = Arrays.asList(quotationInfoRequestVO.getQuotationNumber().split(CommonConstant.SYMBOL_COMMA));

            // 如果是拆分后超过1个以上的，代码是查询多个 那么要用in 查询 如果是只有一个那么需要模糊查询
            if (quotationNumberList.size() > CommonConstant.ONE) {
                queryWrapper.in(QuotationClickHousePO::getQuotationNumber, quotationNumberList);
            } else {
                queryWrapper.likeRight(QuotationClickHousePO::getQuotationNumber, quotationInfoRequestVO.getQuotationNumber().trim());
            }
        }
    }

    /**
     * 设置查询传时间
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setTimeQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationClickHousePO> queryWrapper) {
        logger.info("set time query wrapper.");

        if (null != quotationInfoRequestVO.getStartTime()) {
            queryWrapper.ge(QuotationClickHousePO::getCreatedDate, quotationInfoRequestVO.getStartTime());
        }
        if (null != quotationInfoRequestVO.getEndTime()) {
            queryWrapper.le(QuotationClickHousePO::getCreatedDate, quotationInfoRequestVO.getEndTime());
        }
    }

    /**
     * 设置查询企业名称
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setCompanyNameQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationClickHousePO> queryWrapper) {
        logger.info("set company name query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getCompanyName())) {
            queryWrapper.likeRight(QuotationClickHousePO::getCompanyName, quotationInfoRequestVO.getCompanyName().trim());
        }
    }

    /**
     * 设置查询报价单状态
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setQuotationStatusQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationClickHousePO> queryWrapper) {
        logger.info("set quotation status query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getQuotationStatus())) {
            queryWrapper.eq(QuotationClickHousePO::getQuotationStatus, quotationInfoRequestVO.getQuotationStatus());
        }
    }

    /**
     * 设置报价时效查询
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setQuotationPeriodQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationClickHousePO> queryWrapper) {
        logger.info("set quotation period query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getQuotationPeriod())) {
            // 先设置已同步
            queryWrapper.eq(QuotationClickHousePO::getSynchronizationStatus, QuotationSynchronizationStatusConstant.SYNCHRONIZED);
            queryWrapper.eq(QuotationClickHousePO::getQuotationStatus, QuotationStatusConstant.QUOTATION);

            // 设置查询的时间点
            LocalDateTime nowTime = LocalDateTime.now();

            switch (quotationInfoRequestVO.getQuotationPeriod()) {
                // 1小时
                case QuotationPeriodConstant.ONE_HOURS:
                    queryWrapper.le(QuotationClickHousePO::getSynchronizationDate, nowTime.minusHours(CommonConstant.ONE));
                    queryWrapper.gt(QuotationClickHousePO::getSynchronizationDate, nowTime.minusHours(CommonConstant.TWO));
                    break;
                // 2小时
                case QuotationPeriodConstant.TWO_HOURS:
                    queryWrapper.le(QuotationClickHousePO::getSynchronizationDate, nowTime.minusHours(CommonConstant.TWO));
                    queryWrapper.gt(QuotationClickHousePO::getSynchronizationDate, nowTime.minusHours(CommonConstant.THREE));
                    break;
                // 3小时
                case QuotationPeriodConstant.THREE_HOURS:
                    queryWrapper.le(QuotationClickHousePO::getSynchronizationDate, nowTime.minusHours(CommonConstant.THREE));
                    queryWrapper.gt(QuotationClickHousePO::getSynchronizationDate, nowTime.minusHours(SEVENTY_TWO));
                    break;
                // 72小时
                case QuotationPeriodConstant.SEVENTY_TWO_HOURS:
                    queryWrapper.le(QuotationClickHousePO::getSynchronizationDate, nowTime.minusHours(SEVENTY_TWO));
                    break;
                default:
            }
        }
    }

    /**
     * 设置查询内部人员创建报价单
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setInsideEmployeeCodeQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationClickHousePO> queryWrapper) {
        logger.info("set inside employee code query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getInsideEmployeeCode())) {
            queryWrapper.eq(QuotationClickHousePO::getInsideEmployeeCode, quotationInfoRequestVO.getInsideEmployeeCode());
        }
    }

    /**
     * 设置查询是否已转订单
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setTransferOrderQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationClickHousePO> queryWrapper) {
        logger.info("set transfer order query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getTransferOrder())) {
            queryWrapper.eq(QuotationClickHousePO::getTransferOrder, quotationInfoRequestVO.getTransferOrder());
        }
    }

    /**
     * 获取完整的SQL语句(带参数值)
     *
     * @param wrapper MPJLambdaWrapper对象
     * @return 完整SQL
     */
    public String getSelectOrderListFullSql(MPJLambdaWrapper<QuotationPO> wrapper) {
        String sqlSelect = wrapper.getSqlSelect();
        String sqlFrom = wrapper.getFrom();
        String sqlComment = wrapper.getCustomSqlSegment();

        // 获取参数映射
        Map<String, Object> paramNameValuePairs = wrapper.getParamNameValuePairs();

        // 组装基础SQL
        String sql = "SELECT DISTINCT " + sqlSelect + " " +
                "FROM fa_quotation t" + sqlFrom + " " +
                sqlComment;

        // 替换参数值
        for (Map.Entry<String, Object> entry : paramNameValuePairs.entrySet()) {
            String paramName = "#{ew.paramNameValuePairs." + entry.getKey() + "}";
            Object paramValue = entry.getValue();

            // 根据参数类型处理值
            String replaceValue;
            if (paramValue instanceof String) {
                replaceValue = "'" + paramValue + "'";
            } else if (paramValue instanceof LocalDateTime) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER);
                replaceValue = "'" + ((LocalDateTime) paramValue).format(formatter) + "'";
            } else {
                replaceValue = String.valueOf(paramValue);
            }

            sql = sql.replace(paramName, replaceValue);
        }

        return sql;
    }


}
