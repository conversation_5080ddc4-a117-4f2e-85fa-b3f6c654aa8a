package com.yhd.fa.marketing.cms.service.impl.sao;

import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;
import com.yhd.fa.marketing.cms.sao.ProductBusinessInfoSAO;
import com.yhd.fa.marketing.cms.service.sao.ProductBusinessInfoService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: ProductBusinessInfoServiceImpl.java, v0.1 2022/12/23 10:16 yehuasheng Exp $
 */
@Service
public class ProductBusinessInfoServiceImpl implements ProductBusinessInfoService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(ProductBusinessInfoServiceImpl.class.getName());

    /**
     * 报价与交期的服务
     */
    @Resource
    private ProductBusinessInfoSAO productBusinessInfoSAO;

    /**
     * 获取型号价格与交期
     *
     * @param quotationRequestVO 询价参数
     * @return BusinessResponse<List < QuotationResponseVO>>
     */
    @Override
    public BusinessResponse<List<QuotationResponseVO>> getModelPrice(QuotationRequestVO quotationRequestVO) {
        logger.info("get model price for yhd-service-product-business-info service.");

        // 定义设置时间
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            logger.info("get model price parameter quotationRequestVO:{}", quotationRequestVO);

            // 请求接口
            BusinessResponse<List<QuotationResponseVO>> businessResponse = productBusinessInfoSAO.quotationPrice(quotationRequestVO);
            logger.info("get model price success. result rt_code:{} rt_msg:{}", businessResponse.getRt_code(), businessResponse.getRt_msg());
            logger.info("get model price cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return businessResponse;
        } catch (Exception e) {
            logger.error("get model price fail errorMsg:{}", e.getMessage());
            logger.info("get model price cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return BusinessResponseCommon.fail(e.getMessage());
        }
    }
}
