package com.yhd.fa.marketing.cms.pojo.dto;


import com.yhd.common.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsProblemCategoriesDTO.java, v 0.1 2025/5/19 15:52 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderCommentsProblemCategoriesDTO extends BaseDTO {

    /**
     * 子级id
     */
    @Schema(description = "子级id", example = "1")
    private Integer childId;

    /**
     * 子级名称
     */
    @Schema(description = "子级名称", example = "质量问题")
    private String childName;

    /**
     * 父级id
     */
    @Schema(description = "父级id", example = "1")
    private Integer parentId;

    /**
     * 父级名称
     */
    @Schema(description = "父级名称", example = "质量问题")
    private String parentName;
}
