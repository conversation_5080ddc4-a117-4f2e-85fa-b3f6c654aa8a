package com.yhd.fa.marketing.cms.service.impl.verification.order;

import cn.hutool.core.util.ObjectUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderListRequestVO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: GetOrderVerification.java, v0.1 2022/12/2 14:34 yehuasheng Exp $
 */
@Component
public class GetOrderListVerification {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderListVerification.class.getName());

    /**
     * 检查订单列表参数
     *
     * @param orderListRequestVO 订单列表参数
     * @param <T>                T
     * @return <T>BusinessResponse<T>
     */
    public <T> BusinessResponse<T> check(OrderListRequestVO orderListRequestVO) {
        logger.info("check order list parameter.");

        // 校验时间是否符合
        boolean checkDateTime = ObjectUtil.isNotNull(orderListRequestVO.getEndDateTime()) && ObjectUtil.isNotNull(orderListRequestVO.getStartDateTime()) && orderListRequestVO.getStartDateTime().isAfter(orderListRequestVO.getEndDateTime());
        if (checkDateTime) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.START_DATE_IS_AFTER_END_TIME_ERROR);
        }

        return BusinessResponse.ok(null);
    }
}
