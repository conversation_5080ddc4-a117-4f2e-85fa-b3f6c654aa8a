package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleDetailResponseVO.java, v0.1 2023/2/24 16:12 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderAfterSaleDetailResponseVO extends BaseVO {

    /**
     * 售后单号
     */
    @Schema(description = "售后单号", example = "ACI201811136064")
    private String afterSaleNumber;

    /**
     * 售后单类型 线上：shop；线下：offline
     */
    @Schema(description = "售后单类型 线上：shop；线下：offline", example = "shop")
    private String afterSaleOrderType;

    /**
     * 订单id
     */
    @Schema(description = "订单id", example = "34b43cf1ef314acdb6dc959b0ac017c4")
    private String orderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "MYI0000013941364829")
    private String orderNumber;

    /**
     * 订单支付方式
     * unknown 暂时未知的支付方式
     * aliPay 支付宝、
     * weChatPay 微信支付、
     * bankTransfer 银行转账、
     * unionPay银联、
     * monthlyKnot 月结、
     * monthlyKnot30 月结30天、
     * monthlyKnot60 月结60天、
     * monthlyKnot90 月结90天
     */
    @Schema(description = "订单支付方式 unknown 暂时未知的支付方式、aliPay 支付宝、weChatPay 微信支付、bankTransfer 银行转账、unionPay银联、monthlyKnot 月结、monthlyKnot30 月结30天、monthlyKnot60 月结60天、monthlyKnot90 月结90天", example = "unknown")
    private String orderPaymentType;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "01111")
    private String userCode;

    /**
     * 公司编码
     */
    @Schema(description = "公司编码", example = "J52274405784")
    private String companyCode;

    /**
     * 公司名称
     */
    @Schema(description = "公司名称", example = "广东唯康教育科技股份有限公司")
    private String companyName;

    /**
     * erp编码
     */
    @Schema(description = "erp编码", example = "C-********-0041")
    private String erpCompanyCode;

    /**
     * 是否收到货 是 true 否 false
     */
    @Schema(description = "是否收到货 是 true 否 false", example = "true")
    private String shipmentReceiptStatus;

    /**
     * 售后类型：退货退款 refund；换货 exchange；维修 repair
     */
    @Schema(description = "售后类型：退货退款 refund；换货 exchange；维修 repair", example = "refund")
    private String afterSaleType;

    /**
     * 订单售后类型名称
     */
    @Schema(description = "订单售后类型名称", example = "换货")
    private String orderAfterSaleTypeName;

    /**
     * 图片路径
     */
    @Schema(description = "图片路径 多张逗号隔开", example = "http://127.0.0.1:8080/fadoc/images/2022/01/01/1641015588921.jpg")
    private String imagePath;

    /**
     * 状态：待审核 pendingAudit；
     * 待寄回商品 pendingReturn；
     * 寄回商品待检测 pendingCheck；
     * 待退款 pendingRefund；
     * 待发货 pendingShip；
     * 待收货 pendingReceive；
     * 待上门维修 pendingRepair；
     * 售后完成 completed；
     * 售后关闭 closed；
     * 用户取消 cancel
     * 退款中 refunding
     *
     */
    @Schema(description = "状态：待审核 pendingAudit；待寄回商品 pendingReturn；寄回商品待检测 pendingCheck；待退款 pendingRefund；待发货 pendingShip；待收货 pendingReceive；待上门维修 pendingRepair；售后完成 completed；售后关闭 closed；用户取消 cancel", example = "processing")
    private String afterSaleStatus;

    /**
     * 订单售后状态名称
     */
    @Schema(description = "订单售后状态名称", example = "处理中")
    private String orderAfterSaleStatusName;

    /**
     * 申请理由
     */
    @Schema(description = "申请理由", example = "我要退货")
    private String suggestReason;

    /**
     * 问题描述
     */
    @Schema(description = "问题描述", example = "我要退货")
    private String problemDescription;

    /**
     * 售后价格
     */
    @Schema(description = "售后价格", example = "100.00")
    private BigDecimal afterSalePrice;

    /**
     * 扣除积分
     */
    @Schema(description = "扣除积分", example = "100")
    private Integer deductionPoints;

    /**
     * ERP同步状态 wait等待同步 success成功 fail失败
     */
    @Schema(description = "ERP同步状态 wait等待同步 success成功 fail失败", example = "success")
    private String synchronizationStatus;

    /**
     * 推送给erp的报错信息
     */
    @Schema(description = "推送给erp的报错信息", example = "成功")
    private String synchronizationErrorMsg;

    /**
     * 处理负责人
     */
    @Schema(description = "处理负责人", example = "林杏金")
    private String handlerPersonName;

    /**
     * 处理负责人联系方式
     */
    @Schema(description = "处理负责人联系方式", example = "1311111111")
    private String handlerPersonPhone;

    /**
     * 处理说明
     */
    @Schema(description = "处理说明", example = "我要退货")
    private String handlerDescription;

    /**
     * 处理方案
     */
    @Schema(description = "处理方案", example = "我要退货")
    private String treatmentPlan;

    /**
     * 申请人
     */
    @Schema(description = "申请人", example = "林杏金")
    private String applicant;

    /**
     * 申请人电话
     */
    @Schema(description = "申请人电话", example = "1311111111")
    private String applicantPhone;

    /**
     * 申请人邮箱
     */
    @Schema(description = "申请人邮箱", example = "<EMAIL>")
    private String applicantEmail;

    /**
     * 维修方式: 上门维修 doorToDoorRepair; 返厂维修 factoryRepair
     */
    @Schema(description = "维修方式: 上门维修 doorToDoorRepair; 返厂维修 factoryRepair", example = "doorToDoorRepair")
    private String repairMethod;

    /**
     * 维修工程师名称
     */
    @Schema(description = "维修工程师名称", example = "林杏金")
    private String maintenanceEngineer;

    /**
     * 维修工程师联系方式
     */
    @Schema(description = "维修工程师联系方式", example = "1311111111")
    private String maintenanceEngineerContact;

    /**
     * 用户寄回商品快递公司名称
     */
    @Schema(description = "用户寄回商品快递公司名称", example = "顺丰")
    private String userReturnCourier;

    /**
     * 用户寄回商品快递单号
     */
    @Schema(description = "用户寄回商品快递单号", example = "*********0")
    private String userReturnTrackingNumber;

    /**
     * 物流公司
     */
    @Schema(description = "物流公司（怡合达回寄）", example = "顺丰")
    private String logisticsCompany;

    /**
     * 物流单号
     */
    @Schema(description = "物流单号（怡合达回寄）", example = "*********")
    private String logisticsNumber;

    /**
     * 寄回公司名称(怡合达收货地址/联系方式)
     */
    @Schema(description = "寄回公司名称(怡合达收货地址/联系方式)", example = "广东唯康教育科技股份有限公司")
    private String returnCompanyName;

    /**
     * 寄回收货人(怡合达收货地址/联系方式)
     */
    @Schema(description = "寄回收货人(怡合达收货地址/联系方式)", example = "林杏金")
    private String returnRecipient;

    /**
     * 寄回地址(怡合达收货地址/联系方式)
     */
    @Schema(description = "寄回地址(怡合达收货地址/联系方式)", example = "广东省东莞市")
    private String returnAddress;

    /**
     * 寄回联系电话(怡合达收货地址/联系方式)
     */
    @Schema(description = "寄回联系电话(怡合达收货地址/联系方式)", example = "1311111111")
    private String returnContactPhone;

    /**
     * 拒绝理由
     */
    @Schema(description = "拒绝理由", example = "我要退货")
    private String rejectReason;

    /**
     * 跟单员
     */
    @Schema(description = "跟单员", example = "你猜")
    private String merchandiserName;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "1311111111")
    private String merchandiserContact;

    /**
     * 业务员名称
     */
    @Schema(description = "业务员名称", example = "01111/林杏金")
    private String salesmanName;

    /**
     * 业务员联系方式
     */
    @Schema(description = "业务员联系方式", example = "13111111111")
    private String salesmanContact;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-01-01 10:10:10")
    private LocalDateTime createdDate;

    /**
     * 售后反馈
     */
    @Schema(description = "售后反馈")
    private OrderAfterSaleFeedbackResponseVO orderAfterSaleFeedback;

    /**
     * 状态节点列表
     */
    @Schema(description = "状态节点列表")
    private List<OrderAfterSaleStatusResponseVO> statusList;

    /**
     * 订单售后的详情产品
     */
    @Schema(description = "订单售后的详情产品")
    private List<OrderAfterSaleDetailListResponseVO> orderAfterSaleDetails;
}
