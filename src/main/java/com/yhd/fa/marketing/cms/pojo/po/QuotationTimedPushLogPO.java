package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: QuotationTimedPushLogPO.java, v0.1 2023/9/27 08:32 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("fa_quotation_timed_push_log")
public class QuotationTimedPushLogPO extends BaseEntity {
    /**
     * 报价单id
     */
    @TableField(value = "quotation_id")
    private String quotationId;

    /**
     * 推送的平台 dingding钉钉、wechat微信、sms短信、email邮件
     */
    @TableField(value = "push_platform")
    private String pushPlatform;

    /**
     * 推送人
     */
    private String pusher;

    /**
     * 推送类型 notTransferOrder未转订单
     */
    @TableField(value = "push_type")
    private String pushType;

    /**
     * 模板id
     */
    @TableField(value = "push_template_id")
    private String pushTemplateId;
}
