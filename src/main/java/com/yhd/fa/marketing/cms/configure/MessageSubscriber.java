//package com.yhd.fa.marketing.cms.configure;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson2.JSON;
//import com.github.yulichang.wrapper.MPJLambdaWrapper;
//import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
//import com.yhd.buc.cms.api.sdk.pojo.vo.response.EnquiryLogInfoResponseVO;
//import com.yhd.buc.cms.api.sdk.utils.UserApiUtil;
//import com.yhd.common.util.CommonConstant;
//import com.yhd.common.util.LogUtils;
//import com.yhd.fa.marketing.cms.constant.BaseConstant;
//import com.yhd.fa.marketing.cms.dao.CompanyDAO;
//import com.yhd.fa.marketing.cms.dao.EnquiryLogDAO;
//import com.yhd.fa.marketing.cms.enums.ShareSystemdCompanyLevelEnum;
//import com.yhd.fa.marketing.cms.pojo.dto.ShareSystemdCompanyLevelDTO;
//import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
//import com.yhd.fa.marketing.cms.pojo.po.CompanyPO;
//import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
//import com.yhd.fa.marketing.cms.pojo.po.UcSyncCustomerInfoPO;
//import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.task.TaskExecutor;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.listener.PatternTopic;
//import org.springframework.data.redis.listener.RedisMessageListenerContainer;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//
//@Configuration
//public class MessageSubscriber extends RedisMessageListenerContainer {
//
//    private static final Logger logger = LogUtils.getLogger(MessageSubscriber.class.getName());
//    /**
//     * 查价单的mapper
//     */
//    @Resource
//    private EnquiryLogDAO enquiryLogDAO;
//    /**
//     * 用户的服务
//     */
//    @Resource
//    private UserBucCmsService userBucCmsService;
//
//    @Resource
//    private CompanyDAO companyDAO;
//
//    @Bean
//    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory connectionFactory, TaskExecutor redisTaskExecutor) {
//        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
//        container.setConnectionFactory(connectionFactory);
//        //设置线程池 若不设置则使用默认的  这里不设置
////        container.setTaskExecutor(redisTaskExecutor); //
//        container.addMessageListener((message, pattern) -> {
//         try {
//             String body = new String(message.getBody());
//             List<EnquiryLogPO> enquiryLogPOList = JSON.parseArray(JSON.parse(body).toString(), EnquiryLogPO.class);
//             if(CollUtil.isEmpty(enquiryLogPOList)){
//                 return;
//             }
//             List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = enquiryLogPOList
//                     .stream()
//                     .map(enquiryLogPO
//                             -> UserCodeAndCompanyCodeDTO
//                             .builder()
//                             .userCode(enquiryLogPO.getUserCode())
//                             .companyCode(enquiryLogPO.getCompanyCode())
//                             .build())
//                     .collect(Collectors.toList());
//             // 获取用户的信息
//             Map<String, EnquiryLogInfoResponseVO> userInfoMap = userBucCmsService.getEnquiryLogInfo(userCodeAndCompanyCodeDTO);
//
//
//             enquiryLogPOList
//                     .stream()
//                     .filter(enquiryLogPO
//                             -> StringUtils.isNotBlank(enquiryLogPO.getUserCode())
//                             && userInfoMap.containsKey(enquiryLogPO.getUserCode()))
//                     .forEach(enquiryLogPO -> Optional.ofNullable(userInfoMap.get(enquiryLogPO.getUserCode())).ifPresent(enquiryLogInfoResponseVO -> {
//                         // 设置用户的名字和企业的名称
//                         enquiryLogPO.setUserName(StringUtils.isBlank(enquiryLogInfoResponseVO.getUserName())?null:enquiryLogInfoResponseVO.getUserName());
//                         enquiryLogPO.setUserPhone(StringUtils.isBlank(enquiryLogInfoResponseVO.getMobile())?null:enquiryLogInfoResponseVO.getMobile());
//                         enquiryLogPO.setUserEmail(StringUtils.isBlank(enquiryLogInfoResponseVO.getEmail())?null:enquiryLogInfoResponseVO.getEmail());
//                         enquiryLogPO.setMerchandiser(StringUtils.isBlank(enquiryLogInfoResponseVO.getMerchandiserEmployeeCode())?null:enquiryLogInfoResponseVO.getMerchandiserEmployeeName() + CommonConstant.SLASH + enquiryLogInfoResponseVO.getMerchandiserEmployeeCode());
//                         enquiryLogPO.setSalesman(StringUtils.isBlank(enquiryLogInfoResponseVO.getSalesmanEmployeeCode())?null:enquiryLogInfoResponseVO.getSalesmanEmployeeName() + CommonConstant.SLASH + enquiryLogInfoResponseVO.getSalesmanEmployeeCode());
//                         enquiryLogPO.setCompanyName(StringUtils.isBlank(enquiryLogInfoResponseVO.getCompanyName())?null:enquiryLogInfoResponseVO.getCompanyName());
//                     }));
//
//             List<String> companyList = enquiryLogPOList.stream().map(EnquiryLogPO::getCompanyCode).distinct().collect(Collectors.toList());
//             if(CollUtil.isNotEmpty(companyList)){
//                 companyDAO.selectJoinList(ShareSystemdCompanyLevelDTO.class, new MPJLambdaWrapper<CompanyPO>()
//                                 .leftJoin(UcSyncCustomerInfoPO.class, UcSyncCustomerInfoPO::getCustomerNo, CompanyPO::getErpCompanyCode)
//                                 .selectAs(CompanyPO::getCompanyCode, ShareSystemdCompanyLevelDTO::getCompanyCode)
//                                 .selectAs(UcSyncCustomerInfoPO::getCustomerNo, ShareSystemdCompanyLevelDTO::getCustomerNo)
//                                 .selectAs(UcSyncCustomerInfoPO::getCustomerGrade, ShareSystemdCompanyLevelDTO::getCustomerGrade)
//                                 .selectAll(CompanyPO.class)
//                                 .in(CompanyPO::getCompanyCode, companyList))
//                         .forEach(ucSyncCustomerInfoPrivatePO -> enquiryLogPOList.stream()
//                                 .filter(enquiryLogPO -> StringUtils.equals(enquiryLogPO.getCompanyCode(), ucSyncCustomerInfoPrivatePO.getCompanyCode()))
//                                 .forEach(enquiryLogPO -> enquiryLogPO.setCustomerGrade(ShareSystemdCompanyLevelEnum.getName(ucSyncCustomerInfoPrivatePO.getCustomerGrade()))));
//             }
//
//             enquiryLogDAO.insertBatch(enquiryLogPOList);
//         }catch (Exception e){
//             logger.error("redisMessageListenerContainer fail :{}",e);
//         }
//        }, new PatternTopic(BaseConstant.QUOTATION_CHANNEL));
//        return container;
//    }
//
////    @Bean
////    public TaskExecutor redisTaskExecutor() {
////        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
////        executor.setCorePoolSize(8);
////        executor.setMaxPoolSize(16);
////        executor.setQueueCapacity(100);
////        executor.setThreadNamePrefix("redis-subscription-");
////        executor.initialize();
////        return executor;
////    }
//}
