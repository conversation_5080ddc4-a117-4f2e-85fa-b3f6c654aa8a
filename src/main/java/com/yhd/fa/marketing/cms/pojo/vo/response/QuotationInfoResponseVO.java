package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: QuotationInfoResponseVO.java, v0.1 2022/12/2 9:40 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuotationInfoResponseVO extends BaseVO {
    /**
     * 报价单id
     */
    @Schema(description = "报价单id", example = "2aa9ccd3fbbd4da486d8c88c05d4d3c9")
    private String quotationId;

    /**
     * 报价单号
     */
    @Schema(description = "报价单号", example = "YB202211080932589779HED6")
    private String quotationNumber;

    /**
     * 客户报价单号
     */
    @Schema(description = "客户报价单号", example = "MISIMI123456")
    private String customerQuotationNumber;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "139779")
    private String userCode;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称", example = "测试大哥好")
    private String userName;

    /**
     * 采购人编号
     */
    @Schema(description = "采购人编号", example = "139779")
    private String purchaseUserCode;

    /**
     * 采购人名称
     */
    @Schema(description = "采购人名称", example = "测试大哥好")
    private String purchaseUserName;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "HA5814973528")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 跟单名称
     */
    @Schema(description = "跟单员名称", example = "0111/林杏金")
    private String merchandiser;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "13111111111")
    private String merchandiserContact;

    /**
     * 业务员名称
     */
    @Schema(description = "业务员名称", example = "01111/林杏金")
    private String salesmanName;

    /**
     * 业务员联系方式
     */
    @Schema(description = "业务员联系方式", example = "13111111111")
    private String salesmanContact;

    /**
     * 报价单总价
     */
    @Schema(description = "报价单的总价", example = "24.02")
    private BigDecimal totalPrice;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "测试大哥好")
    private String createdBy;

    /**
     * 删除状态
     */
    @Schema(description = "删除状态， normal正常，recycle已加入回收站，deleted永久删除", example = "normal")
    private String deleteStatus;

    /**
     * 报价状态
     */
    @Schema(description = "报价状态 quotation快速报价中，finish报价完成，close已终止，outTime超出报价有效期", example = "finish")
    private String quotationStatus;

    /**
     * 是否内部人员创建
     */
    @Schema(description = "是否内部人员创建的 true是 false不是", example = "false")
    private String isInsideCreated;

    /**
     * 创建报价单的内部员工编码
     */
    @Schema(description = "创建报价单的内部员工编码", example = "09785")
    private String insideEmployeeCode;

    /**
     * 创建报价单的员工名称
     */
    @Schema(description = "创建报价单的员工名称", example = "你猜")
    private String employeeName;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态，synchronized已同步，notSynchronized未同步", example = "synchronized")
    private String synchronizationStatus;

    /**
     * 同步时间
     */
    @Schema(description = "同步时间", example = "2022-12-02 10:54:12", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime synchronizeDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-12-02 10:54:12", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    /**
     * 报价单审核时间
     */
    @Schema(description = "报价单审核时间", example = "2022-12-02 10:54:12", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime examineDate;

    /**
     * 报价完成时间
     */
    @Schema(description = "报价完成时间", example = "2022-12-02 10:54:12", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime quotationCompletionDate;

    /**
     * 报价时效
     */
    @Schema(description = "报价时效", example = "1")
    private long quotationPeriod;

    /**
     * 是否需要审核
     */
    @Schema(description = "是否需要审核操作 true需要 false不需要", example = "false")
    private String isExamine;

    /**
     * 是否已转订单
     */
    @Schema(description = "是否已转订单 true已转 false没有转")
    private String transferOrder;
}
