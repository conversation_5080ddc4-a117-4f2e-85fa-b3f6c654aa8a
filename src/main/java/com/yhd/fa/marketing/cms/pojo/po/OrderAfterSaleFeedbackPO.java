package com.yhd.fa.marketing.cms.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleFeedbackPO.java, v 0.1 2025/6/26 16:28 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("fa_order_after_sale_feedback")
@EqualsAndHashCode(callSuper = true)
public class OrderAfterSaleFeedbackPO extends BaseEntity {

    /**
     * 售后单号
     */
    private String afterSaleNumber;

    /**
     * 满意度（1 - 5星，对应1 - 5值  ）
     */
    private Byte satisfaction;

    /**
     * 问题是否解决，1：已解决；0：未解决
     */
    private Byte problemSolved;

    /**
     * 整体感受，1：非常省心；2：省心；3：一般；4：费力；5：非常费力
     */
    private Byte overallFeeling;

    /**
     * 用户建议
     */
    private String suggestion;

}
