package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderInvoiceRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceResponseVO;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceService.java, v0.1 2023/2/23 10:05 yehuasheng Exp $
 */
public interface OrderInvoiceService {
    /**
     * 获取订单发票列表
     *
     * @param orderInvoiceRequestVO 请求订单发票参数
     * @return BusinessResponse<PageInfo < OrderInvoiceResponseVO>>
     */
    BusinessResponse<PageInfo<OrderInvoiceResponseVO>> getOrderInvoiceList(OrderInvoiceRequestVO orderInvoiceRequestVO);

    /**
     * 获取订单发票详情
     *
     * @param orderInvoiceId 订单发票id
     * @return BusinessResponse<OrderInvoiceDetailResponseVO>
     */
    BusinessResponse<OrderInvoiceDetailResponseVO> getOrderInvoiceInfo(String orderInvoiceId);

    /**
     * 同步订单发票
     *
     * @param orderInvoiceId 订单发票id
     * @return BusinessResponse<Void>
     */
    BusinessResponse<Void> synchronizationOrderInvoice(String orderInvoiceId);
}
