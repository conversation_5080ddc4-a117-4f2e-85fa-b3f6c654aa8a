package com.yhd.fa.marketing.cms.mapper.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.SaleCompanyRelationClickHouseDAO;
import com.yhd.fa.marketing.cms.mapper.SaleCompanyRelationClickHouseMapper;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationClickHousePO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: SaleCompanyRelationClickHouseMapperImpl.java, v 0.1 2025/5/13 08:56 JiangYuHong Exp $
 */
@Service
public class SaleCompanyRelationClickHouseMapperImpl extends MPJBaseServiceImpl<SaleCompanyRelationClickHouseDAO, SaleCompanyRelationClickHousePO> implements SaleCompanyRelationClickHouseMapper {

    @Async("MyTaskAsync")
    @DS("order_ch")
    @Override
    public void saveAllToClickhouseOrderDatabase(List<SaleCompanyRelationClickHousePO> list) {
        saveBatch(list);
    }
}
