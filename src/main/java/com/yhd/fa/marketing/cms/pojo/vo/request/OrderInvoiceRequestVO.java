package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.PageRequestVO;
import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.OrderInvoiceStatusConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceRequestVO.java, v0.1 2023/2/23 9:39 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderInvoiceRequestVO extends PageRequestVO {
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2022-01-01 10:10:10")
    private LocalDateTime startDateTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2022-01-01 10:10:10")
    private LocalDateTime endDateTime;

    /**
     * 发票单号
     */
    @Schema(description = "发票单号", example = "I2018072509075558215")
    private String orderInvoiceNumber;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 发票状态
     */
    @Schema(description = "发票状态", example = "invoiced")
    @ValueInEnum(matchTarget = {OrderInvoiceStatusConstant.INVOICED, OrderInvoiceStatusConstant.INVOICING, OrderInvoiceStatusConstant.SHIPPED, OrderInvoiceStatusConstant.CANCEL, CommonConstant.EMPTY}, message = "发票状态错误")
    private String orderInvoiceStatus;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态 true是 false不是", example = "true")
    @ValueInEnum(matchTarget = {CommonConstant.TRUE, CommonConstant.EMPTY, CommonConstant.FALSE}, message = "同步状态错误")
    private String synchronizationStatus;
}
