package com.yhd.fa.marketing.cms.schedule;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhd.common.pojo.dto.BaseDTO;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.configure.AutoReplyTemplateConfigure;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.dao.OrderCommentsDAO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.AddUserIntegralRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.BatchAddUserIntegralRequestVO;
import com.yhd.fa.marketing.cms.service.sao.IntegralCenterService;
import lombok.*;
import org.slf4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version Id: AutoReplyOrderCommentsSchedule.java, v 0.1 2023/8/8 9:44 JiangYuHong Exp $
 */
@Component
public class AutoReplyOrderCommentsSchedule {

    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(AutoReplyOrderCommentsSchedule.class.getName());
    private static final SecureRandom random = new SecureRandom();
    @Resource
    private OrderCommentsDAO orderCommentsDAO;
    @Resource
    private AutoReplyTemplateConfigure autoReplyTemplateConfigure;
    @Resource
    private IntegralCenterService integralCenterService;

    /**
     * 设置添加积分请求参数
     *
     * @param orderCommentsPO              评价数据
     * @param addUserIntegralRequestVOList 请求参数列表
     */
    private static void setIntegralRequestList(OrderCommentsPO orderCommentsPO, List<AddUserIntegralRequestVO> addUserIntegralRequestVOList) {
        addUserIntegralRequestVOList.add(
                AddUserIntegralRequestVO.builder()
                        .integralNumber(orderCommentsPO.getId())
                        .userCode(orderCommentsPO.getUserCode())
                        .platform(FaDocMarketingCmsConstant.PLATFORM_CODE)
                        .ruleName("订单评价赠送积分")
                        .integral(CommonConstant.TEN)
                        .operator("taskTimed")
                        .build()
        );
    }

    /**
     * 随机获取List中的一条数据
     *
     * @param list 待抽取数据的List
     * @param <T>  List中的元素类型
     * @return List中随机抽取的一条数据
     */
    public static <T> T getRandomElement(List<T> list) {
        int randomIndex = random.nextInt(list.size());
        return list.get(randomIndex);
    }

    @XxlJob("autoReplyOrderComments")
    public void exec() {

        logger.info("start auto reply order comments taskTimed.");
        //获取需要自动回复的数据
        AutoReplyDTO autoReplyData = getAutoReplyData();

        //获取自动回复的模板
        LinkedHashMap<String, List<String>> map = autoReplyTemplateConfigure.getMap();

        //判断自动回复的数据是否为空
        if (ObjectUtil.isNull(autoReplyData)) return;

        LambdaUpdateWrapper<OrderCommentsPO> updateWrapper = new LambdaUpdateWrapper<>();
        List<AddUserIntegralRequestVO> addUserIntegralRequestVOList = new ArrayList<>();


        logger.info("auto reply date: {}", autoReplyData);
        Optional.ofNullable(autoReplyData.getPraise()).ifPresent(e -> e.forEach(i -> {
            updateWrapper.clear();
            //获取随机回复内容
            String reply = getRandomElement(map.get("praise"));

            //更新数据
            updateOrderComments(updateWrapper, i.getId(), reply);
            //设置请求添加积分的参数
            setIntegralRequestList(i, addUserIntegralRequestVOList);
        }));

        Optional.ofNullable(autoReplyData.getBad()).ifPresent(e -> e.forEach(i -> {
            updateWrapper.clear();
            //获取随机回复内容
            String reply = getRandomElement(map.get("bad"));
            //更新数据
            updateOrderComments(updateWrapper, i.getId(), reply);
            //设置请求添加积分的参数
            setIntegralRequestList(i, addUserIntegralRequestVOList);
        }));

        //添加积分
        SpringUtil.getBean(AutoReplyOrderCommentsSchedule.class).addIntegral(addUserIntegralRequestVOList);
    }

    /**
     * 更新订单评价
     *
     * @param updateWrapper   updateWrapper
     * @param orderCommentsId 评价记录id
     * @param reply           回复内容
     */
    private void updateOrderComments(LambdaUpdateWrapper<OrderCommentsPO> updateWrapper, String orderCommentsId, String reply) {

        updateWrapper.eq(OrderCommentsPO::getId, orderCommentsId)
                .set(OrderCommentsPO::getReplied, CommonConstant.TRUE)
                .set(OrderCommentsPO::getReply, reply)
                .set(OrderCommentsPO::getRepliedDate, LocalDateTime.now())
                .set(OrderCommentsPO::getReplyPeople, CommonConstant.DASH + CommonConstant.DASH)
                .set(OrderCommentsPO::getBonusPoints, CommonConstant.TEN);

        orderCommentsDAO.update(null, updateWrapper);
    }

    /**
     * 获取需要自动回复的数据
     *
     * @return AutoReplyDTO
     */
    private AutoReplyDTO getAutoReplyData() {

        //获取当前时间前5分钟时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime before = now.minusMinutes(CommonConstant.FIVE);

        List<OrderCommentsPO> orderCommentsPOS = orderCommentsDAO.selectList(
                new LambdaQueryWrapper<OrderCommentsPO>()
                        .isNull(OrderCommentsPO::getComment)
                        .isNull(OrderCommentsPO::getImageUrl)
                        .eq(OrderCommentsPO::getReplied, CommonConstant.FALSE)
                        .le(OrderCommentsPO::getCreatedDate, before)
        );

        if (orderCommentsPOS.isEmpty()) return null;

        List<OrderCommentsPO> praiseList = new ArrayList<>();
        List<OrderCommentsPO> badList = new ArrayList<>();

        for (OrderCommentsPO comment : orderCommentsPOS) {
            //获取 【产品描述相符度】，【人员服务态度】，【产品交付时效】星级均为3-5颗星星,既大于等于3
            if (comment.getProductDescRating() >= CommonConstant.THREE &&
                    comment.getProductDeliveryRating() >= CommonConstant.THREE &&
                    comment.getPersonnelServiceRating() >= CommonConstant.THREE) {
                praiseList.add(comment);
            } else {
                //获取 【产品描述相符度】，【人员服务态度】，【产品交付时效】星级只要其中一项含为1-2颗星星
                badList.add(comment);
            }
        }

        return AutoReplyDTO.builder()
                .praise(praiseList)
                .bad(badList)
                .build();
    }

    /**
     * 添加积分
     *
     * @param addUserIntegralRequestVOList 请求参数
     */
    @Async
    public void addIntegral(List<AddUserIntegralRequestVO> addUserIntegralRequestVOList) {

        if (!addUserIntegralRequestVOList.isEmpty()) {
            logger.info("request integral center add integral");
            BatchAddUserIntegralRequestVO requestVO = BatchAddUserIntegralRequestVO.builder()
                    .list(addUserIntegralRequestVOList)
                    .build();
            integralCenterService.batchAddUserIntegral(requestVO);
        }

    }

    /**
     * 自动回复的DTO
     *
     * <AUTHOR>
     * @version Id: AutoReplyOrderCommentsSchedule.java, v 0.1 2023/8/8 9:44 JiangYuHong Exp $
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    private static class AutoReplyDTO extends BaseDTO {

        /**
         * 好评 3星及以上
         */
        private List<OrderCommentsPO> praise;

        /**
         * 差评 3星一下
         */
        private List<OrderCommentsPO> bad;


    }

}
