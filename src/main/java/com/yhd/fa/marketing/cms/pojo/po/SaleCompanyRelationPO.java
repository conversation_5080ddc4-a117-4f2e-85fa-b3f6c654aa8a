package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogPO.java, v0.1 2022/8/27 10:55 yehuasheng Exp $
 * 企业业务员关联表
 */
@Data
@TableName("sale_company_relation")
public class SaleCompanyRelationPO {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 业务员工号
     */
    private String salesNo;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 归属地DGYHD/SZYHD
     */
    private String ownershipCompany;

}
