package com.yhd.fa.marketing.cms.web.order;

import com.alibaba.nacos.common.http.param.MediaType;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.UriConstant;
import com.yhd.fa.marketing.cms.pojo.vo.request.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.CheckUserOrderResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.LastOrderDateResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInfoResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderListResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderService;
import com.yhd.fa.marketing.cms.util.SeaTaUtil;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @version Id: OrderController.java, v0.1 2022/12/2 14:21 yehuasheng Exp $
 */
@RestController
@RequestMapping(value = UriConstant.PREFIX + UriConstant.VERSION, produces = MediaType.APPLICATION_JSON)
@Tag(name = "订单接口", description = "订单包含订单列表 订单详情等操作")
public class OrderController {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderController.class.getName());

    /**
     * 订单服务
     */
    @Resource
    private OrderService orderService;

    /**
     * 订单列表
     *
     * @param orderListRequestVO 订单列表请求参数
     * @return BusinessResponse<PageInfo < OrderListResponseVO>>
     */
    @Operation(summary = "订单列表")
    @PostMapping(value = UriConstant.ORDER_LIST, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<PageInfo<OrderListResponseVO>> orderList(@RequestBody @Validated OrderListRequestVO orderListRequestVO) {
        logger.info("request get order list api. parameter orderListRequestVO:{}", orderListRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<PageInfo<OrderListResponseVO>> businessResponse = orderService.getOrderList(orderListRequestVO);
        logger.info("get order list api success response businessResponse:{}", businessResponse);
        logger.info("get order list api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 订单详情页
     *
     * @param orderId 订单号
     * @return BusinessResponse<OrderInfoResponseVO>
     */
    @Operation(summary = "订单详情页", parameters = {@Parameter(name = "orderId", description = "订单id", example = "1ca7a1c221c947b5bc4489dfd9097913", required = true)})
    @GetMapping(UriConstant.ORDER_DETAIL)
    public BusinessResponse<OrderInfoResponseVO> orderInfo(@RequestParam String orderId) {
        logger.info("request get order detail api parameter orderId:{}", orderId);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<OrderInfoResponseVO> businessResponse = orderService.getOrderInfo(orderId);
        logger.info("get order detail api success response businessResponse:{}", businessResponse);
        logger.info("get order detail api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 重新同步订单
     *
     * @param orderId 订单id
     * @return BusinessResponse<Void>
     */
    @Operation(summary = "重新同步订单", parameters = {@Parameter(name = "orderId", description = "订单id", example = "1b78cbbd91e64c329b7fb164b84561bc")})
    @GetMapping(value = UriConstant.SYNCHRONIZATION_CREATE_ORDER)
    public BusinessResponse<Void> synchronizationCreateOrder(@RequestParam String orderId) {
        logger.info("request fa doc synchronization create order api parameter orderId:{}", orderId);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<Void> businessResponse = orderService.synchronizationCreateOrder(orderId);
        logger.info("get fa doc synchronization create order api success response businessResponse:{}", businessResponse);
        logger.info("get fa doc synchronization create order api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 重新同步收款单
     *
     * @param orderNumber 订单号
     * @return BusinessResponse<Void>
     */
    @Operation(summary = "重新同步收款单", parameters = {@Parameter(name = "orderNumber", description = "订单号", example = "MYI0000198766030889")})
    @GetMapping(value = UriConstant.SYNCHRONIZATION_ORDER_COLLECTION)
    public BusinessResponse<Void> synchronizationOrderCollection(@RequestParam String orderNumber) {
        logger.info("request fa doc synchronization order collection api parameter orderNumber:{}", orderNumber);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<Void> businessResponse = orderService.synchronizationOrderCollection(orderNumber);
        logger.info("fa doc synchronization order collection api success response businessResponse:{}", businessResponse);
        logger.info("fa doc synchronization order collection api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 更新支付方式以及流水号
     *
     * @param updateOrderPaymentSerialNumberRequestVO 更新的支付方式
     * @return BusinessResponse<Void>
     */
    @GlobalTransactional(name = "updateOrderPaymentSerialNumber", rollbackFor = Exception.class)
    @Operation(summary = "更新支付方式以及流水号")
    @PutMapping(value = UriConstant.UPDATE_ORDER_PAYMENT_SERIAL_NUMBER, consumes = MediaType.APPLICATION_JSON)
    public BusinessResponse<Void> updateOrderPaymentSerialNumber(@RequestBody UpdateOrderPaymentSerialNumberRequestVO updateOrderPaymentSerialNumberRequestVO) {
        logger.info("request fa doc update order payment serial number parameter updateOrderPaymentSerialNumberRequestVO:{}.", updateOrderPaymentSerialNumberRequestVO);

        // 设置开始计算请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<Void> businessResponse = orderService.updateOrderPaymentSerialNumber(updateOrderPaymentSerialNumberRequestVO);
        logger.info("fa doc update order payment serial number api success response businessResponse:{}", businessResponse);
        logger.info("fa doc update order payment serial number api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        // 判断是否手动回滚
        if (!businessResponse.success()) {
            // 手动回滚
            SeaTaUtil.rollback(RootContext.getXID());
        }

        return businessResponse;
    }

    /**
     * 检查昨天用户是否有下单
     *
     * @param userCodeListRequestVO 用户编码集合
     * @return BusinessResponse<List < CheckUserOrderResponseVO>>
     */
    @PostMapping(value = UriConstant.CHECK_YESTERDAY_USER_ORDERS, consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @Operation(summary = "检查昨天用户是否有下单")
    public BusinessResponse<List<CheckUserOrderResponseVO>> checkUserOrder(@RequestBody @Validated UserCodeListRequestVO userCodeListRequestVO) {
        logger.info("request fa doc check user orders api. parameter UserCodeListRequestVO:{}", userCodeListRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<List<CheckUserOrderResponseVO>> businessResponse = orderService.checkUserOrder(userCodeListRequestVO);
        logger.info("fa doc check user orders api success response businessResponse:{}", businessResponse);
        logger.info("fa doc check user orders api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }

    /**
     * 更新订单砸金蛋状态
     * @param updateOrderEggStatusRequestVO 请求参数
     * @return BusinessResponse<Object>
     */
    @PutMapping(value = UriConstant.UPDATE_ORDER_EGG_STATUS, consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @Operation(summary = "更新订单砸金蛋状态")
    public BusinessResponse<Object> updateOrderEggStatus(@RequestBody @Validated UpdateOrderEggStatusRequestVO updateOrderEggStatusRequestVO) {
        logger.info("request fa doc update order egg status api. parameter UpdateOrderEggStatusRequestVO:{}", updateOrderEggStatusRequestVO);

        // 设置开始计算接口请求时间
        Stopwatch stopwatch = Stopwatch.createStarted();

        // 请求接口
        BusinessResponse<Object> businessResponse = orderService.updateOrderEggStatus(updateOrderEggStatusRequestVO);
        logger.info("fa doc update order egg status api success response businessResponse:{}", businessResponse);
        logger.info("fa doc update order egg status api cost:{}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }


    @PostMapping(value = UriConstant.LAST_ORDER_DATE, consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @Operation(summary = "根据用户编码获取最后下单日期")
    public BusinessResponse<List<LastOrderDateResponseVO>> getLastOrderDate(@RequestBody @Validated LastOrderDateRequestVO lastOrderDateRequestVO) {

        return orderService.getLastOrderDate(lastOrderDateRequestVO);
    }

    /**
     * 更新发票信息公司名称
     *
     * @param requestVO 请求参数
     * @return BusinessResponse
     */
    @PutMapping(value = UriConstant.UPDATE_INVOICE_INFO_COMPANY_NAME)
    public BusinessResponse<Object> updateInvoiceInfoCompanyName(@RequestBody  @Validated UpdateInvoiceCompanyNameRequestVO requestVO) {
        logger.info("request update invoice info company name api. parameter:{}", requestVO);

        Stopwatch stopwatch = Stopwatch.createStarted();

        BusinessResponse<Object> businessResponse = orderService.updateInvoiceInfoCompanyName(requestVO);
        logger.info("request update invoice info company name success. cots:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return businessResponse;
    }
}
