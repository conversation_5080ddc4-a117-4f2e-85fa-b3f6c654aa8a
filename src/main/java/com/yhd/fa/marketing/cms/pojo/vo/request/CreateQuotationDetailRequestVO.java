package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id: CreateQuotationDetailRequestVO.java, v0.1 2022/8/23 19:45 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "请求创建报价单的型号集合数据")
public class CreateQuotationDetailRequestVO extends BaseVO {
    /**
     * 客户型号
     */
    @Schema(description = "客户型号", required = true, example = "SAD01-D3-L100")
    @NotEmpty(message = "客户型号不能为空")
    @Length(min = 1, max = 255, message = "字符串不能超过255")
    private String customerModel;

    /**
     * 数量
     */
    @Schema(description = "数量", required = true, minimum = "1")
    @Min(value = 1, message = "数量不能少于1")
    private long quantity;

    /**
     * 客户产品名称
     */
    @Schema(description = "客户产品名称", example = "米思米导向轴")
    private String customerProductName;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "test")
    private String remark;

    /**
     * 文件url
     */
    @Schema(description = "文件url", example = "https://image.yhdfa.com/aa.png")
    private String fileUrl;

    /**
     * 文件标签
     */
    @Schema(description = "文件标签的key，用户上传附件后，进行文件标签去除")
    private String fileKey;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码", example = "100068")
    private String materialCode;

    /**
     * 类别编码
     */
    @Schema(description = "类别编码", example = "A01.01.01")
    private String categoryCode;
}
