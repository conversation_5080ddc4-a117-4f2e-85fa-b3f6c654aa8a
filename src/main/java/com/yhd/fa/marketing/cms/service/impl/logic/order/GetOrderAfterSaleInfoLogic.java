package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.dao.OrderAfterSaleFeedbackDAO;
import com.yhd.fa.marketing.cms.dao.OrderAfterSaleStatusDAO;
import com.yhd.fa.marketing.cms.dao.OrderErpLogisticsDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.OrderAfterSaleStatusEnum;
import com.yhd.fa.marketing.cms.enums.OrderAfterSaleTypeEnum;
import com.yhd.fa.marketing.cms.mapper.OrderAfterSaleMapper;
import com.yhd.fa.marketing.cms.pojo.po.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleDetailListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleFeedbackResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleStatusResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.BaseUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderAfterSaleInfoLogic.java, v0.1 2023/2/24 17:02 yehuasheng Exp $
 */
@Component
@RefreshScope
public class GetOrderAfterSaleInfoLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderAfterSaleInfoLogic.class.getName());
    /**
     * 图片域名
     */
    @Value("${image-config.domain}")
    String imageDomain;


    @Resource
    private OrderAfterSaleStatusDAO orderAfterSaleStatusDAO;

    @Resource
    private OrderAfterSaleMapper orderAfterSaleMapper;

    @Resource
    private OrderErpLogisticsDAO orderErpLogisticsDAO;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    @Resource
    private OrderAfterSaleFeedbackDAO orderAfterSaleFeedbackDAO;

    /**
     * 执行获取订单售后详情页
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<OrderAfterSaleDetailResponseVO>
     */
    public BusinessResponse<OrderAfterSaleDetailResponseVO> exec(String orderAfterSaleId) {
        logger.info("start exec get order after sale detail.");

        // 获取订单售后详情
        OrderAfterSaleDetailResponseVO orderAfterSaleInfo = new OrderAfterSaleDetailResponseVO();

        MPJLambdaWrapper<OrderAfterSalePO> queryWrapper = new MPJLambdaWrapper<OrderAfterSalePO>()
                                                            .selectAll(OrderAfterSalePO.class)
                                                            .eq(OrderAfterSalePO::getId, orderAfterSaleId);
        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderAfterSalePO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        OrderAfterSalePO orderAfterSalePO = orderAfterSaleMapper.getOneDeep(queryWrapper);

        // 判断是否为空
        if (ObjectUtil.isNull(orderAfterSalePO)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_AFTER_SALE_IS_NOT_EXISTS);
        }

        //获取状态节点列表
        List<OrderAfterSaleStatusPO> orderAfterSaleStatusPOS = orderAfterSaleStatusDAO.selectList(
                new LambdaQueryWrapper<OrderAfterSaleStatusPO>()
                        .eq(OrderAfterSaleStatusPO::getAfterSaleId, orderAfterSaleId)
                        .orderByDesc(OrderAfterSaleStatusPO::getStatusTime));

        BeanUtil.copyProperties(orderAfterSalePO, orderAfterSaleInfo);
        orderAfterSaleInfo.setOrderAfterSaleDetails(BeanUtil.copyToList(orderAfterSalePO.getOrderAfterSaleList(), OrderAfterSaleDetailListResponseVO.class));

        //设置状态节点列表
        if (CollUtil.isNotEmpty(orderAfterSaleStatusPOS)){
            orderAfterSaleInfo.setStatusList(BeanUtil.copyToList(orderAfterSaleStatusPOS, OrderAfterSaleStatusResponseVO.class));
        }

        // 查询售后评价
        orderAfterSaleInfo.setOrderAfterSaleFeedback(getOrderAfterSaleFeedback(orderAfterSaleInfo.getAfterSaleNumber()));

        // 设置订单取消其他值
        setOrderAfterSaleOtherValue(orderAfterSaleInfo);

        //设置物流信息
        setOrderAfterSaleLogistics(orderAfterSaleInfo);

        return BusinessResponse.ok(orderAfterSaleInfo);
    }

    /**
     * 获取售后评价
     *
     * @param afterSaleNumber 售后单号
     * @return OrderAfterSaleFeedbackResponseVO
     */
    private OrderAfterSaleFeedbackResponseVO getOrderAfterSaleFeedback(String afterSaleNumber) {

        OrderAfterSaleFeedbackPO afterSaleFeedbackPO = orderAfterSaleFeedbackDAO.selectOne(
                new LambdaQueryWrapper<OrderAfterSaleFeedbackPO>()
                        .eq(OrderAfterSaleFeedbackPO::getAfterSaleNumber, afterSaleNumber)
        );
        if (ObjectUtil.isNull(afterSaleFeedbackPO)) {
            return null;
        }

        return BeanUtil.copyProperties(afterSaleFeedbackPO, OrderAfterSaleFeedbackResponseVO.class);
    }


    /**
     * 设置订单售后其他值
     *
     * @param orderAfterSaleInfo 订单售后详情
     */
    private void setOrderAfterSaleOtherValue(OrderAfterSaleDetailResponseVO orderAfterSaleInfo) {
        logger.info("set order after sale other value.");

        // 设置用户
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(orderAfterSaleInfo.getUserCode(), orderAfterSaleInfo.getCompanyCode());
        Optional.ofNullable(userInfo).ifPresent(allUserInfoResponseVO -> {
            // 设置跟单
            Optional.ofNullable(allUserInfoResponseVO.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserResponseVO -> orderAfterSaleInfo.setMerchandiserName(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName()));
            // 设置业务员
            Optional.ofNullable(allUserInfoResponseVO.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> orderAfterSaleInfo.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName()));
        });

        // 订单售后状态
        Map<String, String> orderAfterSaleStatusMap = Arrays.stream(OrderAfterSaleStatusEnum.values()).collect(Collectors.toMap(OrderAfterSaleStatusEnum::getOrderAfterSaleStatus, OrderAfterSaleStatusEnum::getOrderAfterSaleStatusName));
        orderAfterSaleInfo.setOrderAfterSaleStatusName(orderAfterSaleStatusMap.get(orderAfterSaleInfo.getAfterSaleStatus()));

        // 订单售后类型
        Map<String, String> orderAfterSaleTypeMap = Arrays.stream(OrderAfterSaleTypeEnum.values()).collect(Collectors.toMap(OrderAfterSaleTypeEnum::getOrderAfterSaleType, OrderAfterSaleTypeEnum::getOrderAfterSaleTypeName));
        orderAfterSaleInfo.setOrderAfterSaleTypeName(orderAfterSaleTypeMap.get(orderAfterSaleInfo.getAfterSaleType()));

        //添加图片域名
        orderAfterSaleInfo.setImagePath(BaseUtil.addDomainToImageUrls(imageDomain, orderAfterSaleInfo.getImagePath()));
    }


    /**
     * 设置售后物流信息
     * @param orderAfterSaleInfo 售后详情响应体
     */
    private void setOrderAfterSaleLogistics(OrderAfterSaleDetailResponseVO orderAfterSaleInfo){

        OrderErpLogisticsPO afterSaleLogisticsInfo = orderErpLogisticsDAO.selectOne(
                new LambdaQueryWrapper<OrderErpLogisticsPO>()
                        .eq(OrderErpLogisticsPO::getOrderNumber, orderAfterSaleInfo.getAfterSaleNumber())
                        .eq(OrderErpLogisticsPO::getTargetType, "afterSale")
        );

        if (!ObjectUtils.isEmpty(afterSaleLogisticsInfo)){
            orderAfterSaleInfo.setLogisticsCompany(afterSaleLogisticsInfo.getLogisticsCompany());
            orderAfterSaleInfo.setLogisticsNumber(afterSaleLogisticsInfo.getExpressNumber());
        }
    }
}
