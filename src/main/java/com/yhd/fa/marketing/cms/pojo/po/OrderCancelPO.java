package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_cancel")
public class OrderCancelPO extends BaseEntity {
    /**
     * 取消单号
     */
    private String cancelNumber;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * erp编码
     */
    private String erpCode;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 取消总金额
     */
    private BigDecimal totalMoney;

    /**
     * 同步状态
     */
    private String syncStatus;

    /**
     * 取消类型  part部分取消 all 整单取消
     */
    private String cancelType;

    /**
     * 取消状态 canceling 取消中 processed 已处理 turnDown 驳回 refunded 已退款
     */
    private String cancelStatus;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 驳回原因
     */
    private String notAgree;

    /**
     * 审核时间(ERP处理时间)
     */
    private LocalDateTime handelDate;

    /**
     * 处理时间(同步时间)
     */
    private LocalDateTime syncDate;

    /**
     * 退款时间
     */
    private LocalDateTime refundDate;

    /**
     * 组织架构全链路
     */
    private String departmentLink;

    /**
     * 所属地区 DGYHD东莞 SZYHD苏州
     */
    private String territory;

    /**
     * 同步错误信息
     */
    private String syncErrMsg;

    /**
     * 是否内部人员创建
     */
    private String isInsideCreated;

    /**
     * 内部员工编号
     */
    private String insideEmployeeCode;
}

