package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.common.util.UUIDUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.*;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.OrderDetailStatusEnum;
import com.yhd.fa.marketing.cms.mapper.*;
import com.yhd.fa.marketing.cms.pojo.po.*;
import com.yhd.fa.marketing.cms.pojo.vo.request.UpdateOrderPaymentSerialNumberRequestVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: UpdateOrderPaymentSerialNumberLogic.java, v0.1 2023/3/30 10:15 yehuasheng Exp $
 */
@Component
public class UpdateOrderPaymentSerialNumberLogic {
    /**
     * 日志
     */
    public static final Logger logger = LogUtils.getLogger(UpdateOrderPaymentSerialNumberLogic.class.getName());

    /**
     * 订单mapper
     */
    @Resource
    private OrderMapper orderMapper;

    /**
     * 订单收款单mapper
     */
    @Resource
    private OrderCollectionLogMapper orderCollectionLogMapper;

    /**
     * 订单支付详情
     */
    @Resource
    private OrderPayDetailMapper orderPayDetailMapper;

    /**
     * 订单支付明细
     */
    @Resource
    private OrderPayDetailListMapper orderPayDetailListMapper;

    /**
     * 订单明细mapper
     */
    @Resource
    private OrderListMapper orderListMapper;

    /**
     * 更新订单支付方式以及流水号
     *
     * @param updateOrderPaymentSerialNumberRequestVO 更新订单支付方式以及流水号参数
     * @return BusinessResponse<Void>
     */
    public BusinessResponse<Void> exec(UpdateOrderPaymentSerialNumberRequestVO updateOrderPaymentSerialNumberRequestVO) {
        logger.info("start exec update order payment serial number logic.");

        // 查询订单是否存在
        OrderPO orderInfo = getOrderInfo(updateOrderPaymentSerialNumberRequestVO.getOrderNumber());
        if (null == orderInfo) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_IS_NOT_EXISTS);
        }

        // 判断订单是否已取消
        if (StringUtils.equals(orderInfo.getOrderStatus(), OrderStatusConstant.CANCEL)
                || StringUtils.equals(orderInfo.getOrderStatus(), OrderStatusConstant.CANCELING)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_IS_CANCEL);
        }

        // 判断订单是否现金支付
        if (StringUtils.equals(orderInfo.getSettlementType(), SettlementTypeConstant.OFFLINE)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_SETTLEMENT_TYPE_IS_OFFLINE);
        }

        // 判断价格是否正确
        if (!StringUtils.equals(orderInfo.getPaymentType(), PaymentTypeConstant.BANK_TRANSFER)
                && updateOrderPaymentSerialNumberRequestVO.getPaymentPrice().compareTo(orderInfo.getPayablePrice()) != CommonConstant.ZERO) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_PAYMENT_PRICE_ERROR);
        }

        // 更新订单支付方式以及支付信息
        if (!updateOrderPaymentInfo(orderInfo, updateOrderPaymentSerialNumberRequestVO)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.UPDATE_ORDER_PAYMENT_INFO_FAIL);
        }

        // 添加收款记录
        return saveOrderCollectionLog(updateOrderPaymentSerialNumberRequestVO, orderInfo) ? BusinessResponseCommon.ok(null) : BusinessResponseCommon.fail(FaDocMarketingResponseEnum.INSERT_ORDER_COLLECTION_LOG_FAIL);
    }

    /**
     * 获取订单详情
     *
     * @param orderNumber 订单号
     * @return OrderPO
     */
    private OrderPO getOrderInfo(String orderNumber) {
        logger.info("get order info.");

        if (StringUtils.isBlank(orderNumber)) return null;

        // 查询订单详情
        return orderMapper
                .getOne(new LambdaQueryWrapper<OrderPO>()
                        .eq(OrderPO::getOrderNumber, orderNumber)
                        .select(OrderPO::getId, OrderPO::getOrderNumber, OrderPO::getSettlementType, OrderPO::getPaymentType, OrderPO::getPaymentStatus, OrderPO::getOrderStatus, OrderPO::getPayablePrice, OrderPO::getTerritory)
                        .last(FaDocMarketingCmsConstant.LIMIT));
    }

    /**
     * 更新订单支付类型以及订单支付状态
     *
     * @param orderInfo                               订单详情
     * @param updateOrderPaymentSerialNumberRequestVO 更新内容
     * @return boolean
     */
    private boolean updateOrderPaymentInfo(OrderPO orderInfo, UpdateOrderPaymentSerialNumberRequestVO updateOrderPaymentSerialNumberRequestVO) {
        logger.info("update order payment info.");

        // 设置更新条件以及更新内容
        LambdaUpdateWrapper<OrderPO> updateWrapper = new LambdaUpdateWrapper<OrderPO>()
                .eq(OrderPO::getId, orderInfo.getId())
                .set(OrderPO::getPaymentType, updateOrderPaymentSerialNumberRequestVO.getPaymentType())
                .set(OrderPO::getTradeNo, updateOrderPaymentSerialNumberRequestVO.getSerialNumber())
                .set(OrderPO::getMchOrderNo, updateOrderPaymentSerialNumberRequestVO.getMchOrderNo())
                .set(OrderPO::getUpdatedDate, LocalDateTime.now())
                .set(OrderPO::getUpdatedBy, FaDocMarketingCmsConstant.ADMIN_STRING);
        // 如果支付方式不是银行转账的则需要更新支付时间和支付状态
        if (!StringUtils.equals(updateOrderPaymentSerialNumberRequestVO.getPaymentType(), PaymentTypeConstant.BANK_TRANSFER)) {
            updateWrapper.set(OrderPO::getPayDate, null == updateOrderPaymentSerialNumberRequestVO.getPaymentDateTime() ? LocalDateTime.now() : updateOrderPaymentSerialNumberRequestVO.getPaymentDateTime())
                    .set(OrderPO::getPaymentStatus, PaymentStatusConstant.PAID);
        }

        return orderMapper.update(null, updateWrapper);
    }

    /**
     * 添加收款单
     *
     * @param updateOrderPaymentSerialNumberRequestVO 更新的数据
     * @param orderInfo                               订单详情
     * @return boolean
     */
    private boolean saveOrderCollectionLog(UpdateOrderPaymentSerialNumberRequestVO updateOrderPaymentSerialNumberRequestVO, OrderPO orderInfo) {
        logger.info("save order collection log .");

        // 设置查询条件
        LambdaQueryWrapper<OrderCollectionLogPO> queryWrapper = new LambdaQueryWrapper<OrderCollectionLogPO>()
                .eq(OrderCollectionLogPO::getOrderNumber, updateOrderPaymentSerialNumberRequestVO.getOrderNumber())
                .eq(OrderCollectionLogPO::getPaymentType, updateOrderPaymentSerialNumberRequestVO.getPaymentType());
        // 判断支付类型是否为银行转账，如果不是则添加流水号查询
        if (!StringUtils.equals(updateOrderPaymentSerialNumberRequestVO.getPaymentType(), PaymentTypeConstant.BANK_TRANSFER)) {
            queryWrapper.eq(OrderCollectionLogPO::getSerialNumber, updateOrderPaymentSerialNumberRequestVO.getSerialNumber());
        }

        // 查询订单收款单是否有该记录
        long count = orderCollectionLogMapper.count(queryWrapper);

        // 如果是空则添加收款记录
        if (count == CommonConstant.ZERO) {
            String orderPayDetailId = saveOrderPayDetail(orderInfo);
            if (StringUtils.isBlank(orderPayDetailId)) {
                return false;
            }

            OrderCollectionLogPO orderCollectionLogPO = OrderCollectionLogPO
                    .builder()
                    .orderPayDetailId(orderPayDetailId)
                    .orderNumber(orderInfo.getOrderNumber())
                    .paymentPrice(orderInfo.getPayablePrice())
                    .paymentType(updateOrderPaymentSerialNumberRequestVO.getPaymentType())
                    .serialNumber(updateOrderPaymentSerialNumberRequestVO.getSerialNumber())
                    .mchOrderNo(updateOrderPaymentSerialNumberRequestVO.getMchOrderNo())
                    .paymentDate(null == updateOrderPaymentSerialNumberRequestVO.getPaymentDateTime() ? LocalDateTime.now() : updateOrderPaymentSerialNumberRequestVO.getPaymentDateTime())
                    .appId(StringUtils.equals(orderInfo.getTerritory(), "DGYHD") ? "GB2vYd" : "T3Ks8E")
                    .downErpStatus(StringUtils.equals(updateOrderPaymentSerialNumberRequestVO.getPaymentType(), PaymentTypeConstant.BANK_TRANSFER) ? "notRequired" : "wait")
                    .synchronizationsNumber(CommonConstant.ZERO)
                    .synchronizationsMessage(null)
                    .build();
            orderCollectionLogPO.setId(UUIDUtils.getStringUUID());
            orderCollectionLogPO.setCreatedDate(LocalDateTime.now());
            orderCollectionLogPO.setCreatedBy(FaDocMarketingCmsConstant.ADMIN_STRING);
            return orderCollectionLogMapper.save(orderCollectionLogPO);
        }

        return true;
    }

    /**
     * 保存支付记录
     *
     * @param orderInfo 订单详情
     * @return String
     */
    private String saveOrderPayDetail(OrderPO orderInfo) {
        logger.info("save order pay detail.");

        // 查询订单明细
        List<OrderListPO> orderList = orderListMapper
                .list(new LambdaQueryWrapper<OrderListPO>()
                        .eq(OrderListPO::getOrderId, orderInfo.getId())
                        .notIn(OrderListPO::getOrderDetailStatus, OrderDetailStatusEnum.CANCEL.getOrderDetailStatus(), OrderDetailStatusEnum.CANCELING.getOrderDetailStatus()));

        if (CollUtil.isEmpty(orderList)) {
            logger.error("get order list is empty.");
            return null;
        }

        // 设置订单支付详情id
        String id = UUIDUtils.getStringUUID();
        LocalDateTime nowDate = LocalDateTime.now();

        // 设置主表
        OrderPayDetailPO orderPayDetailInfo = OrderPayDetailPO
                .builder()
                .orderNumber(orderInfo.getOrderNumber())
                .totalPrice(orderInfo.getPayablePrice())
                .build();
        orderPayDetailInfo.setCreatedBy(FaDocMarketingCmsConstant.ADMIN_STRING);
        orderPayDetailInfo.setId(id);
        orderPayDetailInfo.setCreatedDate(nowDate);

        // 添加主表信息
        if (orderPayDetailMapper.save(orderPayDetailInfo)) {
            List<OrderPayDetailListPO> orderPayDetailList = orderList
                    .stream()
                    .map(orderListPO -> {
                        // 设置id
                        String ids = UUIDUtils.getStringUUID();

                        OrderPayDetailListPO orderPayDetailListPO = OrderPayDetailListPO
                                .builder()
                                .orderPayDetailId(id)
                                .orderNumber(orderListPO.getOrderNumber())
                                .orderSort(orderListPO.getSortId())
                                .model(orderListPO.getProductModel())
                                .customerModel(orderListPO.getCustomerModel())
                                .quantity(orderListPO.getQuantity())
                                .costPrice(orderListPO.getPrice())
                                .discountPrice(orderListPO.getDiscountPrice())
                                .taxDiscountPrice(orderListPO.getTaxDiscountPrice())
                                .totalPrice(orderListPO.getTotalPrice())
                                .unit(orderListPO.getUnit())
                                .build();

                        orderPayDetailListPO.setId(ids);
                        orderPayDetailListPO.setCreatedDate(nowDate);
                        orderPayDetailListPO.setCreatedBy(FaDocMarketingCmsConstant.ADMIN_STRING);

                        return orderPayDetailListPO;
                    })
                    .collect(Collectors.toList());

            orderPayDetailListMapper.saveBatch(orderPayDetailList);
        }

        return id;
    }
}
