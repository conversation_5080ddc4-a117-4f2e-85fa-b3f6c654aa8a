package com.yhd.fa.marketing.cms.service.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.EnquiryLogListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryLogListResponseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: EnquiryLogService.java, v0.1 2022/12/8 11:15 yehuasheng Exp $
 */
public interface EnquiryLogService {
    /**
     * 获取查价单集合
     *
     * @param enquiryLogListRequestVO 查价单参数
     * @return BusinessResponse<PageInfo < EnquiryLogListResponseVO>>
     */
    BusinessResponse<PageInfo<EnquiryLogListResponseVO>> getEnquiryLogList(EnquiryLogListRequestVO enquiryLogListRequestVO);

    BusinessResponse<String> enquiryLogAdd(List<EnquiryLogPO> enquiryLogPOList);

    void addSync(List<EnquiryLogPO> enquiryLogPOList);
}
