package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order_pay_detail_list")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderPayDetailListPO extends BaseEntity {
    /**
     * 付款记录id
     */
    private String orderPayDetailId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单序号
     */
    private Integer orderSort;

    /**
     * 型号
     */
    private String model;

    /**
     * 客户型号
     */
    private String customerModel;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 数量
     */
    private long quantity;

    /**
     * 原价
     */
    private BigDecimal costPrice;

    /**
     * 折扣价
     */
    private BigDecimal discountPrice;

    /**
     * 含税折扣价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 单位
     */
    private String unit;
}

