package com.yhd.fa.marketing.cms.mapper.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.QuotationListDAO;
import com.yhd.fa.marketing.cms.mapper.QuotationListMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version Id: QuotationListMapperImpl.java, v0.1 2022/12/6 10:20 yehuasheng Exp $
 */
@Service
@DS("quotation")
public class QuotationListMapperImpl extends MPJBaseServiceImpl<QuotationListDAO, QuotationListPO> implements QuotationListMapper {
}
