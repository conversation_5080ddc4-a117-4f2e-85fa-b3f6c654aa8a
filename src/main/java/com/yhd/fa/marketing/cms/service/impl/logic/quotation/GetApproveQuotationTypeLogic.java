package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionTypeRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationTypeResponseVO;
import com.yhd.fa.marketing.cms.sao.ProductCenterCmsSAO;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: GetApproveQuotationTypeLogic.java, v0.1 2022/12/22 10:31 yehuasheng Exp $
 */
@Component
public class GetApproveQuotationTypeLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetApproveQuotationTypeLogic.class.getName());

    /**
     * 产品中心sao
     */
    @Resource
    private ProductCenterCmsSAO productCenterCmsSAO;

    /**
     * 获取产品分类 代码编码
     *
     * @param approveQuotationSelectionTypeRequestVO 产品分类代码编码参数
     * @return BusinessResponse<List < ApproveQuotationTypeResponseVO>>
     */
    public BusinessResponse<List<ApproveQuotationTypeResponseVO>> exec(ApproveQuotationSelectionTypeRequestVO approveQuotationSelectionTypeRequestVO) {
        logger.info("start exec get product type logic.");

        // 执行调用远程逻辑
        return productCenterCmsSAO.getProductTypeCode(approveQuotationSelectionTypeRequestVO);
    }
}
