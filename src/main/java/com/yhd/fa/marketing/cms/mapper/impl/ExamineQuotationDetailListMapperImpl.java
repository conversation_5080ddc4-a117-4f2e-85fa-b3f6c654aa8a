package com.yhd.fa.marketing.cms.mapper.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.ExamineQuotationDetailListDAO;
import com.yhd.fa.marketing.cms.mapper.ExamineQuotationDetailListMapper;
import com.yhd.fa.marketing.cms.pojo.po.ExamineQuotationDetailListPO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version Id: ExamineQuotationDetailListMapperImpl.java, v0.1 2023/6/26 12:11 yehuasheng Exp $
 */
@Service
@DS("quotation")
public class ExamineQuotationDetailListMapperImpl extends MPJBaseServiceImpl<ExamineQuotationDetailListDAO, ExamineQuotationDetailListPO> implements ExamineQuotationDetailListMapper {
}
