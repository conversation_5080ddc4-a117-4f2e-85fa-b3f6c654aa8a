package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderCancelResponseVO.java, v0.1 2023/2/21 8:47 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderCancelResponseVO extends BaseVO {
    /**
     * 订单取消id
     */
    @Schema(description = "订单取消id", example = "5012551d99714d3e93c6f649aa87dd1d")
    private String orderCancelId;

    /**
     * 订单取消单号
     */
    @Schema(description = "订单取消单号", example = "SQ20221124102914QDJN")
    private String orderCancelNumber;

    /**
     * 订单id
     */
    @Schema(description = "订单id", example = "a83e6926e3034c51949564ee4a697e7f")
    private String orderId;

    /**
     * 取消状态 canceling 取消中 processed 已处理 turnDown 驳回 refunded 已退款
     */
    @Schema(description = "取消状态 canceling 取消中 processed 已处理 turnDown 驳回 refunded 已退款", example = "canceling")
    private String orderCancelStatus;

    /**
     * 取消状态名称
     */
    @Schema(description = "取消状态名称", example = "取消中")
    private String orderCancelStatusName;

    /**
     * 用户编码
     */
    @Schema(description = "订单采购人编码", example = "139779")
    private String userCode;

    /**
     * 用户名称
     */
    @Schema(description = "用户")
    private String userName;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "HA5814973528")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "潮州三环（集团）股份有限公司")
    private String companyName;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "YI202210311644369779HFMV")
    private String orderNumber;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2021-01-29 10:49:14")
    private LocalDateTime createdDate;

    /**
     * 订单取消类型
     */
    @Schema(description = "订单取消类型 part部分取消 all 整单取消", example = "all")
    private String orderCancelType;

    /**
     * 订单取消类型名称
     */
    @Schema(description = "订单取消类型名称", example = "整单取消")
    private String orderCancelTypeName;

    /**
     * 跟单员
     */
    @Schema(description = "跟单员", example = "你猜")
    private String merchandiser;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "1311111111")
    private String merchandiserContact;

    /**
     * 业务员名称
     */
    @Schema(description = "业务员名称", example = "01111/林杏金")
    private String salesmanName;

    /**
     * 业务员联系方式
     */
    @Schema(description = "业务员联系方式", example = "13111111111")
    private String salesmanContact;

    /**
     * 是否内部人员帮客户申请 true是 false不是
     */
    @Schema(description = "是否内部人员帮客户申请 true是 false不是", example = "false")
    private String isInsideCreated;

    /**
     * 内部人员工号
     */
    @Schema(description = "内部人员工号", example = "yhd575")
    private String insideEmployeeCode;

    /**
     * 内部人员名称
     */
    @Schema(description = "内部人员名称", example = "最帅的那位")
    private String insideEmployeeName;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态 success已同步 fail同步失败 wait等待同步 synchronizing同步中", example = "success")
    private String syncStatus;

    /**
     * 同步返回的信息
     */
    @Schema(description = "同步返回的信息", example = "success")
    private String syncErrMsg;
}
