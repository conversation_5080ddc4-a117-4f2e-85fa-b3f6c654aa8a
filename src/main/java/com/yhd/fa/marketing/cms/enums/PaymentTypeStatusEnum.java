package com.yhd.fa.marketing.cms.enums;

import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.constant.PaymentTypeConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: PaymentTypeStatusEnum.java, v0.1 2023/2/20 8:43 yehuasheng Exp $
 */
@Getter
public enum PaymentTypeStatusEnum {
    ALL(CommonConstant.EMPTY, "全部"),
    ALI_PAY(PaymentTypeConstant.ALI_PAY, "支付宝支付"),
    WECHAT_PAY(PaymentTypeConstant.WECHAT_PAY, "微信支付"),
    UNION_PAY(PaymentTypeConstant.UNION_PAY, "企业网银"),
    UNKNOWN(PaymentTypeConstant.UNKNOWN, "未支付"),
    MONTHLY(PaymentTypeConstant.MONTHLY, "信用月结"),
    BANK_TRANSFER(PaymentTypeConstant.BANK_TRANSFER, "线下支付"),
    MONTHLY_KNOT_30(PaymentTypeConstant.MONTHLY_KNOT_30, "信用月结"),
    MONTHLY_KNOT_60(PaymentTypeConstant.MONTHLY_KNOT_60, "信用月结"),
    MONTHLY_KNOT_90(PaymentTypeConstant.MONTHLY_KNOT_90, "信用月结"),
    ;

    private final String paymentTypeStatus;
    private final String paymentTypeStatusCn;

    PaymentTypeStatusEnum(String paymentTypeStatus, String paymentTypeStatusCn) {
        this.paymentTypeStatus = paymentTypeStatus;
        this.paymentTypeStatusCn = paymentTypeStatusCn;
    }
}
