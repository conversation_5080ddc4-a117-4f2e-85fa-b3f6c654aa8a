package com.yhd.fa.marketing.cms.util;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.OrderStatusConstant;
import com.yhd.fa.marketing.cms.pojo.po.*;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderInvoiceResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderListResponseVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: OrderUtil.java, v0.1 2023/2/20 14:14 yehuasheng Exp $
 */
public class OrderUtil {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderUtil.class.getName());

    private OrderUtil() {
    }

    /**
     * 设置查询的字段
     *
     * @return MPJLambdaWrapper<OrderPO>
     */
    public static MPJLambdaWrapper<OrderPO> setQueryWrapperSelectAs() {
        logger.info("set query wrapper select as name.");

        // 设置
        return new MPJLambdaWrapper<OrderPO>()
                .selectAs(OrderPO::getId, OrderListResponseVO::getOrderId)
                .selectAs(OrderPO::getOrderNumber, OrderListResponseVO::getOrderNumber)
                .selectAs(OrderPO::getOrderStatus, OrderListResponseVO::getOrderStatus)
                .selectAs(OrderPO::getCancelStatus, OrderListResponseVO::getCancelStatus)
                .selectAs(OrderPO::getDeleteStatus, OrderListResponseVO::getDeleteStatus)
                .selectAs(OrderPO::getCompanyCode, OrderListResponseVO::getCompanyCode)
                .selectAs(OrderPO::getCompanyName, OrderListResponseVO::getCompanyName)
                .selectAs(OrderPO::getUserCode, OrderListResponseVO::getUserCode)
                .selectAs(OrderPO::getSettlementType, OrderListResponseVO::getSettlementType)
                .selectAs(OrderPO::getPaymentStatus, OrderListResponseVO::getPaymentStatus)
                .selectAs(OrderPO::getPaymentType, OrderListResponseVO::getPaymentType)
                .selectAs(OrderPO::getTotalPrice, OrderListResponseVO::getTotalPrice)
                .selectAs(OrderPO::getPayablePrice, OrderListResponseVO::getPayablePrice)
                .selectAs(OrderPO::getSynchronizeStatus, OrderListResponseVO::getSynchronizeStatus)
                .selectAs(OrderPO::getSynchronizeMessage, OrderListResponseVO::getSynchronizeMessage)
                .selectAs(OrderPO::getOrderChannel, OrderListResponseVO::getOrderChannel)
                .selectAs(OrderPO::getOrderSource, OrderListResponseVO::getOrderSource)
                .selectAs(OrderPO::getCreatedDate, OrderListResponseVO::getCreatedDate)
                .selectAs(OrderPO::getTradeNo, OrderListResponseVO::getTradeNo)
                .selectAs(OrderCommentsPO::getRemark, OrderCommentsResponseVO::getRemark);
    }

    /**
     * 设置查询的字段
     *
     * @return MPJLambdaWrapper<OrderPO>
     */
    public static MPJLambdaWrapper<OrderClickHousePO> setClickHouseQueryWrapperSelectAs() {
        logger.info("set click house query wrapper select as name.");

        // 设置
        return new MPJLambdaWrapper<OrderClickHousePO>()
                .selectAs(OrderClickHousePO::getId, OrderListResponseVO::getOrderId)
                .selectAs(OrderClickHousePO::getOrderNumber, OrderListResponseVO::getOrderNumber)
                .selectAs(OrderClickHousePO::getOrderStatus, OrderListResponseVO::getOrderStatus)
                .selectAs(OrderClickHousePO::getCancelStatus, OrderListResponseVO::getCancelStatus)
                .selectAs(OrderClickHousePO::getDeleteStatus, OrderListResponseVO::getDeleteStatus)
                .selectAs(OrderClickHousePO::getCompanyCode, OrderListResponseVO::getCompanyCode)
                .selectAs(OrderClickHousePO::getCompanyName, OrderListResponseVO::getCompanyName)
                .selectAs(OrderClickHousePO::getUserCode, OrderListResponseVO::getUserCode)
                .selectAs(OrderClickHousePO::getSettlementType, OrderListResponseVO::getSettlementType)
                .selectAs(OrderClickHousePO::getPaymentStatus, OrderListResponseVO::getPaymentStatus)
                .selectAs(OrderClickHousePO::getPaymentType, OrderListResponseVO::getPaymentType)
                .selectAs(OrderClickHousePO::getTotalPrice, OrderListResponseVO::getTotalPrice)
                .selectAs(OrderClickHousePO::getPayablePrice, OrderListResponseVO::getPayablePrice)
                .selectAs(OrderClickHousePO::getSynchronizeStatus, OrderListResponseVO::getSynchronizeStatus)
                .selectAs(OrderClickHousePO::getSynchronizeMessage, OrderListResponseVO::getSynchronizeMessage)
                .selectAs(OrderClickHousePO::getOrderChannel, OrderListResponseVO::getOrderChannel)
                .selectAs(OrderClickHousePO::getOrderSource, OrderListResponseVO::getOrderSource)
                .selectAs(OrderClickHousePO::getCreatedDate, OrderListResponseVO::getCreatedDate)
                .selectAs(OrderClickHousePO::getTradeNo, OrderListResponseVO::getTradeNo)
                .selectAs(OrderCommentsPO::getRemark, OrderCommentsResponseVO::getRemark);
    }

    /**
     * 设置订单取消查询的字段
     *
     * @return MPJLambdaWrapper<OrderCancelPO>
     */
    public static MPJLambdaWrapper<OrderCancelPO> setOrderCancelQueryWrapperSelectAs() {
        logger.info("set order cancel query wrapper.");

        // 设置
        return new MPJLambdaWrapper<OrderCancelPO>()
                .selectAs(OrderCancelPO::getId, OrderCancelResponseVO::getOrderCancelId)
                .selectAs(OrderCancelPO::getCancelNumber, OrderCancelResponseVO::getOrderCancelNumber)
                .selectAs(OrderCancelPO::getCancelStatus, OrderCancelResponseVO::getOrderCancelStatus)
                .selectAs(OrderCancelPO::getUserCode, OrderCancelResponseVO::getUserCode)
                .selectAs(OrderCancelPO::getCompanyCode, OrderCancelResponseVO::getCompanyCode)
                .selectAs(OrderCancelPO::getCompanyName, OrderCancelResponseVO::getCompanyName)
                .selectAs(OrderCancelPO::getOrderNumber, OrderCancelResponseVO::getOrderNumber)
                .selectAs(OrderCancelPO::getCreatedDate, OrderCancelResponseVO::getCreatedDate)
                .selectAs(OrderCancelPO::getCancelType, OrderCancelResponseVO::getOrderCancelType);
    }

    /**
     * 设置订单发票查询字段
     *
     * @return MPJLambdaWrapper<OrderInvoicePO>
     */
    public static MPJLambdaWrapper<OrderInvoicePO> setOrderInvoiceQueryWrapperSelectAs() {
        logger.info("set order invoice query wrapper select as.");

        // 设置
        return new MPJLambdaWrapper<OrderInvoicePO>()
                .selectAs(OrderInvoicePO::getId, OrderInvoiceResponseVO::getOrderInvoiceId)
                .selectAs(OrderInvoicePO::getInvoiceNumber, OrderInvoiceResponseVO::getOrderInvoiceNumber)
                .selectAs(OrderInvoicePO::getInvoiceCode, OrderInvoiceResponseVO::getOrderInvoiceCode)
                .selectAs(OrderInvoicePO::getUserCode, OrderInvoiceResponseVO::getUserCode)
                .selectAs(OrderInvoicePO::getCompanyCode, OrderInvoiceResponseVO::getCompanyCode)
                .selectAs(OrderInvoicePO::getInvCustomerName, OrderInvoiceResponseVO::getCompanyName)
                .selectAs(OrderInvoicePO::getCreatedDate, OrderInvoiceResponseVO::getCreatedDate)
                .selectAs(OrderInvoicePO::getInvoiceType, OrderInvoiceResponseVO::getOrderInvoiceType)
                .selectAs(OrderInvoicePO::getInvoiceStatus, OrderInvoiceResponseVO::getOrderInvoiceStatus)
                .selectAs(OrderInvoicePO::getInvoiceNature, OrderInvoiceResponseVO::getOrderInvoiceNature)
                .selectAs(OrderInvoicePO::getSendErpStatus, OrderInvoiceResponseVO::getSynchronizationStatus)
                .selectAs(OrderInvoicePO::getSendErpTime, OrderInvoiceResponseVO::getSynchronizationDate)
                .selectAs(OrderInvoicePO::getErrorMsg, OrderInvoiceResponseVO::getSynchronizationMessage)
                .selectAs(OrderInvoicePO::getPrice, OrderInvoiceResponseVO::getOrderInvoiceTotalPrice);
    }

    /**
     * 设置订单评论查询字段
     *
     * @return MPJLambdaWrapper<OrderCommentsPO>
     */
    public static MPJLambdaWrapper<OrderCommentsPO> setOrderCommentsSelectAs() {
        logger.info("set order comments select as.");

        return new MPJLambdaWrapper<OrderCommentsPO>()
                .selectAs(OrderCommentsPO::getId, OrderCommentsResponseVO::getOrderCommentsId)
                .selectAs(OrderCommentsPO::getOrderId, OrderCommentsResponseVO::getOrderId)
                .selectAs(OrderCommentsPO::getOrderNumber, OrderCommentsResponseVO::getOrderNumber)
                .selectAs(OrderCommentsPO::getUserCode, OrderCommentsResponseVO::getUserCode)
                .selectAs(OrderCommentsPO::getCompanyCode, OrderCommentsResponseVO::getCompanyCode)
                .selectAs(OrderCommentsPO::getCompanyName, OrderCommentsResponseVO::getCompanyName)
                .selectAs(OrderCommentsPO::getCreatedDate, OrderCommentsResponseVO::getCreatedDate)
                .selectAs(OrderCommentsPO::getProductDescRating, OrderCommentsResponseVO::getProductDescRating)
                .selectAs(OrderCommentsPO::getPersonnelServiceRating, OrderCommentsResponseVO::getPersonnelServiceRating)
                .selectAs(OrderCommentsPO::getProductDeliveryRating, OrderCommentsResponseVO::getProductDeliveryRating)
                .selectAs(OrderCommentsPO::getReplied, OrderCommentsResponseVO::getReplied)
                .selectAs(OrderCommentsPO::getPhone, OrderCommentsResponseVO::getPhone)
                .selectAs(OrderCommentsPO::getEmail, OrderCommentsResponseVO::getEmail)
                .selectAs(OrderCommentsPO::getVisitRecords, OrderCommentsResponseVO::getVisitRecords)
                .select(OrderCommentsPO::getCompanyNameInitial)
                .selectAs(OrderCommentsPO::getRemark, OrderCommentsResponseVO::getRemark);
    }


    /**
     * 根据明细状态判断主状态
     *
     * @param orderList 明细列表
     * @return 状态
     */
    public static String judgmentStatus(List<OrderListPO> orderList) {

        logger.info("judgment status. parameter:{}", orderList);
        //获取去重后的订单明细状态
        List<String> statusList = orderList.stream().map(OrderListPO::getOrderDetailStatus).distinct().collect(Collectors.toList());

        logger.info("Get the order detail status after deduplication. old status:{}", statusList);

        String orderStatus;
        if (statusList.size() == 1 && statusList.contains(OrderStatusConstant.CANCEL)) {

            orderStatus = OrderStatusConstant.CANCEL;

        } else if (statusList.size() == 1 && statusList.contains(OrderStatusConstant.FINISH)) {

            orderStatus = OrderStatusConstant.FINISH;

        } else if (statusList.size() == 2 && statusList.contains(OrderStatusConstant.CANCEL) &&
                statusList.contains(OrderStatusConstant.FINISH)) {

            orderStatus = OrderStatusConstant.FINISH;

        } else if (statusList.size() == 1 && statusList.contains(OrderStatusConstant.CLOSED)) {

            orderStatus = OrderStatusConstant.CLOSED;

        } else {

            orderStatus = OrderStatusConstant.ON_WAY;

        }

        logger.info("get order status success. orderId:{}, newStatus:{}", orderList.get(0).getOrderId(), orderStatus);

        return orderStatus;
    }


    /**
     * 判断是否全部明细都已取消
     *
     * @param orderListPOs 订单列表PO
     * @return boolean
     */
    public static boolean hasAllCancel(List<OrderListPO> orderListPOs) {
        return orderListPOs.stream().allMatch(orderListPO -> StringUtils.equals(orderListPO.getOrderDetailStatus(), OrderStatusConstant.CANCEL) ||
                StringUtils.equals(orderListPO.getOrderDetailStatus(), OrderStatusConstant.CANCELING)
        );
    }
}
