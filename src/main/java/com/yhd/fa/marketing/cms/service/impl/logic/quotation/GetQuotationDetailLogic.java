package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.dao.QuotationListClickHouseDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.QuotationListMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListClickHousePO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationListPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProductInfoResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationGoodsDetailResponseVO;
import com.yhd.fa.marketing.cms.service.sao.ProductCenterCmsService;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version Id: GetQuotationDetailLogic.java, v0.1 2022/12/6 14:54 yehuasheng Exp $
 */
@Component
public class GetQuotationDetailLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetQuotationDetailLogic.class.getName());

    /**
     * 报价单列表
     */
    @Resource
    private QuotationListMapper quotationListMapper;

    /**
     * 产品中心服务
     */
    @Resource
    private ProductCenterCmsService productCenterCmsService;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private QuotationListClickHouseDAO quotationListClickHouseDAO;

    /**
     * 执行获取报价单详情逻辑
     *
     * @param quotationDetailResponseVO 报价单详情
     * @return BusinessResponse<QuotationDetailResponseVO>
     */
    public BusinessResponse<QuotationDetailResponseVO> exec(QuotationDetailResponseVO quotationDetailResponseVO) {
        logger.info("start exec quotation detail logic.");

        // 查报价单明细
        List<QuotationGoodsDetailResponseVO> quotationDetailList = getQuotationDetailList(quotationDetailResponseVO.getQuotationId());

        // 判断是否为空
        if (CollUtil.isEmpty(quotationDetailList)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.QUOTATION_DETAIL_IS_EMPTY);
        }

        // 获取产品信息
        setProductInfo(quotationDetailList);

        // 业务员/用户跟单信息
        setUserNameAndMerchandiser(quotationDetailResponseVO);

        // 设置输出内容
        quotationDetailResponseVO.setGoodsDetail(quotationDetailList);

        return BusinessResponseCommon.ok(quotationDetailResponseVO);
    }

    /**
     * 查询报价单明细信息
     *
     * @param quotationId 报价单id
     * @return List<QuotationGoodsDetailResponseVO>
     */
    private List<QuotationGoodsDetailResponseVO> getQuotationDetailList(String quotationId) {
        logger.info("get quotation detail by quotation id");
        List<QuotationGoodsDetailResponseVO> quotationGoodsDetailResponseVOS;
        // 查报价单明细
        quotationGoodsDetailResponseVOS = quotationListMapper.selectJoinList(QuotationGoodsDetailResponseVO.class, new MPJLambdaWrapper<QuotationListPO>()
                .eq(QuotationListPO::getQuotationId, quotationId)
                .selectAs(QuotationListPO::getId, QuotationGoodsDetailResponseVO::getQuotationDetailId)
                .selectAs(QuotationListPO::getSort, QuotationGoodsDetailResponseVO::getProductSort)
                .selectAs(QuotationListPO::getQuotationNumber, QuotationGoodsDetailResponseVO::getQuotationNumber)
                .selectAs(QuotationListPO::getGoodsStatus, QuotationGoodsDetailResponseVO::getGoodsStatus)
                .selectAs(QuotationListPO::getCalculationStatus, QuotationGoodsDetailResponseVO::getCalculationStatus)
                .selectAs(QuotationListPO::getCustomerModel, QuotationGoodsDetailResponseVO::getCustomerModel)
                .selectAs(QuotationListPO::getProductCode, QuotationGoodsDetailResponseVO::getProductCode)
                .selectAs(QuotationListPO::getQuantity, QuotationGoodsDetailResponseVO::getQuantity)
                .selectAs(QuotationListPO::getQuotationStatus, QuotationGoodsDetailResponseVO::getQuotationStatus)
                .selectAs(QuotationListPO::getConfirmationStatus, QuotationGoodsDetailResponseVO::getExamineStatus)
                .selectAs(QuotationListPO::getProductName, QuotationGoodsDetailResponseVO::getProductName)
                .selectAs(QuotationListPO::getModel, QuotationGoodsDetailResponseVO::getModel)
                .selectAs(QuotationListPO::getExamineRemark, QuotationGoodsDetailResponseVO::getEngineerRemark)
                .selectAs(QuotationListPO::getCustomerMaterialCode, QuotationGoodsDetailResponseVO::getCustomerMaterialCode)
                .selectAs(QuotationListPO::getCustomerProductName, QuotationGoodsDetailResponseVO::getCustomerProductName)
                .selectAs(QuotationListPO::getErrorMessage, QuotationGoodsDetailResponseVO::getErrorMsg)
                .selectAs(QuotationListPO::getFileUrl, QuotationGoodsDetailResponseVO::getFileUrl)
                .selectAs(QuotationListPO::getInsideFileUrl, QuotationGoodsDetailResponseVO::getInsideFileUrl)
                .selectAs(QuotationListPO::getRemark, QuotationGoodsDetailResponseVO::getRemark)
                .selectAs(QuotationListPO::getRecommend, QuotationGoodsDetailResponseVO::getRecommend)
                .selectAs(QuotationListPO::getOriginalPrice, QuotationGoodsDetailResponseVO::getPrice)
                .selectAs(QuotationListPO::getDiscountPrice, QuotationGoodsDetailResponseVO::getDiscountPrice)
                .selectAs(QuotationListPO::getTaxDiscountPrice, QuotationGoodsDetailResponseVO::getTaxDiscountPrice)
                .selectAs(QuotationListPO::getTotalPrice, QuotationGoodsDetailResponseVO::getTotalPrice)
                .selectAs(QuotationListPO::getDelivery, QuotationGoodsDetailResponseVO::getDelivery)
                .selectAs(QuotationListPO::getQuantityDiscountRate, QuotationGoodsDetailResponseVO::getQuantityDiscountRate)
                .selectAs(QuotationListPO::getTotalDiscountRate, QuotationGoodsDetailResponseVO::getTotalDiscountRate)
        );

        if (quotationGoodsDetailResponseVOS.isEmpty()) {
            quotationGoodsDetailResponseVOS = quotationListClickHouseDAO.selectJoinList(QuotationGoodsDetailResponseVO.class, new MPJLambdaWrapper<QuotationListClickHousePO>()
                    .eq(QuotationListClickHousePO::getQuotationId, quotationId)
                    .selectAs(QuotationListClickHousePO::getId, QuotationGoodsDetailResponseVO::getQuotationDetailId)
                    .selectAs(QuotationListClickHousePO::getSort, QuotationGoodsDetailResponseVO::getProductSort)
                    .selectAs(QuotationListClickHousePO::getQuotationNumber, QuotationGoodsDetailResponseVO::getQuotationNumber)
                    .selectAs(QuotationListClickHousePO::getGoodsStatus, QuotationGoodsDetailResponseVO::getGoodsStatus)
                    .selectAs(QuotationListClickHousePO::getCalculationStatus, QuotationGoodsDetailResponseVO::getCalculationStatus)
                    .selectAs(QuotationListClickHousePO::getCustomerModel, QuotationGoodsDetailResponseVO::getCustomerModel)
                    .selectAs(QuotationListClickHousePO::getProductCode, QuotationGoodsDetailResponseVO::getProductCode)
                    .selectAs(QuotationListClickHousePO::getQuantity, QuotationGoodsDetailResponseVO::getQuantity)
                    .selectAs(QuotationListClickHousePO::getQuotationStatus, QuotationGoodsDetailResponseVO::getQuotationStatus)
                    .selectAs(QuotationListClickHousePO::getConfirmationStatus, QuotationGoodsDetailResponseVO::getExamineStatus)
                    .selectAs(QuotationListClickHousePO::getProductName, QuotationGoodsDetailResponseVO::getProductName)
                    .selectAs(QuotationListClickHousePO::getModel, QuotationGoodsDetailResponseVO::getModel)
                    .selectAs(QuotationListClickHousePO::getExamineRemark, QuotationGoodsDetailResponseVO::getEngineerRemark)
                    .selectAs(QuotationListClickHousePO::getCustomerMaterialCode, QuotationGoodsDetailResponseVO::getCustomerMaterialCode)
                    .selectAs(QuotationListClickHousePO::getCustomerProductName, QuotationGoodsDetailResponseVO::getCustomerProductName)
                    .selectAs(QuotationListClickHousePO::getErrorMessage, QuotationGoodsDetailResponseVO::getErrorMsg)
                    .selectAs(QuotationListClickHousePO::getFileUrl, QuotationGoodsDetailResponseVO::getFileUrl)
                    .selectAs(QuotationListClickHousePO::getInsideFileUrl, QuotationGoodsDetailResponseVO::getInsideFileUrl)
                    .selectAs(QuotationListClickHousePO::getRemark, QuotationGoodsDetailResponseVO::getRemark)
                    .selectAs(QuotationListClickHousePO::getRecommend, QuotationGoodsDetailResponseVO::getRecommend)
                    .selectAs(QuotationListClickHousePO::getOriginalPrice, QuotationGoodsDetailResponseVO::getPrice)
                    .selectAs(QuotationListClickHousePO::getDiscountPrice, QuotationGoodsDetailResponseVO::getDiscountPrice)
                    .selectAs(QuotationListClickHousePO::getTaxDiscountPrice, QuotationGoodsDetailResponseVO::getTaxDiscountPrice)
                    .selectAs(QuotationListClickHousePO::getTotalPrice, QuotationGoodsDetailResponseVO::getTotalPrice)
                    .selectAs(QuotationListClickHousePO::getDelivery, QuotationGoodsDetailResponseVO::getDelivery)
                    .selectAs(QuotationListClickHousePO::getQuantityDiscountRate, QuotationGoodsDetailResponseVO::getQuantityDiscountRate)
                    .selectAs(QuotationListClickHousePO::getTotalDiscountRate, QuotationGoodsDetailResponseVO::getTotalDiscountRate));
        }

        return quotationGoodsDetailResponseVOS;
    }

    /**
     * 设置产品信息
     *
     * @param quotationGoodsDetail 报价单商品信息
     */
    private void setProductInfo(List<QuotationGoodsDetailResponseVO> quotationGoodsDetail) {
        logger.info("set product info.");

        // 提取代码
        List<String> codes = quotationGoodsDetail
                .stream()
                .map(QuotationGoodsDetailResponseVO::getProductCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(codes)) {
            return;
        }

        // 获取产品信息
        Map<String, ProductInfoResponseVO> productInfo = productCenterCmsService.getProductInfo(codes);

        // 设置产品信息
        quotationGoodsDetail.forEach(quotationGoodsDetailResponseVO
                -> Optional.ofNullable(productInfo.get(quotationGoodsDetailResponseVO.getProductCode()))
                .ifPresent(productInfoResponseVO -> {
                    // 设置产品一级分类
                    quotationGoodsDetailResponseVO.setTypeCode(productInfoResponseVO.getTypeCode());
                    // 设置产品二级分类
                    quotationGoodsDetailResponseVO.setCatCode(productInfoResponseVO.getCatCode());
                    // 设置产品系列
                    quotationGoodsDetailResponseVO.setGoodsCode(productInfoResponseVO.getGoodsCode());
                    // 设置产品图片
                    quotationGoodsDetailResponseVO.setImageUrl(productInfoResponseVO.getImageUrl());
                }));
    }

    /**
     * 设置用户的信息
     *
     * @param quotationDetailResponseVO 报价单详情
     */
    private void setUserNameAndMerchandiser(QuotationDetailResponseVO quotationDetailResponseVO) {
        logger.info("set quotation detail user name and merchandiser.");

        // 提取用户
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = Stream.of(UserCodeAndCompanyCodeDTO
                        .builder()
                        .companyCode(quotationDetailResponseVO.getCompanyCode())
                        .userCode(quotationDetailResponseVO.getUserCode())
                        .purchaseUserCode(quotationDetailResponseVO.getPurchaseUserCode())
                        .build())
                .collect(Collectors.toList());

        // 获取用户的信息
        Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userInfo = userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);

        Optional.ofNullable(userInfo.get(quotationDetailResponseVO.getUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
            // 设置跟单员
            Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).filter(merchandiserResponseVO -> StringUtils.isNotBlank(merchandiserResponseVO.getEmployeeName())).ifPresent(merchandiserResponseVO -> {
                // 设置跟单的名称
                quotationDetailResponseVO.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
                // 设置跟单联系方式
                quotationDetailResponseVO.setMerchandiserContact(merchandiserResponseVO.getMobile());
            });

            // 设置业务员
            Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> {
                // 设置跟单的名称
                quotationDetailResponseVO.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
                // 设置跟单联系方式
                quotationDetailResponseVO.setSalesmanContact(salesMan.getMobile());
            });

            // 设置用户名称
            if (ObjectUtil.isNotNull(userAndCompanyAndMerchandiserResponseVO.getUserInfo())) {
                quotationDetailResponseVO.setUserName(userAndCompanyAndMerchandiserResponseVO.getUserInfo().getUserName());
            }
        });

        Optional.ofNullable(userInfo.get(quotationDetailResponseVO.getPurchaseUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> quotationDetailResponseVO.setPurchaseUserName(userAndCompanyAndMerchandiserResponseVO.getUserInfo().getUserName()));
    }
}
