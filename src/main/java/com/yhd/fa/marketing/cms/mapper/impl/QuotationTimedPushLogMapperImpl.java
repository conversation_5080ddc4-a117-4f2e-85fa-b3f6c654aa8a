package com.yhd.fa.marketing.cms.mapper.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yhd.fa.marketing.cms.dao.QuotationTimedPushLogDAO;
import com.yhd.fa.marketing.cms.mapper.QuotationTimedPushLogMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationTimedPushLogPO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version Id: QuotationTimedPushLogMapperImpl.java, v0.1 2023/9/27 08:44 yehuasheng Exp $
 */
@Service
@DS("quotation")
public class QuotationTimedPushLogMapperImpl extends MPJBaseServiceImpl<QuotationTimedPushLogDAO, QuotationTimedPushLogPO> implements QuotationTimedPushLogMapper {
}
