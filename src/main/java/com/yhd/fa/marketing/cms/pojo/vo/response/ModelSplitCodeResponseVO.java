package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: ModelSplitCodeResponseVO.java, v0.1 2023/5/5 14:25 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ModelSplitCodeResponseVO extends BaseVO {
    /**
     * 型号
     */
    private String model;

    /**
     * 代码
     */
    private String code;

    /**
     * 是否有代码
     */
    private boolean hasCode;

    /**
     * 单据号
     */
    private String documentCode;

    /**
     * 代码状态
     */
    private String codeStatus;

    /**
     * 默认交期
     */
    private Integer defaultDelivery;

    /**
     * 类别编码
     */
    private String typeCode;

    /**
     * 单位
     */
    private String units;

    /**
     * 物料类型
     */
    private String materialType;

    /**
     * 是否去长度
     */
    private boolean deleteLength;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 说明
     */
    private String technicalSpecifications;

    /**
     * 报价状态
     */
    private String quoteStatus;

    /**
     * 是否没有历史最低价
     */
    private boolean hasNotHistoryPrice;

    /**
     * 是否隐藏网上价格
     */
    private boolean hideWebPrice;
}
