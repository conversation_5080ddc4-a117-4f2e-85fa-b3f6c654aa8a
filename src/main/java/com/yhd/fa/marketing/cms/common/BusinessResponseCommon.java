package com.yhd.fa.marketing.cms.common;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;

/**
 * <AUTHOR>
 * @version Id: BusinessResponseCommon.java, v0.1 2022/12/2 14:09 yehuasheng Exp $
 */
public class BusinessResponseCommon {
    /**
     * 成功语句
     */
    public static final String RESPONSE_OK_MSG = "success";
    /**
     * 失败语句
     */
    public static final String RESPONSE_ERROR_MSG = "服务异常";
    private static final long serialVersionUID = -3865115480476574285L;
    /**
     * 成功编码
     */
    private static final int RESPONSE_OK = 0;
    /**
     * 失败编码
     */
    private static final int RESPONSE_ERROR = 500000;

    private BusinessResponseCommon() {
    }

    /**
     * 成功
     *
     * @param data 返回的数据
     * @param <T>  T
     * @return BusinessResponse
     */
    public static <T> BusinessResponse<T> ok(T data) {
        BusinessResponse<T> businessResponse = new BusinessResponse<>();
        businessResponse.setRt_code(RESPONSE_OK);
        businessResponse.setRt_msg(RESPONSE_OK_MSG);
        businessResponse.setData(data);

        return businessResponse;
    }

    /**
     * 错误失败的返回
     *
     * @param <T> T
     * @return BusinessResponse
     */
    public static <T> BusinessResponse<T> fail() {
        return fail(RESPONSE_ERROR_MSG);
    }

    /**
     * 错误失败的返回
     *
     * @param msg 错误提示语
     * @param <T> T
     * @return BusinessResponse
     */
    public static <T> BusinessResponse<T> fail(String msg) {
        return fail(RESPONSE_ERROR, msg);
    }

    /**
     * 错误失败的返回
     *
     * @param code 错误失败的编码
     * @param msg  错误失败的提示语
     * @param <T>  T
     * @return BusinessResponse
     */
    public static <T> BusinessResponse<T> fail(int code, String msg) {
        BusinessResponse<T> businessResponse = new BusinessResponse<>();
        businessResponse.setRt_code(code);
        businessResponse.setRt_msg(msg);
        return businessResponse;
    }

    /**
     * 错误失败的返回
     *
     * @param resultCodeEnum 结果枚举
     * @param <T>            T
     * @return BusinessResponse
     */
    public static <T> BusinessResponse<T> fail(FaDocMarketingResponseEnum resultCodeEnum) {
        return BusinessResponse.fail(resultCodeEnum.getCode(), resultCodeEnum.getDesc());
    }
}
