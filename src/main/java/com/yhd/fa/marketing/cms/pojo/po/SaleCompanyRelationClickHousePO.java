package com.yhd.fa.marketing.cms.pojo.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version Id: SaleCompanyRelationClickHousePO.java, v 0.1 2025/5/7 15:24 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "sale_company_relation")
public class SaleCompanyRelationClickHousePO {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 业务员工号
     */
    private String salesNo;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 归属地DGYHD/SZYHD
     */
    private String ownershipCompany;
}
