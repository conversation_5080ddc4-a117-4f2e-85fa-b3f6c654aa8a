package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: CheckUserOrderResponseVO.java, v0.1 2023/5/3 12:13 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "检查用户是否有下过订单返回参数vo")
public class CheckUserOrderResponseVO extends BaseVO {
    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "12", required = true)
    private String userCode;

    /**
     * 是否有下过订单
     */
    @Schema(description = "是否有下过订单 true有 false没有", example = "true", required = true)
    private boolean hadOrders;
}
