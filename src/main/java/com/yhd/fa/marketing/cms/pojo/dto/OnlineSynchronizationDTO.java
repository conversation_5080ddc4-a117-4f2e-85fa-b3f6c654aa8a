package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: CreateQuotationMqDTO.java, v0.1 2023/1/27 9:03 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OnlineSynchronizationDTO extends BaseDTO {
    /**
     * 平台来源 YHD-fa
     */
    private String source;

    /**
     * 同步的json数据
     */
    private String synchronizationData;

    /**
     * 同步业务类型
     * "createOrder创建订单，" +
     * "createQuotation创建报价单，" +
     * "orderStatus订单状态，" +
     * "orderCancel订单取消，" +
     * "orderAfterSales订单售后，" +
     * "orderLogistics订单物流信息，" +
     * "orderRefund订单退款回传，" +
     * "orderInvoice订单发票，" +
     * "orderInvoiceData发票资料，" +
     * "quotation报价单
     */
    private String businessType;

    /**
     * 业务单号
     */
    private String businessOrderNo;
}
