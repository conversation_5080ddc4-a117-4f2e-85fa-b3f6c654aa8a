package com.yhd.fa.marketing.cms.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderClickHousePO.java, v 0.1 2025/3/20 09:55 JiangYuHong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fa_order")
public class OrderClickHousePO extends BaseEntity {
    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单等级，ordCo普通企业，certCo认证企业
     */
    private String orderLevel;

    /**
     * 客户单号
     */
    private String customerNumber;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 结算方式
     */
    private String settlementType;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 支付类型  unknown 暂时未知的支付方式 aliPay 支付宝、weChatPay 微信支付、bankTransfer 银行转账、unionPay银联、monthlyKnot 月结、monthlyKnot30 月结30天、monthlyKnot60 月结60天、monthlyKnot90 月结90天
     */
    private String paymentType;

    /**
     * 支付流水号
     */
    private String tradeNo;

    /**
     * 支付回调商户单号
     */
    private String mchOrderNo;

    /**
     * 支付时间
     */
    private LocalDateTime payDate;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 应付金额
     */
    private BigDecimal payablePrice;

    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 同步状态
     */
    private String synchronizeStatus;

    /**
     * 同步信息
     */
    private String synchronizeMessage;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 收寄件人
     */
    private String consignee;

    /**
     * 省份
     */
    private String province;

    /**
     * 市区
     */
    private String city;

    /**
     * 镇区
     */
    private String town;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 市区id
     */
    private Long cityId;

    /**
     * 镇区id
     */
    private Long townId;

    /**
     * 收货地址编码
     */
    private String addressId;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 删除状态 recycleBin 回收站  false 未删除 true 彻底删除
     */
    private String deleteStatus;

    /**
     * 取消状态 true 已取消, false未取消
     */
    private String cancelStatus;

    /**
     * 是否可以申请售后 true 已申请  false 未申请
     */
    private String afterSaleStatus;

    /**
     * 是否已砸金蛋 true 已砸  false 未砸
     */
    private String integralEgg;

    /**
     * 发票申请状态 true 已申请 false 未申请
     */
    private String invoiceStatus;

    /**
     * 订单状态  unpaid 待支付、onWay 在途单、finish 已完成、cancel 已取消、closed已关闭
     */
    private String orderStatus;

    /**
     * 配送方式 inBatches分批发货、merge合并一起
     */
    private String shipping;

    /**
     * 首批发货商品的发货日
     */
    private Integer firstShippingDay;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 下单来源
     */
    private String orderSource;

    /**
     * 下单渠道 pc端 wap手机端 weChat微信 app端 applet小程序
     */
    private String orderChannel;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 下单ip
     */
    private String ipLocation;

    /**
     * 订单所属地址 DGYHD东莞 SZYHD苏州
     */
    private String territory;

    /**
     * 订单已确认消息通知 true 已通知 false 未通知
     */
    private String orderConfirmNotice;

    /**
     * 是否已评论 true 已评论 false 未评论
     */
    private String replyStatus;

    /**
     * 订单完成时间
     */
    private LocalDateTime receivedTime;

    /**
     * 平台来源
     */
    private String platformCode;
    /**
     * 订单明细
     */
    @TableField(exist = false)
    @EntityMapping(thisField = "id", joinField = "orderId")
    private List<OrderListClickHousePO> orderDetailList;
    /**
     * 全部订单总金额
     */
    @TableField(exist = false)
    private BigDecimal totalPriceAll;
    /**
     *部分订单总金额
     */
    @TableField(exist = false)
    private BigDecimal totalPricePart;
}
