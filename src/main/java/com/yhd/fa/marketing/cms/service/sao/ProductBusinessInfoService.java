package com.yhd.fa.marketing.cms.service.sao;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationResponseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ProductBusinessInfoService.java, v0.1 2022/12/23 10:13 yehuasheng Exp $
 */
public interface ProductBusinessInfoService {
    /**
     * 获取型号的价格和交期
     *
     * @param quotationRequestVO 询价参数
     * @return BusinessResponse<List < QuotationResponseVO>>
     */
    BusinessResponse<List<QuotationResponseVO>> getModelPrice(QuotationRequestVO quotationRequestVO);
}
