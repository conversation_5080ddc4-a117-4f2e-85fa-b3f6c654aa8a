package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.fa.marketing.cms.pojo.dto.InvoiceFileInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderInvoiceInfoResponseVO.java, v0.1 2023/2/23 14:34 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderInvoiceDetailResponseVO extends OrderInvoiceResponseVO {
    /**
     * 发货状态
     */
    @Schema(description = "发货状态 shipped已发货 unshipped未发货", example = "shipped")
    private String deliveryStatus;

    /**
     * 发货状态名称
     */
    @Schema(description = "发货状态名称", example = "已发货")
    private String deliveryStatusName;

    /**
     * 用户收货地址id
     */
    @Schema(description = "用户收货地址id", example = "48c671c37e8a4acc8b9a89d5c61d9010")
    private String userAddressId;

    /**
     * 发票收货地址
     */
    @Schema(description = "发票收货地址", example = "广东省东莞市横沥镇桃园二路33号")
    private String userAddress;

    /**
     * 收件人
     */
    @Schema(description = "收件人", example = "你猜")
    private String consignee;

    /**
     * 收件人联系方式
     */
    @Schema(description = "收件人联系方式", example = "13111111111")
    private String linkPhone;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    /**
     * 发票税务登记号
     */
    @Schema(description = "发票税务登记号", example = "54454564564M")
    private String invTaxRegNumber;

    /**
     * 发票公司地址
     */
    @Schema(description = "发票公司地址", example = "啦啦啦啦啦")
    private String invAddress;

    /**
     * 发票公司电话
     */
    @Schema(description = "发票公司电话", example = "0769-8888888")
    private String invLinkPhone;

    /**
     * 发票开户银行
     */
    @Schema(description = "发票开户银行", example = "大东莞银行")
    private String invManuBank;

    /**
     * 发票银行账户
     */
    @Schema(description = "发票银行账户", example = "***************")
    private String invAccount;

    /**
     * 物流编码
     */
    @Schema(description = "物流编码", example = "**********")
    private String logisticsCode;

    /**
     * 物流公司
     */
    @Schema(description = "物流公司", example = "顺丰")
    private String logisticsName;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "你猜猜")
    private String remarks;

    /**
     * 是否erp有错误
     */
    @Schema(description = "是否erp有错误 true有 false没有", example = "false")
    private String isHasError;

    /**
     * 发送给erp时间
     */
    @Schema(description = "同步时间 该字段没有值时会不存在")
    private LocalDateTime synchronizationDate;

    /**
     * 发票文件信息;财务系统回传
     */
    @Schema(description = "发票文件信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<InvoiceFileInfoDTO> invoiceFileInfo;

    /**
     * 发票文件信息;财务系统回传
     */
    private String invoiceFileInfoStr;

    /**
     * 订单发票申请的订单列表
     */
    @Schema(description = "订单发票申请的订单列表")
    private List<OrderInvoiceDetailListResponseVO> orderInvoiceDetails;
}
