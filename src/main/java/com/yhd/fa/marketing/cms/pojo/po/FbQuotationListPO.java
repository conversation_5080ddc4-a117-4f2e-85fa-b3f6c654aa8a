package com.yhd.fa.marketing.cms.pojo.po;


import java.util.Date;

import com.yhd.common.pojo.po.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (FbQuotationList)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 16:17:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "fb_quotation_list")
public class FbQuotationListPO extends BaseEntity {

    /**
     * 报价单id
     */
    private String quotationId;
    /**
     * 报价单号
     */
    private String quotationNumber;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 型号
     */
    private String model;
    /**
     * 客户型号
     */
    private String customerModel;
    /**
     * 代码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 数量
     */
    private String quantity;
    /**
     * 原单价
     */
    private String originalPrice;
    /**
     * 折扣单价
     */
    private String discountPrice;
    /**
     * 含税折扣单价
     */
    private String taxDiscountPrice;
    /**
     * 总价
     */
    private String totalPrice;
    /**
     * 数量折扣
     */
    private String quantityDiscountRate;
    /**
     * 总折扣
     */
    private String totalDiscountRate;
    /**
     * 交期
     */
    private String delivery;
    /**
     * 备注
     */
    private String remark;
    /**
     * 客户物料编码
     */
    private String customerMaterialCode;
    /**
     * 客户产品名称
     */
    private String customerProductName;
    /**
     * 长度
     */
    private String modelLong;
    /**
     * 单位
     */
    private String unit;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 报价状态，quotation报价中，finish报价完成，close已终止
     */
    private String quotationStatus;
    /**
     * 确认状态，confirm确认，disagree不同意
     */
    private String confirmationStatus;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 审核的备注
     */
    private String examineRemark;
    /**
     * 文件地址
     */
    private String fileUrl;
    /**
     * 怡合达附件地址
     */
    private String insideFileUrl;
    /**
     * 报价修改人
     */
    private String quotationUpdatedBy;
    /**
     * 产品图片
     */
    private String imageUrl;
    /**
     * 报价方式'SYSTEM'自动报价,'ARTIFICIAL'人工报价
     */
    private String quotationType;

    /**
     *
     * 材料
     */
    private String rawMaterial;

    /**
     * 表面处理
     */
    private String surfaceTreatment;



    /**
     * 热处理
     */
    private String heatTreatment;

    /**
     * 利润系数
     */
    private String profitCoefficient;

    /**
     * 非标类别
     */
    private String categoryCode;


    /**
     * 物料类型
     */
    private String materialType;

    /**
     * 报价人类型 电商用户/报价工程师
     */
    private String quotationUserType;

}

