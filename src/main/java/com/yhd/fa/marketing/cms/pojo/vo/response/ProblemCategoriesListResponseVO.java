package com.yhd.fa.marketing.cms.pojo.vo.response;


import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ProblemCategoriesListResponseVO.java, v 0.1 2025/5/16 11:54 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProblemCategoriesListResponseVO extends BaseVO {

    /**
     * 问题分类id
     */
    @Schema(description = "问题分类id", example = "1")
    private Integer id;

    /**
     * 问题分类名称
     */
    @Schema(description = "问题分类名称", example = "质量问题")
    private String name;

    /**
     * 父级id
     */
    @Schema(description = "父级id", example = "0")
    private Integer parentId;

    /**
     * 子级
     */
    @Schema(description = "子级")
    private List<ProblemCategoriesListResponseVO> children;
}
