package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version Id: AddNebulaGraphQuotationDetailDTO.java, v0.1 2023/3/13 11:29 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddNebulaGraphQuotationDetailDTO extends BaseDTO {
    /**
     * id
     */
    private String id;

    /**
     * 报价单id
     */
    private String quotationId;

    /**
     * 报价单单号
     */
    private String quotationNumber;

    /**
     * 报价单序号
     */
    private int sort;

    /**
     * 报价单名称
     */
    private String productName;

    /**
     * 型号
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 代码
     */
    private String code;

    /**
     * 一级分类
     */
    private String typeCode;

    /**
     * 二级分类
     */
    private String catCode;

    /**
     * 系列编码
     */
    private String goodsCode;

    /**
     * 创建时间
     */
    private Date createdDate;
}
