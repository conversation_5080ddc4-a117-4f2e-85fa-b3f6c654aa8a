package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCommentsReplyRequestVO.java, v 0.1 2023/3/6 14:11 JiangYuHong Exp $
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderCommentsReplyRequestVO extends BaseVO {

    @Schema(description = "订单评价id")
    @NotEmpty(message = "请操作正确的订单评价信息")
    private String commentsId;
    /**
     * 回复内容
     */
    @Schema(description = "回复内容")
    @NotEmpty(message = "回复内容不能为空")
    private String replyContent;

    /**
     * 赠送积分
     */
    @Schema(description = "赠送积分", example = "10")
    @NotNull(message = "请选择赠送积分")
    private Integer points;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "已生成工单")
    private String remark;

    /**
     * 问题分类id
     */
    @Schema(description = "问题分类id", example = "[1,2,3]")
    private List<Integer> problemCategoriesIds;

    /**
     * 回访记录
     */
    @Schema(description = "回访记录", example = "已回访")
    private String visitRecords;
}
