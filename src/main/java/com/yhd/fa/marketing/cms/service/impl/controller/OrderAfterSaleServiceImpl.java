package com.yhd.fa.marketing.cms.service.impl.controller;

import com.github.pagehelper.PageInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.request.OrderAfterSaleRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleDetailResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderAfterSaleResponseVO;
import com.yhd.fa.marketing.cms.service.controller.OrderAfterSaleService;
import com.yhd.fa.marketing.cms.service.impl.logic.order.GetOrderAfterSaleInfoLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.order.GetOrderAfterSaleListLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.order.SynchronizationOrderAfterSaleLogic;
import com.yhd.fa.marketing.cms.service.impl.verification.order.GetOrderAfterSaleListVerification;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleServiceImpl.java, v0.1 2023/2/24 14:51 yehuasheng Exp $
 */
@Service
public class OrderAfterSaleServiceImpl implements OrderAfterSaleService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(OrderAfterSaleServiceImpl.class.getName());

    /**
     * 检查订单售后
     */
    @Resource
    private GetOrderAfterSaleListVerification getOrderAfterSaleListVerification;

    /**
     * 订单售后列表逻辑
     */
    @Resource
    private GetOrderAfterSaleListLogic getOrderAfterSaleListLogic;

    /**
     * 订单售后详情页逻辑
     */
    @Resource
    private GetOrderAfterSaleInfoLogic getOrderAfterSaleInfoLogic;

    /**
     * 售后逻辑
     */
    @Resource
    private SynchronizationOrderAfterSaleLogic synchronizationOrderAfterSaleLogic;

    /**
     * 获取订单售后列表
     *
     * @param orderAfterSaleRequestVO 订单售后列表请求参数
     * @return BusinessResponse<PageInfo < OrderAfterSaleResponseVO>>
     */
    @Override
    public BusinessResponse<PageInfo<OrderAfterSaleResponseVO>> getOrderAfterSaleList(OrderAfterSaleRequestVO orderAfterSaleRequestVO) {
        logger.info("start get order after sale list service.");

        // 检查请求参数
        BusinessResponse<PageInfo<OrderAfterSaleResponseVO>> checkOrderAfterSaleParameter = getOrderAfterSaleListVerification.check(orderAfterSaleRequestVO);
        if (!checkOrderAfterSaleParameter.success()) {
            return checkOrderAfterSaleParameter;
        }

        // 执行逻辑
        return getOrderAfterSaleListLogic.exec(orderAfterSaleRequestVO);
    }

    /**
     * 获取订单售后详情页
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<OrderAfterSaleDetailResponseVO>
     */
    @Override
    public BusinessResponse<OrderAfterSaleDetailResponseVO> getOrderAfterSaleInfo(String orderAfterSaleId) {
        logger.info("start get order after sale detail service.");

        // 执行逻辑
        return getOrderAfterSaleInfoLogic.exec(orderAfterSaleId);
    }

    /**
     * 同步订单售后
     *
     * @param orderAfterSaleId 订单售后id
     * @return BusinessResponse<Void>
     */
    @Override
    public BusinessResponse<Void> synchronizationOrderAfterSale(String orderAfterSaleId) {
        logger.info("start synchronization order after sale service.");

        // 执行同步
        return synchronizationOrderAfterSaleLogic.exec(orderAfterSaleId);
    }
}
