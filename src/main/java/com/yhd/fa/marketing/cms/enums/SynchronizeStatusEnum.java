package com.yhd.fa.marketing.cms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: SynchronizeStatusEnum.java, v 0.1 2022/11/23 17:27 JiangYuHong Exp $
 */
@Getter
@AllArgsConstructor
public enum SynchronizeStatusEnum {

    WAIT("wait", "等待"),
    SUCCESS("success", "成功"),
    FAIL("fail", "失败"),
    SYNCHRONIZING("synchronizing", "同步中");

    private final String status;
    private final String desc;

}
