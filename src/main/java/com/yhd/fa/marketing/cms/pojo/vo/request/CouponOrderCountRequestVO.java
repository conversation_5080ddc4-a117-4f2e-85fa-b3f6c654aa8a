package com.yhd.fa.marketing.cms.pojo.vo.request;


import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: CouponOrderCountRequestVO.java, v 0.1 2025/7/7 11:11 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CouponOrderCountRequestVO extends BaseVO {

    /**
     * 促销活动id
     */
    @NotEmpty(message = "促销活动id不能为空")
    @Schema(description = "促销活动id", example = "1")
    private List<String> promotionIds;
}
