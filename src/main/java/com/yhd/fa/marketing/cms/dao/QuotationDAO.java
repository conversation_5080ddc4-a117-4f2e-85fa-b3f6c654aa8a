package com.yhd.fa.marketing.cms.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version Id: QuotationDAO.java, v0.1 2022/12/1 17:02 yehuasheng Exp $
 */
@Repository("faQuotation")
@DS("quotation")
public interface QuotationDAO extends MPJBaseMapper<QuotationPO> {

    @Select("<script>" +
            "SELECT count(0) FROM (" +
            "${fullSql} " +
            ") table_count;" +
            "</script>")
    Integer countOrderList(@Param("fullSql") String fullSql);
}
