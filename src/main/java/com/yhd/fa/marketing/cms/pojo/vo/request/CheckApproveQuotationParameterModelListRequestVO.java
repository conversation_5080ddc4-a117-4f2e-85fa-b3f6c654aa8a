package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id: CheckApproveQuotationParameterModelListRequestVO.java, v0.1 2023/2/9 20:00 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckApproveQuotationParameterModelListRequestVO extends BaseVO {
    /**
     * 型号
     */
    @NotEmpty(message = "型号不能为空")
    @Schema(description = "型号", example = "SAD01-D3-L100")
    private String model;

    /**
     * 数量
     */
    @Min(value = 1, message = "数量不能少于1")
    @Schema(description = "数量", example = "1")
    private long quantity;

    /**
     * 附件地址
     */
    @Schema(description = "附件地址，如果填写了说明需要人工报价无价格出来", example = "https://image.yhdfa.com/asd.png")
    private String fileUrl;

    /**
     * 备注
     */
    @Schema(description = "备注， 如果填写了说明需要人工报价无价格出来", example = "你猜 备注")
    private String remark;

    /**
     * 前端使用的序号
     */
    @Schema(description = "前端使用的序号 用户前端展示", example = "1")
    private Integer frontEndSort;
}
