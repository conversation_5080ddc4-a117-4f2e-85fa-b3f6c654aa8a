package com.yhd.fa.marketing.cms.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhd.common.pojo.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id: QuotationExcelTemplateDTO.java, v0.1 2022/10/22 11:42 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuotationExcelTemplateDTO extends BaseDTO {
    /**
     * 客户型号
     */
    @ExcelProperty("规格型号")
    private String customerModel;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    private long quantity;

    /**
     * 客户物料编码
     */
    @ExcelProperty("物料编码")
    private String customerMaterialCode;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 备注说明
     */
    @ExcelProperty("物料名称")
    private String customerProductName;
}
