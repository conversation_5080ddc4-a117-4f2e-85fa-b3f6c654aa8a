package com.yhd.fa.marketing.cms.service.impl.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.PageInfo;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.BaseConstant;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.dao.EnquiryLogDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.EnquiryLogListRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ExportEnquiryRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.CustomerCompanyListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryExportResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryListExportResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.EnquiryLogListResponseVO;
import com.yhd.fa.marketing.cms.service.controller.ExportService;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.quotation.ExcelExportLogic;
import com.yhd.fa.marketing.cms.service.impl.logic.quotation.GetEnquiryLogLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import com.yhd.fa.marketing.cms.util.SyncSaleCompanyUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@DS("ch")
public class ExportServiceImpl extends MPJBaseServiceImpl<EnquiryLogDAO, EnquiryLogPO> implements ExportService {

    @Resource
    private ExcelExportLogic excelExportLogic;

    @Resource
    private EnquiryLogDAO enquiryLogDAO;

    @Resource
    private UserBucCmsService userBucCmsService;
    @Resource
    private DataAuthLogic dataAuthLogic;

    @Resource
    private GetEnquiryLogLogic getEnquiryLogLogic;
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(ExportServiceImpl.class.getName());


    /**
     * 导出价单excel文件
     *
     * @param requestVO 导出报价单参数
     * @param result    校验的返回值
     * @param response  返回的内容
     */
    @Override
    public void exportExcel(ExportEnquiryRequestVO requestVO, BindingResult result, HttpServletResponse response) {

        //导出逻辑
        EnquiryExportResponseVO enquiryLog = getEnquiryLog(requestVO);

        if (Objects.isNull(enquiryLog) || CollectionUtils.isEmpty(enquiryLog.getList())) {
            response.setStatus(500);
            return;
        }

        excelExportLogic.exportExcel(enquiryLog, response);
    }

    /**
     * 获取需要导出的数据
     *
     * @param requestVO 请求参数
     * @return 数据
     */
    public EnquiryExportResponseVO getEnquiryLog(ExportEnquiryRequestVO requestVO) {
        logger.info("get export enquiry log start.parameter:{}", requestVO);

        //初始化返回实体
        EnquiryExportResponseVO enquiryExportResponseVO = new EnquiryExportResponseVO();
        enquiryExportResponseVO.setPass(false);

        EnquiryLogListRequestVO enquiryLogListRequestVO = new EnquiryLogListRequestVO();
        BeanUtil.copyProperties(requestVO, enquiryLogListRequestVO);
        enquiryLogListRequestVO.setPageNum(CommonConstant.ZERO);
        BusinessResponse<PageInfo<EnquiryLogListResponseVO>> exec = getEnquiryLogLogic.exec(enquiryLogListRequestVO);

        if(!exec.success()){
            enquiryExportResponseVO.setErrorMessage(FaDocMarketingResponseEnum.QUOTATION_IS_EMPTY.getDesc());
            enquiryExportResponseVO.setList(new ArrayList<>());
            return enquiryExportResponseVO;
        }
        PageInfo<EnquiryLogListResponseVO> data = exec.getData();
        List<EnquiryLogListResponseVO> list = data.getList();

        List<EnquiryListExportResponseVO> enquiryLogList = BeanUtil.copyToList(list, EnquiryListExportResponseVO.class);
        enquiryLogList.forEach(enquiryListExportResponseVO ->

                {
                    enquiryListExportResponseVO.setCreatedDateStr(enquiryListExportResponseVO.getCreatedDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                    if (enquiryListExportResponseVO.getRegisterDate() != null) {
                        enquiryListExportResponseVO.setRegisterDateStr(enquiryListExportResponseVO.getRegisterDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                }
        );
        //设置返回数据
        enquiryExportResponseVO.setList(enquiryLogList);

        return enquiryExportResponseVO;
    }




    /**
     * 查询集合总条数
     *
     * @param requestVO 请求参数
     * @param count     条数
     * @return MPJLambdaWrapper
     */
    private MPJLambdaWrapper<EnquiryLogPO> setMPJLambdaWrapper(ExportEnquiryRequestVO requestVO, boolean count) {
        MPJLambdaWrapper<EnquiryLogPO> queryWrapper = new MPJLambdaWrapper<>();
        if (!count) {
            queryWrapper.selectAll(EnquiryLogPO.class);
            queryWrapper.orderByDesc(EnquiryLogPO::getCreatedDate);
        }
        //初始化条件
        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, EnquiryLogPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        //产品名称
        Optional.ofNullable(requestVO.getProductName())
                .filter(StringUtils::isNotBlank)
                .ifPresent(productName -> queryWrapper.like(EnquiryLogPO::getProductName, productName));
        //型号查询
        Optional.ofNullable(requestVO.getModel())
                .filter(StringUtils::isNotBlank)
                .ifPresent(model -> queryWrapper.eq(EnquiryLogPO::getModel, model));

        // 判断是否有企业名称
        setCompanyNameQueryWrapper(requestVO, queryWrapper);

        // 判断是否有用户手机号码
        setUserMobileQueryWrapper(requestVO, queryWrapper);

        // 判断是否有用户邮箱
        setUserEmailQueryWrapper(requestVO, queryWrapper);

        // 判断是否有日期
        setTimeQueryWrapper(requestVO, queryWrapper);

        // 判断是否有含税单价
        setTaxDiscountPriceQueryWrapper(requestVO, queryWrapper);

        // 是否查询用户类型
        Optional.ofNullable(requestVO.getUserType())
                .filter(StringUtils::isNotBlank)
                .ifPresent(userType -> queryWrapper.eq(EnquiryLogPO::getUserType, userType));

        // 是否有查询职位
        Optional.ofNullable(requestVO.getOccupation())
                .filter(StringUtils::isNotBlank)
                .ifPresent(occupation -> queryWrapper.eq(EnquiryLogPO::getUserType, occupation));

        // 是否有查询区域
        Optional.ofNullable(requestVO.getIpRegion())
                .filter(StringUtils::isNotBlank)
                .ifPresent(region -> queryWrapper.likeRight(EnquiryLogPO::getIpRegion, region));

        if (requestVO.getStartRegisterDate() != null && requestVO.getEndRegisterDate() != null) {
            queryWrapper.ge(EnquiryLogPO::getRegisterDate, requestVO.getStartRegisterDate().atStartOfDay().format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
            queryWrapper.le(EnquiryLogPO::getRegisterDate, requestVO.getEndRegisterDate().atTime(LocalTime.MAX).format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
        }

        return queryWrapper;
    }

    /**
     * 设置查询传时间
     *
     * @param requestVO    查价单列表请求参数
     * @param queryWrapper 查询where条件
     */
    private void setTimeQueryWrapper(ExportEnquiryRequestVO requestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set export time query wrapper.");

        if (null != requestVO.getStartTime()) {
            queryWrapper.ge(OrderPO::getCreatedDate, requestVO.getStartTime());
        }
        if (null != requestVO.getEndTime()) {
            queryWrapper.le(OrderPO::getCreatedDate, requestVO.getEndTime());
        }

        // 当前时间
        LocalDateTime nowDateTime = LocalDateTime.now();
        // 开始时间
        LocalDateTime startTime = Optional.ofNullable(requestVO.getStartTime()).orElse(nowDateTime.minusMonths(CommonConstant.ONE));
        // 结束时间
        LocalDateTime endTime = Optional.ofNullable(requestVO.getEndTime()).orElse(nowDateTime);
        // 时间的分区
        LocalDate createdDay = startTime.toLocalDate();

        // 设置查询
        queryWrapper.ge(EnquiryLogPO::getCreatedDay, createdDay);
        queryWrapper.ge(EnquiryLogPO::getCreatedDate, startTime.format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
        queryWrapper.le(EnquiryLogPO::getCreatedDate, endTime.format(DateTimeFormatter.ofPattern(FaDocMarketingCmsConstant.DATE_TIME_DEFAULT_FORMATTER)));
    }

    /**
     * 设置查询含税单价
     *
     * @param requestVO    请求参数
     * @param queryWrapper 查询where条件
     */
    private void setTaxDiscountPriceQueryWrapper(ExportEnquiryRequestVO requestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set export tax discount price query wrapper.");

        if (requestVO.getTaxDiscountPriceMin() != null) {
            queryWrapper.ge(EnquiryLogPO::getTaxDiscountPrice, requestVO.getTaxDiscountPriceMin());

        }
        if (requestVO.getTaxDiscountPriceMax() != null) {
            queryWrapper.le(EnquiryLogPO::getTaxDiscountPrice, requestVO.getTaxDiscountPriceMax());
        }
    }

    /**
     * 设置企业名称查询
     *
     * @param requestVO    查价单列表请求参数
     * @param queryWrapper 查询where条件
     */
    private void setCompanyNameQueryWrapper(ExportEnquiryRequestVO requestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set export company name query wrapper.");

        if (StringUtils.isNotBlank(requestVO.getCompanyName())) {
            // 请求用户中心获取企业编码
            List<CustomerCompanyListResponseVO> customerCompanyList = userBucCmsService.searchCompanyListByCompanyName(requestVO.getCompanyName());
            if (CollUtil.isNotEmpty(customerCompanyList)) {
                // 提取企业编码
                List<String> companyCode = customerCompanyList.stream().map(CustomerCompanyListResponseVO::getCompanyCode).collect(Collectors.toList());
                queryWrapper.in(EnquiryLogPO::getCompanyCode, companyCode);
            } else {
                // 如果查不到企业默认设置值
                queryWrapper.eq(EnquiryLogPO::getCompanyCode, CommonConstant.FALSE);
            }
        }
    }

    /**
     * 设置用户手机号码查询条件
     *
     * @param requestVO    查价单列表请求参数
     * @param queryWrapper 查询where条件
     */
    private void setUserMobileQueryWrapper(ExportEnquiryRequestVO requestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set export user mobile query wrapper.");

        if (StringUtils.isNotBlank(requestVO.getMobile())) {
            // 请求用户中心获取用户编码
            List<String> userCodeByModel = userBucCmsService.getUserCodeByModelOrEmail(requestVO.getMobile(), null);
            queryWrapper.in(EnquiryLogPO::getUserCode, userCodeByModel);
        }
    }

    /**
     * 设置用户邮箱查询条件
     *
     * @param requestVO    查价单列表请求参数
     * @param queryWrapper 查询where条件
     */
    private void setUserEmailQueryWrapper(ExportEnquiryRequestVO requestVO, MPJLambdaWrapper<EnquiryLogPO> queryWrapper) {
        logger.info("set export user email query wrapper.");

        if (StringUtils.isNotBlank(requestVO.getEmail())) {
            // 请求用户中心获取用户编码
            List<String> userCodeByModel = userBucCmsService.getUserCodeByModelOrEmail(null, requestVO.getEmail());
            queryWrapper.in(EnquiryLogPO::getUserCode, userCodeByModel);
        }
    }




}
