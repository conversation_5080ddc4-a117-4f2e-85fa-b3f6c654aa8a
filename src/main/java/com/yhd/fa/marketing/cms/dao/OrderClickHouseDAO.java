package com.yhd.fa.marketing.cms.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderClickHousePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @version Id: OrderClickHouseDAO.java, v 0.1 2024/12/26 16:11 JiangYuHong Exp $
 */
@DS("order_ch")
public interface OrderClickHouseDAO extends MPJBaseMapper<OrderClickHousePO> {

    @Select("<script>" +
            "SELECT count(0) FROM (" +
            "${fullSql} " +
            ") table_count;" +
            "</script>")
    Integer countOrderList(@Param("fullSql") String fullSql);
}
