package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.fa.marketing.cms.constant.PaymentTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: UpdateOrderPaymentSerialNumberRequestVO.java, v0.1 2023/3/29 9:11 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateOrderPaymentSerialNumberRequestVO extends BaseVO {
    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "YI202211151647149779ZLNE", required = true)
    @NotBlank(message = "订单号不能为空")
    private String orderNumber;

    /**
     * 流水号
     */
    @Schema(description = "流水号", example = "a51eabaea7f341a78fc572ce9de0e3f7")
    private String serialNumber;

    /**
     * 支付回调商户单号
     */
    @Schema(description = "支付回调商户单号", example = "OIHJhd20220906083150652000005")
    private String mchOrderNo;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式，aliPlay支付宝支付 weChatPay微信支付 bankTransfer银行转账 unionPay企业网银",
            allowableValues = {PaymentTypeConstant.ALI_PAY,
                    PaymentTypeConstant.WECHAT_PAY,
                    PaymentTypeConstant.BANK_TRANSFER,
                    PaymentTypeConstant.UNION_PAY},
            required = true)
    @NotBlank(message = "支付方式不能为空")
    private String paymentType;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额，当支付类型不是银行转账时必填.", example = "10.00")
    private BigDecimal paymentPrice;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间，如果不存则使用当前时间", example = "2022-01-01 00:00:00")
    private LocalDateTime paymentDateTime;
}
