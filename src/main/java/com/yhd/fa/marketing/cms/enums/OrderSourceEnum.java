package com.yhd.fa.marketing.cms.enums;

import com.yhd.common.util.CommonConstant;
import com.yhd.fa.marketing.cms.constant.OrderSourceConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderSourceEnum.java, v0.1 2023/2/20 8:56 yehuasheng Exp $
 */
@Getter
public enum OrderSourceEnum {
    ALL(CommonConstant.EMPTY, "全部"),
    CART(OrderSourceConstant.CART, "购物车"),
    BUY(OrderSourceConstant.BUY, "一键购买"),
    QUOTATION(OrderSourceConstant.QUOTATION, "报价单"),
    ;

    private final String orderSource;
    private final String orderSourceCn;

    OrderSourceEnum(String orderSource, String orderSourceCn) {
        this.orderSource = orderSource;
        this.orderSourceCn = orderSourceCn;
    }
}
