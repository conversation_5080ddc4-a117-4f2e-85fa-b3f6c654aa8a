package com.yhd.fa.marketing.cms.constant;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleStatusConstant.java, v0.1 2023/2/24 14:38 yehuasheng Exp $
 */
public class OrderAfterSaleStatusConstant {
    /**
     * 处理中
     */
    public static final String PROCESSING = "processing";
    /**
     * 已处理
     */
    public static final String PROCESSED = "processed";

    /**
     * 待审核
     */
    public static final String PENDING_AUDIT = "pendingAudit";

    /**
     * 待售后方案(ERP发起单审核同意)
     */
    public static final String PENDING_PLAN = "pendingPlan";

    /**
     * 待寄回商品
     */
    public static final String PENDING_RETURN = "pendingReturn";

    /**
     * 寄回商品待检测
     */
    public static final String PENDING_CHECK = "pendingCheck";

    /**
     * 待退款
     */
    public static final String PENDING_REFUND = "pendingRefund";

    /**
     * 待发货
     */
    public static final String PENDING_SHIP = "pendingShip";

    /**
     * 待收货
     */
    public static final String PENDING_RECEIVE = "pendingReceive";

    /**
     * 待维修
     */
    public static final String PENDING_REPAIR = "pendingRepair";

    /**
     * 售后完成
     */
    public static final String COMPLETED = "completed";

    /**
     * 售后关闭
     */
    public static final String CLOSED = "closed";

    /**
     * 取消
     */
    public static final String CANCEL = "cancel";

    private OrderAfterSaleStatusConstant() {
    }
}
