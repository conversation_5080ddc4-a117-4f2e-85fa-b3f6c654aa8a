package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: SendMessageToEmailRequestVO.java, v0.1 2023/3/20 19:54 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendMessageToEmailRequestVO extends BaseVO {
    /**
     * 模板id
     */
    private String templateId;

    /**
     * 数据
     */
    private transient Map<String, Object> map = new HashMap<>();

    /**
     * 邮箱
     */
    private List<String> emailAddress;

    /**
     * 邮件类型：order订单，quotation报价单，other其他；订单报价单邮件包含明细项时传入order/quotation，不包含明细项直接传other
     */
    private String type;
}
