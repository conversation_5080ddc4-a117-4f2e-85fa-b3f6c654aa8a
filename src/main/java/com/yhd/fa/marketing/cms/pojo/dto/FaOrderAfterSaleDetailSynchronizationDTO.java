package com.yhd.fa.marketing.cms.pojo.dto;

import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: FaOrderAfterSaleDetailSynchronizationDTO.java, v0.1 2023/3/2 10:37 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaOrderAfterSaleDetailSynchronizationDTO extends BaseDTO {

    /**
     * 订单售后详情id
     */
    private String id;

    /**
     * 售后id
     */
    private String afterSaleId;

    /**
     * 售后单号
     */
    private String afterSaleNumber;

    /**
     * 订单明细序号
     */
    private Integer orderSortId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 折扣单价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣后含税单价
     */
    private BigDecimal taxDiscountPrice;

    /**
     * 含税总金额
     */
    private BigDecimal totalPrice;

    /**
     * 产品单位
     */
    private String unit;
}
