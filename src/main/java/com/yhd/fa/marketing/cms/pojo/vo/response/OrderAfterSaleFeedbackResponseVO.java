package com.yhd.fa.marketing.cms.pojo.vo.response;


import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: OrderAfterSaleFeedbackResponseVO.java, v 0.1 2025/6/26 16:33 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderAfterSaleFeedbackResponseVO extends BaseVO {

    /**
     * 满意度（1 - 5星，对应1 - 5值  ）
     */
    @Schema(description = "满意度（1 - 5星，对应1 - 5值  ）", example = "1")
    private Byte satisfaction;

    /**
     * 问题是否解决，1：已解决；2：未解决
     */
    @Schema(description = "问题是否解决，1：已解决；2：未解决", example = "1")
    private Byte problemSolved;

    /**
     * 整体感受，1：非常省心；2：省心；3：一般；4：费力；5：非常费力
     */
    @Schema(description = "整体感受，1：非常省心；2：省心；3：一般；4：费力；5：非常费力", example = "1")
    private Byte overallFeeling;

    /**
     * 用户建议
     */
    @Schema(description = "用户建议", example = "建议")
    private String suggestion;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-01-01 10:10:10")
    private LocalDateTime createdDate;

}
