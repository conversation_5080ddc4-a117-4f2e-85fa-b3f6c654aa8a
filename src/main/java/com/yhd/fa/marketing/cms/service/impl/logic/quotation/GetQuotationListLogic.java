package com.yhd.fa.marketing.cms.service.impl.logic.quotation;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.constant.QuotationPeriodConstant;
import com.yhd.fa.marketing.cms.constant.QuotationStatusConstant;
import com.yhd.fa.marketing.cms.constant.QuotationSynchronizationStatusConstant;
import com.yhd.fa.marketing.cms.dao.QuotationClickHouseDAO;
import com.yhd.fa.marketing.cms.dao.QuotationDAO;
import com.yhd.fa.marketing.cms.mapper.QuotationMapper;
import com.yhd.fa.marketing.cms.pojo.dto.UserCodeAndCompanyCodeDTO;
import com.yhd.fa.marketing.cms.pojo.po.EnquiryLogPO;
import com.yhd.fa.marketing.cms.pojo.po.QuotationPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.request.QuotationInfoRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.QuotationInfoResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetQuotationListLogic.java, v0.1 2022/12/2 14:05 yehuasheng Exp $
 */
@Component
public class GetQuotationListLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetQuotationListLogic.class.getName());

    /**
     * 72小时
     */
    private static final Integer SEVENTY_TWO = 72;

    /**
     * 报价单mapper
     */
    @Resource
    private QuotationMapper quotationMapper;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private QuotationClickHouseLogic queryCrossDataLogic;

    @Resource
    private QuotationDAO quotationDAO;

    @Resource
    private QuotationClickHouseDAO quotationClickHouseDAO;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 设置是否需要审核
     *
     * @param quotationInfoResponseVO 报价单信息
     */
    private static void setWhetherAuditIsRequired(QuotationInfoResponseVO quotationInfoResponseVO) {
        if (StringUtils.equals(quotationInfoResponseVO.getSynchronizationStatus(), QuotationSynchronizationStatusConstant.SYNCHRONIZED)
                && (StringUtils.equals(quotationInfoResponseVO.getQuotationStatus(), QuotationStatusConstant.QUOTATION))) {
            long time = Duration.between(quotationInfoResponseVO.getSynchronizeDate(), LocalDateTime.now()).toHours();
            if (time >= SEVENTY_TWO) {
                quotationInfoResponseVO.setQuotationPeriod(SEVENTY_TWO);
            } else if (time >= CommonConstant.THREE) {
                quotationInfoResponseVO.setQuotationPeriod(CommonConstant.THREE);
            } else {
                quotationInfoResponseVO.setQuotationPeriod(time);
            }

        }
    }

    /**
     * 获取报价单列表
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @return BusinessResponse<PageInfo < QuotationInfoResponseVO>>
     */
    public BusinessResponse<PageInfo<QuotationInfoResponseVO>> exec(QuotationInfoRequestVO quotationInfoRequestVO) {
        logger.info("start exec get quotation list logic.");

        // 设置查询的条件
        MPJLambdaWrapper<QuotationPO> queryWrapper = setQueryWrapper(quotationInfoRequestVO);

        if (null == queryWrapper) {
            return BusinessResponse.ok(PageInfo.of(new ArrayList<>()));
        }
        // 查询报价单列表
        List<QuotationInfoResponseVO> quotationList = queryCrossData(quotationInfoRequestVO, queryWrapper);

        // 设置分页
        PageInfo<QuotationInfoResponseVO> pageInfo = new PageInfo<>(quotationList);

        // 设置查询后的结果其余的值 并且 返回
        return setQuotationOtherValue(pageInfo);
    }

    private List<QuotationInfoResponseVO> queryCrossData(QuotationInfoRequestVO requestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("start query cross quotation data.");

        try (Page<QuotationInfoResponseVO> page = new Page<>(requestVO.getPageNum(), requestVO.getPageSize())) {

            // 获取查询总条数的sql
            String fullSql = queryCrossDataLogic.getSelectOrderListFullSql(queryWrapper);

            // 查询MySql和ClickHouse的总条数
            Integer mysqlTotal = quotationDAO.countOrderList(fullSql);
            Integer clickHouseTotal = quotationClickHouseDAO.countOrderList(fullSql);

            // 计算总页数
            int total = clickHouseTotal + mysqlTotal;
            int residueP = total % requestVO.getPageSize();
            int totalPage = total / requestVO.getPageSize();
            if (residueP > CommonConstant.ZERO) {
                totalPage = totalPage + CommonConstant.ONE;
            }

            int startMysql = (requestVO.getPageNum() - CommonConstant.ONE) * requestVO.getPageSize();
            String lastSql = FaDocMarketingCmsConstant.LIMIT_STR + startMysql + CommonConstant.SYMBOL_COMMA + requestVO.getPageSize();

            int mysqlPageTotal = requestVO.getPageNum() * requestVO.getPageSize();
            //总条数小于mysql总条数，就走查询mysql
            if (mysqlPageTotal <= mysqlTotal) {

                logger.info("start query mysql quotation list.");

                queryWrapper.last(lastSql);
                List<QuotationInfoResponseVO> mySqlData = quotationMapper.selectJoinList(QuotationInfoResponseVO.class, queryWrapper);

                // 设置分页数据
                page.setTotal(total);
                page.setPages(totalPage);
                page.setPageSize(mySqlData.size());
                page.addAll(mySqlData);

                return page;
            }

            // 剩余数量小于每页显示的条数，查两个库
            int residue = mysqlPageTotal - mysqlTotal;
            if (residue < requestVO.getPageSize()) {

                logger.info("start query mysql and click house quotation list.");

                queryWrapper.last(lastSql);

                List<QuotationInfoResponseVO> mySqlData = quotationMapper.selectJoinList(QuotationInfoResponseVO.class, queryWrapper);
                List<QuotationInfoResponseVO> clickHouseData = queryCrossDataLogic.selectListDeepForQueryCrossData(requestVO, FaDocMarketingCmsConstant.LIMIT_0_STR + residue);

                // 合并数据
                mySqlData.addAll(clickHouseData);

                // 按创建时间排序
                mySqlData.sort((o1, o2) -> o2.getCreatedDate().compareTo(o1.getCreatedDate()));

                // 设置分页数据
                page.setTotal(total);
                page.setPages(totalPage);
                page.setPageSize(mySqlData.size());
                page.addAll(mySqlData);

                return page;
            }

            logger.info("start query click house quotation list.");

            // 计算查询click house数据的页数
            int mysqlPage = Math.toIntExact(mysqlTotal % requestVO.getPageSize());
            int mysqlTotalPage = Math.toIntExact(mysqlTotal / requestVO.getPageSize()) + CommonConstant.ONE;
            int clickHouseStartPage = requestVO.getPageNum() - mysqlTotalPage - CommonConstant.ONE;
            int clickHouseStart = clickHouseStartPage * requestVO.getPageSize() + (requestVO.getPageSize() - mysqlPage);

            // 查询数据
            List<QuotationInfoResponseVO> clickHouseData = queryCrossDataLogic.selectListDeepForQueryCrossData(requestVO,
                    FaDocMarketingCmsConstant.LIMIT_STR + clickHouseStart + CommonConstant.SYMBOL_COMMA + requestVO.getPageSize());

            // 设置分页数据
            page.setTotal(total);
            page.setPages(totalPage);
            page.setPageSize(clickHouseData.size());
            page.addAll(clickHouseData);

            return page;

        } catch (Exception e) {
            logger.error("query cross data quotation error:{}", e.getMessage());
            return new Page<>();
        }

    }

    /**
     * 设置查询条件
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @return MPJLambdaWrapper<QuotationPO>
     */
    private MPJLambdaWrapper<QuotationPO> setQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO) {
        logger.info("set get quotation list query wrapper.");

        // 设置查询的字段以及排序
        MPJLambdaWrapper<QuotationPO> queryWrapper = new MPJLambdaWrapper<QuotationPO>()
                .selectAs(QuotationPO::getId, QuotationInfoResponseVO::getQuotationId)
                .selectAs(QuotationPO::getQuotationNumber, QuotationInfoResponseVO::getQuotationNumber)
                .selectAs(QuotationPO::getUserCode, QuotationInfoResponseVO::getUserCode)
                .selectAs(QuotationPO::getPurchaseUserCode, QuotationInfoResponseVO::getPurchaseUserCode)
                .selectAs(QuotationPO::getCompanyCode, QuotationInfoResponseVO::getCompanyCode)
                .selectAs(QuotationPO::getCompanyName, QuotationInfoResponseVO::getCompanyName)
                .selectAs(QuotationPO::getPayablePrice, QuotationInfoResponseVO::getTotalPrice)
                .selectAs(QuotationPO::getCreatedBy, QuotationInfoResponseVO::getCreatedBy)
                .selectAs(QuotationPO::getDeleteStatus, QuotationInfoResponseVO::getDeleteStatus)
                .selectAs(QuotationPO::getQuotationStatus, QuotationInfoResponseVO::getQuotationStatus)
                .selectAs(QuotationPO::getIsInsideCreated, QuotationInfoResponseVO::getIsInsideCreated)
                .selectAs(QuotationPO::getInsideEmployeeCode, QuotationInfoResponseVO::getInsideEmployeeCode)
                .selectAs(QuotationPO::getEmployeeName, QuotationInfoResponseVO::getEmployeeName)
                .selectAs(QuotationPO::getSynchronizationStatus, QuotationInfoResponseVO::getSynchronizationStatus)
                .selectAs(QuotationPO::getSynchronizationDate, QuotationInfoResponseVO::getSynchronizeDate)
                .selectAs(QuotationPO::getCreatedDate, QuotationInfoResponseVO::getCreatedDate)
                .selectAs(QuotationPO::getExamineDate, QuotationInfoResponseVO::getExamineDate)
                .selectAs(QuotationPO::getQuotationCompletionDate, QuotationInfoResponseVO::getQuotationCompletionDate)
                .selectAs(QuotationPO::getIsExamine, QuotationInfoResponseVO::getIsExamine)
                .selectAs(QuotationPO::getTransferOrder, QuotationInfoResponseVO::getTransferOrder)
                .orderByDesc(QuotationPO::getCreatedDate);

        // 如果有传递报价单号
        setQuotationNumberQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传时间
        setTimeQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传企业名称
        setCompanyNameQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传状态
        setQuotationStatusQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有超报价时效
        setQuotationPeriodQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传建单内部人
        setInsideEmployeeCodeQueryWrapper(quotationInfoRequestVO, queryWrapper);

        // 如果有传是否转订单
        setTransferOrderQueryWrapper(quotationInfoRequestVO, queryWrapper);

//        //判断是否查看全部
//        boolean showAllData = SecurityUtil.ifShowAllData();
//        //若不是看全部则取相关联企业查询
//        if (!showAllData) {
//            List<String> companyCodeList = SecurityUtil.getOperatorCompanyCode();
//            if (CollUtil.isNotEmpty(companyCodeList)) {
//                queryWrapper.in(QuotationPO::getCompanyCode, companyCodeList);
//            } else {
//                //关联企业为空 直接返回空交给上一层判断
//                return null;
//            }
//        }
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, EnquiryLogPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }


        // 返回查询的条件
        return queryWrapper;
    }

    /**
     * 设置查询报价单号
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setQuotationNumberQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("set quotation number query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getQuotationNumber())) {
            // 将报价单号以“,”号隔开
            List<String> quotationNumberList = Arrays.asList(quotationInfoRequestVO.getQuotationNumber().split(CommonConstant.SYMBOL_COMMA));

            // 如果是拆分后超过1个以上的，代码是查询多个 那么要用in 查询 如果是只有一个那么需要模糊查询
            if (quotationNumberList.size() > CommonConstant.ONE) {
                queryWrapper.in(QuotationPO::getQuotationNumber, quotationNumberList);
            } else {
                queryWrapper.likeRight(QuotationPO::getQuotationNumber, quotationInfoRequestVO.getQuotationNumber().trim());
            }
        }
    }

    /**
     * 设置查询传时间
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setTimeQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("set time query wrapper.");

        if (null != quotationInfoRequestVO.getStartTime()) {
            queryWrapper.ge(QuotationPO::getCreatedDate, quotationInfoRequestVO.getStartTime());
        }
        if (null != quotationInfoRequestVO.getEndTime()) {
            queryWrapper.le(QuotationPO::getCreatedDate, quotationInfoRequestVO.getEndTime());
        }
    }

    /**
     * 设置查询企业名称
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setCompanyNameQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("set company name query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getCompanyName())) {
            queryWrapper.likeRight(QuotationPO::getCompanyName, quotationInfoRequestVO.getCompanyName().trim());
        }
    }

    /**
     * 设置查询报价单状态
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setQuotationStatusQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("set quotation status query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getQuotationStatus())) {
            queryWrapper.eq(QuotationPO::getQuotationStatus, quotationInfoRequestVO.getQuotationStatus());
        }
    }

    /**
     * 设置报价时效查询
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setQuotationPeriodQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("set quotation period query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getQuotationPeriod())) {
            // 先设置已同步
            queryWrapper.eq(QuotationPO::getSynchronizationStatus, QuotationSynchronizationStatusConstant.SYNCHRONIZED);
            queryWrapper.eq(QuotationPO::getQuotationStatus, QuotationStatusConstant.QUOTATION);

            // 设置查询的时间点
            LocalDateTime nowTime = LocalDateTime.now();

            switch (quotationInfoRequestVO.getQuotationPeriod()) {
                // 1小时
                case QuotationPeriodConstant.ONE_HOURS:
                    queryWrapper.le(QuotationPO::getSynchronizationDate, nowTime.minusHours(CommonConstant.ONE));
                    queryWrapper.gt(QuotationPO::getSynchronizationDate, nowTime.minusHours(CommonConstant.TWO));
                    break;
                // 2小时
                case QuotationPeriodConstant.TWO_HOURS:
                    queryWrapper.le(QuotationPO::getSynchronizationDate, nowTime.minusHours(CommonConstant.TWO));
                    queryWrapper.gt(QuotationPO::getSynchronizationDate, nowTime.minusHours(CommonConstant.THREE));
                    break;
                // 3小时
                case QuotationPeriodConstant.THREE_HOURS:
                    queryWrapper.le(QuotationPO::getSynchronizationDate, nowTime.minusHours(CommonConstant.THREE));
                    queryWrapper.gt(QuotationPO::getSynchronizationDate, nowTime.minusHours(SEVENTY_TWO));
                    break;
                // 72小时
                case QuotationPeriodConstant.SEVENTY_TWO_HOURS:
                    queryWrapper.le(QuotationPO::getSynchronizationDate, nowTime.minusHours(SEVENTY_TWO));
                    break;
                default:
            }
        }
    }

    /**
     * 设置查询内部人员创建报价单
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setInsideEmployeeCodeQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("set inside employee code query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getInsideEmployeeCode())) {
            queryWrapper.eq(QuotationPO::getInsideEmployeeCode, quotationInfoRequestVO.getInsideEmployeeCode());
        }
    }

    /**
     * 设置查询是否已转订单
     *
     * @param quotationInfoRequestVO 报价单列表请求参数
     * @param queryWrapper           查询where条件
     */
    private void setTransferOrderQueryWrapper(QuotationInfoRequestVO quotationInfoRequestVO, MPJLambdaWrapper<QuotationPO> queryWrapper) {
        logger.info("set transfer order query wrapper.");

        if (StringUtils.isNotBlank(quotationInfoRequestVO.getTransferOrder())) {
            queryWrapper.eq(QuotationPO::getTransferOrder, quotationInfoRequestVO.getTransferOrder());
        }
    }

    /**
     * 设置报价单的其他参数值
     *
     * @param pageInfo 分页的报价单列表
     * @return BusinessResponse<PageInfo < QuotationInfoResponseVO>>
     */
    private BusinessResponse<PageInfo<QuotationInfoResponseVO>> setQuotationOtherValue(PageInfo<QuotationInfoResponseVO> pageInfo) {
        logger.info("set quotation other value");

        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            // 获取用户名字 采购人名字
            Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> userBaseMap = getUserBaseInfo(pageInfo.getList());

            pageInfo.getList().forEach(quotationInfoResponseVO -> {
                // 设置是否需要审核
                setWhetherAuditIsRequired(quotationInfoResponseVO);

                // 设置用户名字以及跟单得信息
                Optional.ofNullable(userBaseMap.get(quotationInfoResponseVO.getUserCode()))
                        .ifPresent(userAndCompanyAndMerchandiserResponseVO -> {
                            // 设置用户名称
                            quotationInfoResponseVO.setUserName(userAndCompanyAndMerchandiserResponseVO.getUserInfo().getUserName());
                            // 设置跟单员信息
                            Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserResponseVO -> {
                                // 设置跟单以及联系方式
                                quotationInfoResponseVO.setMerchandiser(merchandiserResponseVO.getEmployeeCode() + CommonConstant.SLASH + merchandiserResponseVO.getEmployeeName());
                                quotationInfoResponseVO.setMerchandiserContact(merchandiserResponseVO.getMobile());
                            });
                            // 设置业务员信息
                            Optional.ofNullable(userAndCompanyAndMerchandiserResponseVO.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> {
                                // 设置业务员名称联系方式
                                quotationInfoResponseVO.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName());
                                quotationInfoResponseVO.setSalesmanContact(salesMan.getMobile());
                            });
                        });
                // 设置采购人名称
                Optional.ofNullable(userBaseMap.get(quotationInfoResponseVO.getPurchaseUserCode())).ifPresent(userAndCompanyAndMerchandiserResponseVO -> quotationInfoResponseVO.setPurchaseUserName(userAndCompanyAndMerchandiserResponseVO.getUserInfo().getUserName()));
            });
        }

        return BusinessResponse.ok(pageInfo);
    }

    /**
     * 获取用户基础信息
     *
     * @param quotationList 报价单集合
     * @return Map<String, BaseUserInfoResponseVO>
     */
    private Map<String, CompanyAndUserAndMerchandiserAndResourcesResponseVO> getUserBaseInfo(List<QuotationInfoResponseVO> quotationList) {
        logger.info("get user base info.");

        // 获取并设置用户编码和企业编码
        List<UserCodeAndCompanyCodeDTO> userCodeAndCompanyCodeDTO = quotationList
                .stream()
                .map(quotationInfoResponseVO
                        -> UserCodeAndCompanyCodeDTO
                        .builder()
                        .userCode(quotationInfoResponseVO.getUserCode())
                        .purchaseUserCode(quotationInfoResponseVO.getPurchaseUserCode())
                        .companyCode(quotationInfoResponseVO.getCompanyCode())
                        .build())
                .collect(Collectors.toList());

        // 请求用户中心获取用户基础信息
        return userBucCmsService.getUserAndCompanyBaseInfo(userCodeAndCompanyCodeDTO);
    }
}
