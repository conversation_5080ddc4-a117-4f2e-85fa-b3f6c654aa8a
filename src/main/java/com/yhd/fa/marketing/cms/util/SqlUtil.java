package com.yhd.fa.marketing.cms.util;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: SqlUtil.java, v0.1 2022/9/27 11:11 yehuasheng Exp $
 */
public class SqlUtil {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(SqlUtil.class.getName());

    private SqlUtil() {
    }

    /**
     * 设置模糊或者IN查询
     *
     * @param value         入参
     * @param lambdaWrapper lambdaWrapper
     * @param column        查询字段
     * @param <R>           R
     */
    public static <R> void setLambdaWrapperLikeOrInSearch(String value, MPJLambdaWrapper<R> lambdaWrapper, SFunction<R, ?> column) {

        if (StringUtils.isNotBlank(value)) {
            // 将订单以“,”号隔开
            List<String> orderNumberList = Arrays.asList(value.split(CommonConstant.SYMBOL_COMMA));
            if (orderNumberList.size() > CommonConstant.ONE) {
                lambdaWrapper.in(column, orderNumberList);
            } else {
                lambdaWrapper.likeRight(column, value);
            }
        }
    }

    /**
     * 判断是否业务员，如果业务员则添加业务员条件查询
     *
     * @param lambdaWrapper 查询条件
     * @param column        查询字段
     * @param <R>           R
     */
    public static <R> void setOperatorCompanyCodeSearchSql(MPJLambdaWrapper<R> lambdaWrapper, SFunction<R, ?> column) {
        logger.info("set operator company code search sql.");

        List<String> companyCodeList = SecurityUtil.getOperatorCompanyCode();
        if (CollUtil.isNotEmpty(companyCodeList)) {
            lambdaWrapper.in(column, companyCodeList);
        }
    }
}
