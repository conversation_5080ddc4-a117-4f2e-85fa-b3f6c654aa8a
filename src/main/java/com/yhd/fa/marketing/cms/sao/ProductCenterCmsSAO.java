package com.yhd.fa.marketing.cms.sao;

import com.alibaba.nacos.common.http.param.MediaType;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionCategoryRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ApproveQuotationSelectionTypeRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.request.ProductInfoRequestVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationCategoryResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ApproveQuotationTypeResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.ProductInfoResponseVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: ProductCenterCmsSAO.java, v0.1 2022/12/19 10:50 yehuasheng Exp $
 */
@FeignClient(name = "yhd-service-fa-product-center-cms")
public interface ProductCenterCmsSAO {
    /**
     * 获取产品分类、代码信息
     *
     * @param approveQuotationSelectionTypeRequestVO 请求产品分类代码的参数
     * @return BusinessResponse<List < ApproveQuotationTypeResponseVO>>
     */
    @PostMapping(value = "/product/center/cms/v1/0/product/linkage", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    BusinessResponse<List<ApproveQuotationTypeResponseVO>> getProductTypeCode(@RequestBody ApproveQuotationSelectionTypeRequestVO approveQuotationSelectionTypeRequestVO);


    /**
     * 获取产品类别信息
     *
     * @param approveQuotationSelectionCategoryRequestVO 类型请求参数
     * @return BusinessResponse<List < ApproveQuotationCategoryResponseVO>>
     */
    @PostMapping(value = "/product/center/cms/v1/0/product/getCategoryCodeByCode", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    BusinessResponse<List<ApproveQuotationCategoryResponseVO>> getProductCategoryCode(@RequestBody ApproveQuotationSelectionCategoryRequestVO approveQuotationSelectionCategoryRequestVO);

    /**
     * 获取产品代码信息
     *
     * @param productCodeList 产品代码
     * @return BusinessResponse<List < ProductInfoResponseVO>>
     */
    @PostMapping(value = "/product/center/cms/v1/0/product/info", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    BusinessResponse<List<ProductInfoResponseVO>> getProductInfo(@RequestBody ProductInfoRequestVO productCodeList);
}
