package com.yhd.fa.marketing.cms.service.impl.controller;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.pojo.vo.response.ExcelFileAnalysisResponseVO;
import com.yhd.fa.marketing.cms.service.controller.QuotationUploadFileService;
import com.yhd.fa.marketing.cms.service.impl.logic.quotation.ExcelFileAnalysisLogic;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: QuotationUploadFileServiceImpl.java, v0.1 2023/1/3 17:52 yehuasheng Exp $
 */
@Service
public class QuotationUploadFileServiceImpl implements QuotationUploadFileService {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(QuotationUploadFileServiceImpl.class.getName());

    /**
     * 解析excel的逻辑
     */
    @Resource
    private ExcelFileAnalysisLogic excelFileAnalysisLogic;

    /**
     * 解析excel文件
     *
     * @param excelFile excel文件流
     * @param userCode  用户编码
     * @return BusinessResponse<List < ExcelFileAnalysisResponseVO>>
     */
    @Override
    public BusinessResponse<List<ExcelFileAnalysisResponseVO>> excelFileAnalysis(MultipartFile excelFile, String userCode) {
        logger.info("start analysis excel file service.");

        // 执行解析excel逻辑
        return excelFileAnalysisLogic.exec(excelFile, userCode);
    }
}
