package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: ApproveQuotationResponseVO.java, v0.1 2022/12/8 9:18 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApproveQuotationResponseVO extends BaseVO {
    /**
     * 报价单id
     */
    @Schema(description = "报价单id", example = "2bdb24c30f6e49ce8eb23d66672c39a1")
    private String quotationId;

    /**
     * 报价单号
     */
    @Schema(description = "报价单号", example = "YB202211161438419779SXBQ")
    private String quotationNumber;

    /**
     * 制单人编码
     */
    @Schema(description = "制单人编码", example = "12")
    private String userCode;

    /**
     * 制单人名称
     */
    @Schema(description = "用户名称", example = "你猜")
    private String userName;

    /**
     * 采购人
     */
    @Schema(description = "采购人编号", example = "12")
    private String purchaseUserCode;

    /**
     * 采购人名称
     */
    @Schema(description = "采购人名称", example = "你猜")
    private String purchaseUserName;

    /**
     * 企业编码
     */
    @Schema(description = "企业编码", example = "A001")
    private String companyCode;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "你猜2")
    private String companyName;

    /**
     * 跟单名称
     */
    @Schema(description = "跟单员名称", example = "林杏金")
    private String merchandiser;

    /**
     * 跟单员联系方式
     */
    @Schema(description = "跟单员联系方式", example = "13111111111")
    private String merchandiserContact;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2022-12-08 09:22:30")
    private LocalDateTime createdDate;
}
