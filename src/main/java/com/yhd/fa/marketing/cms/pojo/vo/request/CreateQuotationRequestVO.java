package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.fa.marketing.cms.annotation.ValueInEnum;
import com.yhd.fa.marketing.cms.constant.ChannelTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: CreateQuotationRequestVO.java, v0.1 2023/1/4 9:53 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreateQuotationRequestVO extends BaseVO {
    /**
     * 用户编码
     */
    @Schema(description = "用户编码", example = "12")
    @NotEmpty(message = "用户编码不能为空")
    private String userCode;

    /**
     * 采购用户id
     */
    @Schema(description = "采购用户id", example = "12")
    @NotEmpty(message = "采购用户id不能为空")
    private String purchaseUserCode;

    /**
     * 客户报价单id
     */
    @Schema(description = "客户报价单id", example = "10002564")
    private String customerQuotationCode;

    /**
     * 渠道来源 pc电脑端 wap手机端 app端 wechat微信 applets小程序
     */
    @Schema(description = "渠道来源 pc电脑端 wap手机端 app端 wechat微信 applet小程序", example = "pc", allowableValues = {ChannelTypeConstant.APP, ChannelTypeConstant.APPLET, ChannelTypeConstant.PC, ChannelTypeConstant.WAP, ChannelTypeConstant.WECHAT})
    @ValueInEnum(matchTarget = {ChannelTypeConstant.APP, ChannelTypeConstant.APPLET, ChannelTypeConstant.PC, ChannelTypeConstant.WAP, ChannelTypeConstant.WECHAT}, message = "渠道来源不正确")
    private String channelType;

    /**
     * 型号信息
     */
    @Schema(description = "请求创建报价单的型号集合")
    @Valid
    private List<CreateQuotationDetailRequestVO> products;
}
