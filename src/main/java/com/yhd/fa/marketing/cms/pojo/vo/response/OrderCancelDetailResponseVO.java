package com.yhd.fa.marketing.cms.pojo.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: OrderCancelDetailResponseVO.java, v0.1 2023/2/22 15:11 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderCancelDetailResponseVO extends OrderCancelResponseVO {
    /**
     * 同步时间
     */
    @Schema(description = "订单取消同步erp时间/处理中时间", example = "2022-01-01 10:10:10")
    private LocalDateTime syncDate;

    /**
     * 最终节点处理结果时间
     */
    @Schema(description = "最终节点处理结果时间，需要根据取消类型以及支付方式是否展示", example = "2022-01-01 10:10:10")
    private LocalDateTime handelDate;

    /**
     * 退款时间
     */
    @Schema(description = "最终节点处理结果时间/退款时间，需要根据取消类型以及支付方式是否展示", example = "2022-01-01 10:10:10")
    private LocalDateTime refundDate;

    /**
     * 取消明细总项数
     */
    @Schema(description = "取消明细总项数", example = "2")
    private int cancelCount;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因", example = "我就是不想要 吹啊")
    private String reason;

    /**
     * 驳回原因
     */
    @Schema(description = "驳回原因", example = "就是不给你 吹啊")
    private String notAgree;

    /**
     * 取消的金额
     */
    @Schema(description = "取消的金额", example = "10.00")
    private BigDecimal totalMoney;

    /**
     * 订单id
     */
    @Schema(description = "订单id", example = "a83e6926e3034c51949564ee4a697e7f")
    private String orderId;

    /**
     * 是否内部人员帮客户申请 true是 false不是
     */
    @Schema(description = "是否内部人员帮客户申请 true是 false不是", example = "false")
    private String isInsideCreated;

    /**
     * 内部人员工号
     */
    @Schema(description = "内部人员工号", example = "yhd575")
    private String insideEmployeeCode;

    /**
     * 内部人员名称
     */
    @Schema(description = "内部人员名称", example = "最帅的那位")
    private String insideEmployeeName;

    /**
     * 订单取消的明细
     */
    @Schema(description = "订单取消明细")
    private List<OrderCancelDetailListResponseVO> orderCancelDetails;
}
