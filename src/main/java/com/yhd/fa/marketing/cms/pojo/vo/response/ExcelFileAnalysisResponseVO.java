package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version Id: ExcelFileAnalysisResponseVO.java, v0.1 2022/8/26 14:01 yehuasheng Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExcelFileAnalysisResponseVO extends BaseVO {
    /**
     * 客户型号
     */
    @Schema(description = "客户型号")
    private String customerModel;

    /**
     * 产品型号
     */
    @Schema(description = "产品型号")
    private String model;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Long quantity;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 客户产品名称
     */
    @Schema(description = "客户产品名称")
    private String customerProductName;

    /**
     * 询价的结果
     */
    @Valid
    @Schema(description = "询价的结果")
    private QuotationResponseVO quotationResult;
}
