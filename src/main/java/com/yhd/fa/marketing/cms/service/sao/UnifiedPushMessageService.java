package com.yhd.fa.marketing.cms.service.sao;

import com.yhd.common.pojo.vo.BusinessResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: UnifiedPushMessageService.java, v0.1 2023/3/6 20:06 yehuasheng Exp $
 */
public interface UnifiedPushMessageService {
    /**
     * 发送钉钉告警
     *
     * @param templateId     模板id
     * @param receivers      钉钉发送的人
     * @param replaceContent 替换的内容
     * @param atAll          是否@所有人 true是 false否
     * @return BusinessResponse<Void>
     */
    BusinessResponse<String> sendDingDingRobot(String templateId, List<String> receivers, Map<String, Object> replaceContent, String atAll);

    /**
     * 发送钉钉多对多
     *
     * @param templateId     模板id
     * @param receivers      钉钉发送的人
     * @param replaceContent 替换的内容
     * @return BusinessResponse<String>
     */
    BusinessResponse<String> sendDingDingManyToMany(String templateId, List<String> receivers, List<Map<String, Object>> replaceContent);

    /**
     * 发送钉钉一对多
     *
     * @param templateId     模板id
     * @param receivers      钉钉发送的人
     * @param replaceContent 替换的内容
     * @return BusinessResponse<String>
     */
    BusinessResponse<String> sendDingDingOneToMany(String templateId, List<String> receivers, Map<String, Object> replaceContent);

    /**
     * 发送短信多对多
     *
     * @param templateId     模板id
     * @param phoneNumber    手机号码
     * @param replaceContent 短信多对多消息推送传入
     * @param sign           短信签名，当短信为多对多发送时，有多少个手机号，传多少个签名；
     * @return BusinessResponse<String>
     */
    BusinessResponse<String> sendSmsToMany(String templateId, List<String> phoneNumber, List<Map<String, Object>> replaceContent, List<String> sign);

    /**
     * 发送短信
     *
     * @param templateId     模板id
     * @param phoneNumber    手机号码
     * @param replaceContent 短信多对多消息推送传入
     * @param sign           短信签名
     * @return BusinessResponse<String>
     */
    BusinessResponse<String> sendSms(String templateId, List<String> phoneNumber, Map<String, Object> replaceContent, String sign);

    /**
     * 发送邮件 多对多
     *
     * @param templateId     模板id
     * @param emailAddress   邮箱
     * @param replaceContent 内容
     * @param type           邮件类型：order订单，quotation报价单，other其他；订单报价单邮件包含明细项时传入order/quotation，不包含明细项直接传other
     * @return BusinessResponse<String>
     */
    BusinessResponse<String> sendEmailToMany(String templateId, List<String> emailAddress, List<Map<String, Object>> replaceContent, String type);

    /**
     * 发送邮件
     *
     * @param templateId     模板id
     * @param emailAddress   邮箱
     * @param replaceContent 数据
     * @param type           邮件类型：order订单，quotation报价单，other其他；订单报价单邮件包含明细项时传入order/quotation，不包含明细项直接传other
     * @return BusinessResponse<String>
     */
    BusinessResponse<String> sendEmail(String templateId, List<String> emailAddress, Map<String, Object> replaceContent, String type);
}
