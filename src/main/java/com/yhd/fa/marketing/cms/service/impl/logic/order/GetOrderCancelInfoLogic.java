package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.CompanyAndUserAndMerchandiserAndResourcesResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.constant.FaDocMarketingCmsConstant;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.enums.OrderCancelStatusEnum;
import com.yhd.fa.marketing.cms.enums.OrderCancelTypeEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCancelListMapper;
import com.yhd.fa.marketing.cms.mapper.OrderCancelMapper;
import com.yhd.fa.marketing.cms.pojo.po.OrderCancelListPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCancelPO;
import com.yhd.fa.marketing.cms.pojo.po.SaleCompanyRelationPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelDetailListResponseVO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCancelDetailResponseVO;
import com.yhd.fa.marketing.cms.service.impl.logic.dataAuth.DataAuthLogic;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderCancelInfoLogic.java, v0.1 2023/2/22 17:30 yehuasheng Exp $
 */
@Component
public class GetOrderCancelInfoLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderCancelInfoLogic.class.getName());

    /**
     * 订单取消mapper
     */
    @Resource
    private OrderCancelMapper orderCancelMapper;

    /**
     * 订单取消明细mapper
     */
    @Resource
    private OrderCancelListMapper orderCancelListMapper;

    /**
     * 用户服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private DataAuthLogic dataAuthLogic;

    /**
     * 执行获取订单取消详情
     *
     * @param orderCancelId 订单取消id
     * @return BusinessResponse<OrderCancelDetailResponseVO>
     */
    public BusinessResponse<OrderCancelDetailResponseVO> exec(String orderCancelId) {
        logger.info("start get order cancel detail logic.");

        // 获取订单取消详情
        OrderCancelDetailResponseVO orderCancelInfo = getOrderCancelInfo(orderCancelId);

        // 判断是否存在订单取消
        if (ObjectUtil.isNull(orderCancelInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_CANCEL_IS_NOT_EXISTS);
        }

        // 设置订单取消明细
        setOrderCancelDetail(orderCancelInfo);

        // 设置订单取消其他值
        setOrderCancelInfoOtherValue(orderCancelInfo);

        // 输出
        return BusinessResponse.ok(orderCancelInfo);
    }

    /**
     * 查询订单取消信息
     *
     * @param orderCancelId 取消订单的id
     * @return OrderCancelDetailResponseVO
     */
    private OrderCancelDetailResponseVO getOrderCancelInfo(String orderCancelId) {
        logger.info("get order cancel info.");

        // 设置查询条件
        MPJLambdaWrapper<OrderCancelPO> queryWrapper = OrderUtil.setOrderCancelQueryWrapperSelectAs()
                .selectAs(OrderCancelPO::getSyncDate, OrderCancelDetailResponseVO::getSyncDate)
                .selectAs(OrderCancelPO::getHandelDate, OrderCancelDetailResponseVO::getHandelDate)
                .selectAs(OrderCancelPO::getRefundDate, OrderCancelDetailResponseVO::getRefundDate)
                .selectAs(OrderCancelPO::getReason, OrderCancelDetailResponseVO::getReason)
                .selectAs(OrderCancelPO::getNotAgree, OrderCancelDetailResponseVO::getNotAgree)
                .selectAs(OrderCancelPO::getTotalMoney, OrderCancelDetailResponseVO::getTotalMoney)
                .selectAs(OrderCancelPO::getOrderId, OrderCancelDetailResponseVO::getOrderId)
                .selectAs(OrderCancelPO::getIsInsideCreated, OrderCancelDetailResponseVO::getIsInsideCreated)
                .selectAs(OrderCancelPO::getInsideEmployeeCode, OrderCancelDetailResponseVO::getInsideEmployeeCode)
                .selectAs(OrderCancelPO::getCreatedBy, OrderCancelDetailResponseVO::getInsideEmployeeName)
                .eq(OrderCancelPO::getId, orderCancelId)
                .last(FaDocMarketingCmsConstant.LIMIT);

        //判断是否查看全部
        List<String> allShowDataEmplCode = dataAuthLogic.getAllShowDataEmplCode(SecurityUtil.getAccountNo());
        if (!allShowDataEmplCode.isEmpty()) {
            queryWrapper.leftJoin(SaleCompanyRelationPO.class, SaleCompanyRelationPO::getCompanyCode, OrderCancelPO::getCompanyCode)
                    .in(SaleCompanyRelationPO::getSalesNo, allShowDataEmplCode);
        }

        return orderCancelMapper.selectJoinOne(OrderCancelDetailResponseVO.class, queryWrapper);
    }

    /**
     * 设置订单取消明细
     *
     * @param orderCancelInfo 订单详情
     */
    private void setOrderCancelDetail(OrderCancelDetailResponseVO orderCancelInfo) {
        logger.info("set order cancel detail list.");

        // 查询订单取消明细
        List<OrderCancelDetailListResponseVO> orderCancelDetailList = orderCancelListMapper.selectJoinList(OrderCancelDetailListResponseVO.class,
                new MPJLambdaWrapper<OrderCancelListPO>()
                        .selectAs(OrderCancelListPO::getId, OrderCancelDetailListResponseVO::getOrderCancelDetailId)
                        .selectAs(OrderCancelListPO::getCancelId, OrderCancelDetailListResponseVO::getOrderCancelId)
                        .selectAs(OrderCancelListPO::getSortId, OrderCancelDetailListResponseVO::getSortId)
                        .selectAs(OrderCancelListPO::getQuantity, OrderCancelDetailListResponseVO::getQuantity)
                        .selectAs(OrderCancelListPO::getProductModel, OrderCancelDetailListResponseVO::getProductModel)
                        .selectAs(OrderCancelListPO::getProductCode, OrderCancelDetailListResponseVO::getProductCode)
                        .selectAs(OrderCancelListPO::getProductName, OrderCancelDetailListResponseVO::getProductName)
                        .selectAs(OrderCancelListPO::getDiscountPrice, OrderCancelDetailListResponseVO::getDiscountPrice)
                        .selectAs(OrderCancelListPO::getTaxDiscountPrice, OrderCancelDetailListResponseVO::getTaxDiscountPrice)
                        .selectAs(OrderCancelListPO::getTotalPrice, OrderCancelDetailListResponseVO::getTotalPrice)
                        .selectAs(OrderCancelListPO::getCancelDetailStatus, OrderCancelDetailListResponseVO::getOrderCancelDetailStatus)
                        .eq(OrderCancelListPO::getCancelId, orderCancelInfo.getOrderCancelId())
                        .orderByAsc(OrderCancelListPO::getSortId)
        );
        // 判断是否为空
        if (CollUtil.isNotEmpty(orderCancelDetailList)) {
            // 设置订单取消详情明细状态名称
            Map<String, String> orderCancelStatusMap = Arrays.stream(OrderCancelStatusEnum.values()).collect(Collectors.toMap(OrderCancelStatusEnum::getOrderCancelStatus, OrderCancelStatusEnum::getOrderCancelStatusName));

            // 设置明细中的值
            orderCancelDetailList.forEach(orderCancelDetailListResponseVO ->
                    // 设置状态
                    Optional.ofNullable(orderCancelStatusMap.get(orderCancelDetailListResponseVO.getOrderCancelDetailStatus()))
                            .ifPresent(orderCancelDetailListResponseVO::setOrderCancelDetailStatusName)
            );

            // 设置订单取消明细
            orderCancelInfo.setOrderCancelDetails(orderCancelDetailList);
        }
    }

    /**
     * 设置订单取消的其他值
     *
     * @param orderCancelInfo 订单取消详情
     */
    private void setOrderCancelInfoOtherValue(OrderCancelDetailResponseVO orderCancelInfo) {
        logger.info("set order cancel info other value.");

        // 设置用户名字
        CompanyAndUserAndMerchandiserAndResourcesResponseVO userInfo = userBucCmsService.getUserInfo(orderCancelInfo.getUserCode(), orderCancelInfo.getCompanyCode());
        orderCancelInfo.setUserName(userInfo.getUserInfo().getUserName());

        // 设置跟单
        Optional.ofNullable(userInfo.getMerchandiserInfo()).filter(merchandiserInfo -> StringUtils.isNotBlank(merchandiserInfo.getEmployeeName())).ifPresent(merchandiserInfo -> orderCancelInfo.setMerchandiser(merchandiserInfo.getEmployeeCode() + CommonConstant.SLASH + merchandiserInfo.getEmployeeName()));
        // 设置业务员
        Optional.ofNullable(userInfo.getSalesManInfo()).filter(salesManInfo -> StringUtils.isNotBlank(salesManInfo.getEmployeeName())).ifPresent(salesMan -> orderCancelInfo.setSalesmanName(salesMan.getEmployeeCode() + CommonConstant.SLASH + salesMan.getEmployeeName()));

        // 设置取消状态名称
        Map<String, String> orderCancelStatusNameMap = Arrays.stream(OrderCancelStatusEnum.values()).collect(Collectors.toMap(OrderCancelStatusEnum::getOrderCancelStatus, OrderCancelStatusEnum::getOrderCancelStatusName));
        orderCancelInfo.setOrderCancelStatusName(orderCancelStatusNameMap.get(orderCancelInfo.getOrderCancelStatus()));

        // 设置订单取消类型名称
        Map<String, String> orderCancelTypeNameMap = Arrays.stream(OrderCancelTypeEnum.values()).collect(Collectors.toMap(OrderCancelTypeEnum::getOrderCancelType, OrderCancelTypeEnum::getOrderCancelTypeName));
        orderCancelInfo.setOrderCancelTypeName(orderCancelTypeNameMap.get(orderCancelInfo.getOrderCancelType()));

        // 设置取消明细总项目数
        orderCancelInfo.setCancelCount(orderCancelInfo.getOrderCancelDetails().size());
    }
}
