package com.yhd.fa.marketing.cms.service.sao;

import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.fa.marketing.cms.pojo.vo.request.ReturnCouponRequestVO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version Id: FaMarketingSystemService.java, v 0.1 2024/8/13 10:22 JiangYuHong Exp $
 */
public interface FaMarketingSystemService {

    /**
     * 退还优惠券
     *
     * @param requestVO 请求参数
     * @return businessResponse
     */
    BusinessResponse<Object> returnCoupon(@RequestBody ReturnCouponRequestVO requestVO);

}
