package com.yhd.fa.marketing.cms.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 11:49
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@Schema(description = "FB订单详情返参")
public class FbOrderDetailResponseVO extends BaseVO {

    @Schema(description = "订单id")
    private String id;

    @Schema(description = "订单编号")
    private String orderNumber;


    @Schema(description = "订单状态  unpaid 待支付、onWay 在途单、finish 已完成、cancel 已取消、closed已关闭")
    private String orderStatus;

    @Schema(description = "配送方式 inBatches分批发货、merge合并一起")
    private String shipping;

    @Schema(description = "跟单员")
    private String merchandiser;

    @Schema(description = "业务员")
    private String salesMan;

    @Schema(description = "删除状态 recycleBin 回收站  false 未删除 true 彻底删除")
    private String deleteStatus;

    @Schema(description = "支付类型   unknown 暂时未知的支付方式 aliPay 支付宝、weChatPay 微信支付、bankTransfer 银行转账、unionPay银联、monthlyKnot 月结、monthlyKnot30 月结30天、monthlyKnot60 月结60天、monthlyKnot90 月结90天")
    private String paymentType;

    @Schema(description = "采购人")
    private String createdUser;

    @Schema(description = "创建时间")
    private String createdDate;

    @Schema(description = "采购人联系方式")
    private String userContactNumber;

    @Schema(description = "支付流水号")
    private String tradeNo;

    @Schema(description = "支付时间")
    private String payDate;

    @Schema(description = "订单完成时间")
    private String receivedTime;

    @Schema(description = "订单备注")
    private String remark;

    @Schema(description = "内部单号")
    private String customerNumber;



    @Schema(description = "明细集合")
    private List<FbOrderDetailList> detailList;

    @Schema(description = "总价")
    private BigDecimal totalPrice;

    public FbOrderDetailList initFbOrderDetailList(){
        return new FbOrderDetailList();
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public class FbOrderDetailList extends BaseVO {
        @Schema(description = "明细id")
        private String id;

        @Schema(description = "明细序号")
        private Integer sortId;

        @Schema(description = "总数")
        private Long quantity;

        @Schema(description = "已发货数")
        private Long sendQuantity;

        @Schema(description = "订单明细状态;明细状态  unpaid 待支付、untreated 待确认、confirm 已确认、stockUp 备货中、takeDelivered 待收货、finish 已完成、cancelling 取消中、cancel 已取消、closed 已关闭")
        private String orderDetailStatus;

        @Schema(description = "怡合达备注")
        private String examineRemark;

        @Schema(description = "订单明细备注")
        private String remark;

        /**
         * 折扣单价
         */
        @Schema(description = "折扣单价", example = "8.00")
        private BigDecimal discountPrice;

        /**
         * 含税折扣单价
         */
        @Schema(description = "含税折扣单价", example = "9.04")
        private BigDecimal taxDiscountPrice;

        /**
         * 原总价
         */
        @Schema(description = "原总价", example = "9.04")
        private BigDecimal totalPrice;


        @Schema(description = "回复延迟交期")
        private String replyToDelivery;

        @Schema(description = "预计发货日期")
        private String estimatedShippingDate;


//
//        @Schema(description = "报价方式")
//        private String quotationType;
//
//        @Schema(description = "报价人类别")
//        private String quotationUserType;
//
//        @Schema(description = "材料")
//        private String rawMaterial;
//
//        @Schema(description = "表面处理")
//        private String surfaceTreatment;
//
//        @Schema(description = "热处理")
//        private String heatTreatment;
//
//        @Schema(description = "利润系数")
//        private String profitCoefficient;
//
//        @Schema(description = "类别")
//        private String categoryCode;

    }
}
