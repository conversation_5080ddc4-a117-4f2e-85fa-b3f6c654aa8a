package com.yhd.fa.marketing.cms.enums;

import com.yhd.fa.marketing.cms.constant.OrderCancelTypeConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version Id: OrderCancelTypeEnum.java, v0.1 2023/2/22 14:54 yehuasheng Exp $
 */
@Getter
public enum OrderCancelTypeEnum {
    ALL(OrderCancelTypeConstant.ALL, "整单取消"),
    PART(OrderCancelTypeConstant.PART, "部分取消"),
    ;

    private final String orderCancelType;
    private final String orderCancelTypeName;

    OrderCancelTypeEnum(String orderCancelType, String orderCancelTypeName) {
        this.orderCancelType = orderCancelType;
        this.orderCancelTypeName = orderCancelTypeName;
    }
}
