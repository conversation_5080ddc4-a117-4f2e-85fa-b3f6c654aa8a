package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: AddUserIntegralRequestVO.java, v 0.1 2023/3/14 15:03 JiangYuHong Exp $
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
public class AddUserIntegralRequestVO extends BaseVO {

    /**
     * 积分编号
     */
    private String integralNumber;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 平台编码
     */
    private String platform;

    /**
     * 积分记录标题
     */
    private String ruleName;

    /**
     * 积分值(添加积分值)
     */
    private Integer integral;

    /**
     * 操作人（这个获取当前登录用户的工号名称：陈泽源(10360)）
     */
    private String operator;
}
