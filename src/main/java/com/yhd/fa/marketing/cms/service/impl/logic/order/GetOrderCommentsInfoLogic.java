package com.yhd.fa.marketing.cms.service.impl.logic.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.cms.api.sdk.pojo.vo.response.base.UserInfo;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.common.BusinessResponseCommon;
import com.yhd.fa.marketing.cms.dao.OrderCommentsCategoriesRelDAO;
import com.yhd.fa.marketing.cms.dao.OrderCommentsProblemCategoriesDAO;
import com.yhd.fa.marketing.cms.enums.FaDocMarketingResponseEnum;
import com.yhd.fa.marketing.cms.mapper.OrderCommentsMapper;
import com.yhd.fa.marketing.cms.pojo.dto.OrderCommentsProblemCategoriesDTO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsCategoriesRelPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsPO;
import com.yhd.fa.marketing.cms.pojo.po.OrderCommentsProblemCategoriesPO;
import com.yhd.fa.marketing.cms.pojo.vo.response.OrderCommentsDetailResponseVO;
import com.yhd.fa.marketing.cms.service.sao.UserBucCmsService;
import com.yhd.fa.marketing.cms.util.BaseUtil;
import com.yhd.fa.marketing.cms.util.OrderUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: GetOrderCommentsInfoLogic.java, v0.1 2023/2/24 19:52 yehuasheng Exp $
 */
@Component
@RefreshScope
public class GetOrderCommentsInfoLogic {
    /**
     * 日志
     */
    private static final Logger logger = LogUtils.getLogger(GetOrderCommentsInfoLogic.class.getName());

    /**
     * 图片域名
     */
    @Value("${image-config.domain}")
    String imageDomain;
    /**
     * 订单评论mapper
     */
    @Resource
    private OrderCommentsMapper orderCommentsMapper;

    /**
     * 用户中心服务
     */
    @Resource
    private UserBucCmsService userBucCmsService;

    @Resource
    private OrderCommentsProblemCategoriesDAO orderCommentsProblemCategoriesDAO;

    @Resource
    private OrderCommentsCategoriesRelDAO orderCommentsCategoriesRelDAO;

    /**
     * 获取订单评论详情页
     *
     * @param orderCommentsId 订单评论id
     * @return BusinessResponse<OrderCommentsResponseVO>
     */
    public BusinessResponse<OrderCommentsDetailResponseVO> exec(String orderCommentsId) {
        logger.info("start get order comments detail logic.");

        // 获取订单评论详情
        OrderCommentsDetailResponseVO orderCommentsInfo = getOrderCommentsInfo(orderCommentsId);

        // 判断订单评论是否存在
        if (ObjectUtil.isNull(orderCommentsInfo)) {
            return BusinessResponseCommon.fail(FaDocMarketingResponseEnum.ORDER_COMMENTS_IS_NOT_EXISTS);
        }

        // 获取用户信息
        List<UserInfo> userInfoList = userBucCmsService.getUserBaseListByUserCode(Collections.singletonList(orderCommentsInfo.getUserCode()));
        // 设置用户名
        Optional.ofNullable(userInfoList).ifPresent(userInfo -> {
            // Find the matching user info based on the user code
            UserInfo matchingUserInfo = userInfo.stream()
                    .filter(e -> StringUtils.equals(e.getUserCode(), orderCommentsInfo.getUserCode()))
                    .findFirst()
                    .orElse(null);
            // Set the username from the matching user info, if found
            if (matchingUserInfo != null) {
                orderCommentsInfo.setUserName(matchingUserInfo.getUserName());
            }
        });

        orderCommentsInfo.setImageUrl(BaseUtil.addDomainToImageUrls(imageDomain, orderCommentsInfo.getImageUrl()));

        // 设置问题分类
        setOrderCommentsProblemCategoriesList(orderCommentsInfo);

        return BusinessResponse.ok(orderCommentsInfo);
    }

    /**
     * 设置问题分类
     *
     * @param orderCommentsInfo 订单评论详情
     */
    private void setOrderCommentsProblemCategoriesList(OrderCommentsDetailResponseVO orderCommentsInfo) {
        logger.info("get order comments problem categories list.");

        List<OrderCommentsCategoriesRelPO> orderCommentsCategoriesRelPOS = orderCommentsCategoriesRelDAO.selectList(
                new LambdaQueryWrapper<OrderCommentsCategoriesRelPO>().in(OrderCommentsCategoriesRelPO::getCommentId, orderCommentsInfo.getOrderCommentsId()));
        if (CollUtil.isEmpty(orderCommentsCategoriesRelPOS)) return;

        List<Integer> problemCategoriesIds = orderCommentsCategoriesRelPOS.stream()
                .map(OrderCommentsCategoriesRelPO::getCategoryId)
                .distinct()
                .collect(Collectors.toList());

        List<OrderCommentsProblemCategoriesDTO> categoriesPOList = orderCommentsProblemCategoriesDAO.selectJoinList(
                OrderCommentsProblemCategoriesDTO.class,
                new MPJLambdaWrapper<OrderCommentsProblemCategoriesPO>()
                        .leftJoin(OrderCommentsProblemCategoriesPO.class, "p", OrderCommentsProblemCategoriesPO::getId, OrderCommentsProblemCategoriesPO::getParentId)
                        .selectAs(OrderCommentsProblemCategoriesPO::getId, OrderCommentsProblemCategoriesDTO::getChildId)
                        .selectAs(OrderCommentsProblemCategoriesPO::getName, OrderCommentsProblemCategoriesDTO::getChildName)
                        .selectAs("p", OrderCommentsProblemCategoriesPO::getId, OrderCommentsProblemCategoriesDTO::getParentId)
                        .selectAs("p", OrderCommentsProblemCategoriesPO::getName, OrderCommentsProblemCategoriesDTO::getParentName)
                        .in(OrderCommentsProblemCategoriesPO::getId, problemCategoriesIds)
        );
        if (categoriesPOList.isEmpty()) return;

        orderCommentsInfo.setCategories(categoriesPOList);
    }

    /**
     * 获取订单评论详情
     *
     * @param orderCommentsId 订单评论id
     * @return OrderCommentsDetailResponseVO
     */
    private OrderCommentsDetailResponseVO getOrderCommentsInfo(String orderCommentsId) {
        logger.info("get order comments info.");

        // 设置查询的条件以及字段
        MPJLambdaWrapper<OrderCommentsPO> queryWrapper = OrderUtil.setOrderCommentsSelectAs()
                .selectAs(OrderCommentsPO::getComment, OrderCommentsDetailResponseVO::getComment)
                .selectAs(OrderCommentsPO::getImageUrl, OrderCommentsDetailResponseVO::getImageUrl)
                .selectAs(OrderCommentsPO::getBonusPoints, OrderCommentsDetailResponseVO::getBonusPoints)
                .selectAs(OrderCommentsPO::getReply, OrderCommentsDetailResponseVO::getReply)
                .selectAs(OrderCommentsPO::getRepliedDate, OrderCommentsDetailResponseVO::getRepliedDate)
                .selectAs(OrderCommentsPO::getReplyPeople, OrderCommentsDetailResponseVO::getReplyPeople)
//                .selectAs(OrderCommentsPO::getCompanyName, OrderCommentsDetailResponseVO::getCompanyName)
//                .selectAs(OrderCommentsPO::getCompanyCode, OrderCommentsDetailResponseVO::getCompanyCode)
//                .selectAs(OrderCommentsPO::getRemark, OrderCommentsDetailResponseVO::getRemark)
                .eq(OrderCommentsPO::getId, orderCommentsId);

        // 获取订单评论详情
        return orderCommentsMapper.selectJoinOne(OrderCommentsDetailResponseVO.class, queryWrapper);
    }

}
