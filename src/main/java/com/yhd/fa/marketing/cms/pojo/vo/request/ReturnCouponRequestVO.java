package com.yhd.fa.marketing.cms.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: ReturnCouponRequestVO.java, v 0.1 2024/8/8 16:30 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ReturnCouponRequestVO extends BaseVO {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 用户优惠券id
     */
    private String userCouponId;
}
