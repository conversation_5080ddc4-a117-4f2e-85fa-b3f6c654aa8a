<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
    <modelVersion>4.0.0</modelVersion>  
    <groupId>com.yhd</groupId>  
    <artifactId>yhd-service-fa-marketing-cms</artifactId>  
    <version>1.0.0</version>  
    <name>yhd-service-fa-marketing-cms</name>  
    <description>yhd-service-fa-marketing-cmsFA商城营销系统CMS</description>  
    <parent> 
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63051591-->  
        <groupId>com.yhd.springcloud</groupId>  
        <artifactId>yhd-spring-cloud-parent</artifactId>  
        <version>*******</version> 
    </parent>  
    <properties> 
        <maven.compiler.source>8</maven.compiler.source>  
        <maven.compiler.target>8</maven.compiler.target>  
        <sonar.projectKey>yhd-service-fa_yhd-service-fa-marketing-cms_AZY4dHfCH1qEGAFn9HAA</sonar.projectKey>  
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>  
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>  
        <sonar.branch.name>master</sonar.branch.name> 
    </properties>  
    <dependencies> 
        <!-- Excel操作 很坑需要pom 过滤xlsx、xls 看下面plugin 原因是因为打包时将模板文件损坏了 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-easyexcel-starter</artifactId> 
        </dependency>  
        <!-- Excel操作 -->  
        <dependency> 
            <groupId>cn.afterturn</groupId>  
            <artifactId>easypoi-spring-boot-starter</artifactId>  
            <version>4.4.0</version> 
        </dependency>  
        <!-- 用户中心 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-ucs-sdk-alibaba</artifactId>  
            <version>4.0.9</version>
        </dependency>  
        <!-- 电商的用户中心 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>buc-cms-api-sdk</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>  
        <!--日志-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-service-cms-log-common</artifactId>  
            <version>2.0.2</version> 
        </dependency>  
        <!-- nacos config  -->  
        <dependency> 
            <groupId>com.alibaba.cloud</groupId>  
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>  
            <exclusions> 
                <exclusion> 
                    <artifactId>checker-qual</artifactId>  
                    <groupId>org.checkerframework</groupId> 
                </exclusion> 
            </exclusions> 
        </dependency>  
        <!-- nacos discovery  -->  
        <dependency> 
            <groupId>com.alibaba.cloud</groupId>  
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId> 
        </dependency>  
        <!--web 模块-->  
        <dependency> 
            <groupId>org.springframework.boot</groupId>  
            <artifactId>spring-boot-starter-web</artifactId>  
            <exclusions> 
                <exclusion> 
                    <artifactId>spring-boot-starter</artifactId>  
                    <groupId>org.springframework.boot</groupId> 
                </exclusion> 
            </exclusions> 
        </dependency>  
        <!--undertow容器-->  
        <dependency> 
            <groupId>org.springframework.boot</groupId>  
            <artifactId>spring-boot-starter-undertow</artifactId> 
        </dependency>  
        <!-- 数据库连接及多租户 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052099-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-database-starter</artifactId> 
        </dependency>  
        <dependency> 
            <groupId>mysql</groupId>  
            <artifactId>mysql-connector-java</artifactId> 
        </dependency>  
        <!-- 操作日志 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052465-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-log-starter</artifactId> 
        </dependency>  
        <!-- redis -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052515-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-redis-starter</artifactId>  
            <exclusions> 
                <exclusion> 
                    <artifactId>spring-boot-starter</artifactId>  
                    <groupId>org.springframework.boot</groupId> 
                </exclusion> 
            </exclusions> 
        </dependency>  
        <!-- 分布式锁 及幂等性处理  -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052304-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-lock-starter</artifactId> 
        </dependency>  
        <!-- validator 参数校验 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-validator-starter</artifactId> 
        </dependency>  
        <!-- xxs攻击过滤  -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-xss-starter</artifactId> 
        </dependency>  
        <!-- feign 依赖 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052267-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-feign-starter</artifactId> 
        </dependency>  
        <!-- sentinel 依赖 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-sentinel-starter</artifactId> 
        </dependency>  
        <!-- springdoc 依赖 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052621-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-springdoc-starter</artifactId> 
        </dependency>  
        <!-- 灰度路由 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052282-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-gray-starter</artifactId> 
        </dependency>  
        <!-- skywalking -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-skywalking-starter</artifactId>  
            <version>3.0.4</version> 
        </dependency>  
        <!-- seata -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-seata-starter</artifactId> 
        </dependency>  
        <dependency> 
            <groupId>com.github.yulichang</groupId>  
            <artifactId>mybatis-plus-join-boot-starter</artifactId>  
            <version>1.5.2</version> 
        </dependency>  
        <!--xxl-job-->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052285-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-job-starter</artifactId> 
        </dependency>  
        <!--二方包-->  
        <!--第三方jar-->  
        <!-- 分页 -->  
        <dependency> 
            <groupId>com.github.pagehelper</groupId>  
            <artifactId>pagehelper-spring-boot-starter</artifactId> 
        </dependency>  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-rocketmq-starter</artifactId> 
        </dependency>  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-dynamic-database</artifactId> 
        </dependency>  
        <!-- https://mvnrepository.com/artifact/com.clickhouse/clickhouse-jdbc -->  
        <dependency> 
            <groupId>com.clickhouse</groupId>  
            <artifactId>clickhouse-jdbc</artifactId>  
            <version>0.4.2</version> 
        </dependency>  
        <dependency> 
            <groupId>net.jpountz.lz4</groupId>  
            <artifactId>lz4</artifactId>  
            <version>1.3.0</version> 
        </dependency>  
        <!-- 文件上传sdk -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-service-united-file</artifactId>  
            <version>2.0.2</version> 
        </dependency> 
    </dependencies>  
    <build> 
        <plugins> 
            <!--springboot打包插件导入先于assembly插件，便于打包-->  
            <plugin> 
                <groupId>org.springframework.boot</groupId>  
                <artifactId>spring-boot-maven-plugin</artifactId> 
            </plugin>  
            <plugin> 
                <groupId>org.apache.maven.plugins</groupId>  
                <artifactId>maven-resources-plugin</artifactId>  
                <configuration> 
                    <nonFilteredFileExtensions> 
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>  
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>  
                        <nonFilteredFileExtension>ttc</nonFilteredFileExtension> 
                    </nonFilteredFileExtensions> 
                </configuration> 
            </plugin>  
            <plugin> 
                <groupId>org.apache.maven.plugins</groupId>  
                <artifactId>maven-assembly-plugin</artifactId>  
                <version>3.3.0</version>  
                <executions> 
                    <execution> 
                        <configuration> 
                            <appendAssemblyId>false</appendAssemblyId>  
                            <descriptors> 
                                <descriptor>src/main/assembly/assembly.xml</descriptor> 
                            </descriptors> 
                        </configuration>  
                        <id>assemblyId</id>  
                        <phase>package</phase>  
                        <goals> 
                            <goal>single</goal> 
                        </goals> 
                    </execution> 
                </executions> 
            </plugin> 
        </plugins> 
    </build>  
    <distributionManagement> 
        <repository> 
            <id>releases</id>  
            <url>http://nexus.dgyiheda.com/repository/releases/</url> 
        </repository>  
        <snapshotRepository> 
            <id>snapshots</id>  
            <name>snapshots</name>  
            <url>http://nexus.dgyiheda.com/repository/snapshots/</url> 
        </snapshotRepository> 
    </distributionManagement> 
</project>
